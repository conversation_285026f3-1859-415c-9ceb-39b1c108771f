import { useState } from "react";
import { useGetCandidatesQuery } from "../../app-state/apis";
import { useUserDetails } from "../../app-state/context/UserDetailsProvider";
import { PageContainer, PageLayout, LoadingScreen } from "@eva/shared-ui";
import { CandidatesDataTable } from "./sub-components/CandidatesDataTable";
import { CandidatesDataTableFilter, CandidatesFilterFields } from "./sub-components/CandidatesDataTableFilter";

const CandidatesPage = () => {
    const { customerId } = useUserDetails();
    const { data, isLoading } = useGetCandidatesQuery(customerId ?? "", {
        skip: !customerId,
    });

    const [filterFields, setFilterFields] = useState<CandidatesFilterFields>({
        searchText: "",
    });

    const handleFilterChange = (filterFields: CandidatesFilterFields) => {
        setFilterFields(filterFields);
    };

    const filteredCandidates = data
        ? data.candidates.filter((candidate) => {
              return (
                  candidate.name.toLowerCase().includes(filterFields.searchText.toLowerCase()) ||
                  candidate.jobTitle.toLowerCase().includes(filterFields.searchText.toLowerCase())
              );
          })
        : [];

    return (
        <PageContainer>
            {isLoading ? (
                <LoadingScreen />
            ) : (
                <PageLayout transparent>
                    <CandidatesDataTableFilter
                        filterFields={filterFields}
                        handleFilterChange={handleFilterChange}
                        resultsCount={filteredCandidates.length}
                    />
                    <CandidatesDataTable candidates={filteredCandidates} shouldShowJobTitle />
                </PageLayout>
            )}
        </PageContainer>
    );
};

export default CandidatesPage;
