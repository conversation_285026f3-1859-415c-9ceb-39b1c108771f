import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../../../PDF.styles";
import { FamilySVGIcon } from "../../../icons/FamilySVGIcon";
import { FinalScoreSectionPerformanceTableCell } from "./FinalScoreSectionPerformanceTableCell";
import { FinalScoreSectionPerformanceTableHeader } from "./FinalScoreSectionPerformanceTableHeader";
import { Family } from "@eva/shared-common";

interface FinalScoreSectionPerformanceTableProps {
    families: Family[];
    familiesMapForDisplay: { [key: string]: any };
    language: string;
    isRtl: boolean;
}

const determineLeftRoundedCorners = (idx: number, length: number, isRtl: boolean) => {
    if (idx === 0) {
        return isRtl ? styles.rightTopRounded : styles.leftTopRounded;
    } else if (idx === length - 1) {
        return isRtl ? styles.rightBottomRounded : styles.leftBottomRounded;
    } else {
        return {};
    }
};

const determineRightRoundedCorners = (idx: number, length: number, isRtl: boolean) => {
    if (idx === 0) {
        return {};
    } else if (idx === length - 1) {
        return isRtl ? styles.leftBottomRounded : styles.rightBottomRounded;
    } else {
        return {};
    }
};

export const FinalScoreSectionPerformanceTable: FC<FinalScoreSectionPerformanceTableProps> = ({
    familiesMapForDisplay,
    language,
    families,
    isRtl,
}) => {
    return (
        <View style={[styles.displayFlex]}>
            <FinalScoreSectionPerformanceTableHeader isRtl={isRtl} />
            {families.map((family, idx) => (
                <View
                    key={family.code}
                    style={[styles.displayFlex, styles.fullWidth, styles.flewDirectionRow, isRtl ? styles.flexReverse :{}]}
                >
                    <View
                        style={[
                            styles.border,
                            styles.displayFlex,
                            styles.flewDirectionRow,
                            styles.alignItemsCenter,
                            styles.flexGap6,
                            determineLeftRoundedCorners(idx, families.length, isRtl),
                            styles.paddingHorizonta10,
                            { width: "31.25%", height: 30 },
                            isRtl && styles.flexReverse,
                        ]}
                    >
                        <FamilySVGIcon size='12px' familyCode={family.code} />
                        <Text style={[isRtl ? styles.hebrewTextProgressBar : styles.textProgressBar]}>
                            {familiesMapForDisplay[family.code].family[language]}
                        </Text>
                    </View>
                    <FinalScoreSectionPerformanceTableCell score={family.score} segment={1} isRtl={isRtl} />
                    <FinalScoreSectionPerformanceTableCell score={family.score} segment={2} isRtl={isRtl} />
                    <FinalScoreSectionPerformanceTableCell score={family.score} segment={3} isRtl={isRtl} />
                    <FinalScoreSectionPerformanceTableCell score={family.score} segment={4} isRtl={isRtl} />
                    <FinalScoreSectionPerformanceTableCell score={family.score} segment={5} isRtl={isRtl} />
                    <View
                        style={[
                            styles.border,
                            styles.displayFlex,
                            styles.alignItemsCenter,
                            styles.justifyCenter,
                            determineRightRoundedCorners(idx, families.length, isRtl),
                            { width: "6.25%", height: 30 },
                        ]}
                    >
                        <Text style={styles.textBoldScore}>{family.score.toFixed(1)}</Text>
                    </View>
                </View>
            ))}
        </View>
    );
};
