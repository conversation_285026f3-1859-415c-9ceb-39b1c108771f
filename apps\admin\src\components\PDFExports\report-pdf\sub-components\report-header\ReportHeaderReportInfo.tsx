import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";

interface ReportHeaderReportInfoProps {
    propertyValue: string;
    propertyName: string;
    isRtl: boolean;
    border: "left" | "right" | "none" | "both";
    paddingSide: "left" | "right" | "none" | "both";
}

const determineBorder = (border: "left" | "right" | "none" | "both") => {
    switch (border) {
        case "left":
            return styles.borderLeft;
        case "right":
            return styles.borderRight;
        case "both":
            return [styles.borderLeft, styles.borderRight];
        default:
            return {};
    }
};

const determinePadding = (paddingSide: "left" | "right" | "none" | "both") => {
    switch (paddingSide) {
        case "left":
            return styles.paddingLeft6;
        case "right":
            return styles.paddingRight6;
        case "both":
            return [styles.paddingLeft6, styles.paddingRight6];
        default:
            return {};
    }
};

export const ReportHeaderReportInfo: FC<ReportHeaderReportInfoProps> = ({ paddingSide, border, isRtl, propertyName, propertyValue }) => {
    return (
        <View
            style={[
                styles.flewDirectionRow,
                styles.displayFlex,
                styles.flexGap2,
                styles.alignItemsCenter,
                isRtl ? styles.flexReverse : {},
                determineBorder(border),
                determinePadding(paddingSide),
            ]}
        >
            <Text style={isRtl ? styles.hebrewTextBoldSubTitle : styles.textBoldSubTitle}>{propertyName}</Text>
            <Text style={isRtl ? styles.hebrewTextBoldSubTitle : styles.textBoldSubTitle}>:</Text>
            <Text style={isRtl ? styles.hebrewTextSubTitle : styles.textSubTitle}>{propertyValue}</Text>
        </View>
    );
};
