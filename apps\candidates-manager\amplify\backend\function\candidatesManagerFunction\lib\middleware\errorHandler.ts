import { NextFunction, Request, Response } from "express";
import { AppError } from "@eva/shared-common";
import { Logger } from "@eva/logger";

export const errorHandler = (logger: Logger) => (err: any, req: Request, res: Response, next: NextFunction) => {
    logger.error("An error occurred", {
        message: err.message,
        stack: err.stack,
    });

    let statusCode = 500;

    if (err instanceof AppError) {
        statusCode = err.statusCode;
    }

    res.status(statusCode).json({
        error: {
            message: err.message,
            stack: process.env.NODE_ENV === "prod" ? undefined : err.stack,
        },
    });
}; 