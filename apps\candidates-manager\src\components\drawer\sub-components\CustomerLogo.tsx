import { Box, CircularProgress } from "@mui/material";
import { styled } from "@mui/material";
import { useGetCustomerLogoQuery } from "../../../app-state/apis";
import { useUserDetails } from "../../../app-state/context/UserDetailsProvider";
import Logo from "./../logo.png";

const LogoContainer = styled(Box)({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: "16px 0",
});
export const CustomerLogo = () => {
    const { customerId } = useUserDetails();
    const { data: customerLogo, isLoading } = useGetCustomerLogoQuery(customerId ?? "", {
        skip: !customerId,
    });
    return (
        <LogoContainer>
            {isLoading ? (
                <CircularProgress />
            ) : (
                <img src={customerLogo?.logoLink ?? Logo} alt="Logo" style={{ maxWidth: "80%", height: "60px" }} />
            )}
        </LogoContainer>
    );
};
