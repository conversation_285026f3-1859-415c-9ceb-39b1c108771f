import React from 'react';
import { CenteredModal } from '@eva/shared-ui';
import { EditInvitationForm } from './EditInvitationForm';

interface EditInvitationModalProps {
    isOpen: boolean;
    onClose: () => void;
    jobId: string;
}

export const EditInvitationModal: React.FC<EditInvitationModalProps> = ({ 
    isOpen, 
    onClose, 
    jobId
}) => {
    const handleSuccess = () => {
        onClose();
    };

    return (
        <CenteredModal
            isOpen={isOpen}
            onClose={onClose}
            width={650}
            height="auto"
        >
            <EditInvitationForm 
                jobId={jobId}
                onSuccess={handleSuccess}
                onCancel={onClose}
            />
        </CenteredModal>
    );
}; 