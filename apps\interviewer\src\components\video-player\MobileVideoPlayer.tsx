import { useEffect, useLayoutEffect, useRef } from "react";
import { MobileStyledVideo } from "./VideoPlayer.styles";
import { VideoPlayerProps } from "./VideoPlayer";

interface MobileVideoPlayerProps extends VideoPlayerProps {
    width?: string;
    height?: string;
}

export const MobileVideoPlayer = ({
    controls,
    videoUrl,
    "data-qa": dataQa,
    onVideoEnd,
    onVideoStart,
    hidden,
    autoPlayDisabled,
    width = "100%",
    height = "100%",
}: MobileVideoPlayerProps) => {
    const videoRef = useRef<HTMLVideoElement>(null);

    useLayoutEffect(() => {
        if (videoRef.current) {
            videoRef.current.src = videoUrl;
            videoRef.current.load();
        }
    }, [videoUrl]);

    const handleVideoEnd = () => {
        if (onVideoEnd) onVideoEnd();
    };

    useEffect(() => {
        const videoElement = videoRef.current;

        if (videoElement) {
            videoElement.addEventListener("ended", handleVideoEnd);
        }

        return () => {
            if (videoElement) {
                videoElement.removeEventListener("ended", handleVideoEnd);
            }
        };
    }, [onVideoEnd, onVideoStart, videoUrl]);

    return (
        <MobileStyledVideo
            hidden={hidden}
            controlsList="nodownload"
            data-qa={dataQa}
            ref={videoRef}
            controls={true}
            playsInline
            autoPlay={autoPlayDisabled ? false : true}
            width={width}
            height={height}
        />
    );
};
