import React from "react";
import { Alert, AlertTitle } from "@mui/material";
import { Body2 } from "@eva/shared-ui";

export interface ValidationSummaryProps {
    totalCount: number;
    validCount: number;
    invalidCount: number;
}

export const ValidationSummary: React.FC<ValidationSummaryProps> = ({ 
    totalCount, 
    validCount, 
    invalidCount 
}) => (
    <Alert severity={invalidCount > 0 ? "warning" : "success"}>
        <AlertTitle>Validation Results</AlertTitle>
        <Body2>Total candidates: {totalCount}</Body2>
        <Body2>Valid candidates: {validCount}</Body2>
        {invalidCount > 0 && (
            <Body2>
                Invalid candidates: {invalidCount} - Please fix the errors below
            </Body2>
        )}
    </Alert>
); 