import { ReportVersion } from "./AnalyzerReportData";
import { FamiliesWithTraitsForDisplayMap } from "./FamiliesWithTraitsForDisplayMap";

export interface InterviewData {
    accountId: string;
    interviewId: string;
    customer: string;
    jobTitle: string;
    candidateName: string;
    email: string | null;
    socialId: string | null;
    language: string;
    interviewDate: string;
    candidateImage: string | null;
    companyName: string;
}


export interface InterviewResponse {
    interview: InterviewData;
    report: ReportVersion;
    originalReport?: ReportVersion;
    reportEditSession?: ReportVersion;
    familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap;
}
