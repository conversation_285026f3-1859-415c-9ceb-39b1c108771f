import { UploadVideoRequest } from "@eva/shared-common";
import { capitalize, CircularProgress, Stack } from "@mui/material";
import { uploadData } from "aws-amplify/storage";
import { useField, useFormikContext } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { Body2, FileInput, generateUniqueString, PrimaryButton } from "@eva/shared-ui";
import { useLazyValidateVideoQuery } from "../../../app-state/apis";

interface VideoUploadInputProps {
    isLoading: boolean;
}

export const VideoUploadInput: React.FC<VideoUploadInputProps> = ({ isLoading }) => {
    const [videoFile, setVideoFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [validateVideo] = useLazyValidateVideoQuery();
    const { submitForm, values, errors, setErrors, resetForm } = useFormikContext<UploadVideoRequest>();
    const [, , { setValue: setKeyValue }] = useField("key");

    useEffect(() => {
        const errorMessages = Object.values(errors).filter((error) => error !== "");

        errorMessages.forEach((error) => {
            toast.error(error);
        });
        setErrors({});
    }, [errors]);

    const inputRef = useRef<HTMLInputElement>(null);

    const determineIfUploadDisabled = () => {
        if (values.type === "") return true;
        if (values.type === "intro") return !videoFile || isLoading || values.customerId === "" || values.language === "" || isUploading;
        if (values.type === "question")
            return !videoFile || isLoading || values.customerId === "" || values.language === "" || values.questionId === "" || isUploading;
        return true;
    };

    const handleUpload = async () => {
        const videoKey = `public/uploads/${values.type}s/${values.customerId}-${values.language}-${
            values.questionId ? values.questionId : "intro"
        }-${generateUniqueString(12)}.mp4`;

        setIsUploading(true);
        setKeyValue(videoKey);

        const { isValid } = await validateVideo(values).unwrap();
        if (!isValid) {
            setIsUploading(false);
            setKeyValue("");
            toast.error(`Video for this ${capitalize(values.type)} is already has been uploaded`);
            return;
        }

        if (videoFile && isValid) {
            try {
                const uploadTask = await uploadData({
                    path: videoKey,
                    data: videoFile,
                    options: {
                        contentType: "video/mp4",
                        onProgress: (progress) => {
                            if (progress.transferredBytes && progress.totalBytes) {
                                setUploadProgress((progress.transferredBytes / progress.totalBytes) * 100);
                            } else {
                                setUploadProgress(0);
                            }
                        },
                    },
                }).result;
                await submitForm();
                resetForm();
                setIsUploading(false);
                setVideoFile(null);
                if (inputRef.current) {
                    inputRef.current.value = "";
                }
            } catch (error) {
                console.error("Upload failed:", error);
                setIsUploading(false);
            }
        }
    };

    const uploadButtonContent = isUploading || isLoading ? String(uploadProgress.toFixed(0)) + "%" : "Upload Video";
    const isUploadDisabled = determineIfUploadDisabled();

    return (
        <Stack direction='row' gap={3}>
            <FileInput onFileSelect={setVideoFile} buttonLabel='Choose Video' disabled={isUploading} />
            <PrimaryButton variant='outlined' content={uploadButtonContent} onClick={handleUpload} disabled={isUploadDisabled} />
            {videoFile && (
                <Stack justifyContent='center' padding={2}>
                    <Body2>Selected file: {videoFile.name}</Body2>
                </Stack>
            )}
        </Stack>
    );
};
