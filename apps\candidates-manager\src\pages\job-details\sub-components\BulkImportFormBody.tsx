import React, { useRef, useState } from "react";
import { useFormikContext } from "formik";
import { Stack, LinearProgress, Alert, AlertTitle } from "@mui/material";
import { H3, Body2} from "@eva/shared-ui";
import { ParsedCandidate } from "./types/BulkImportTypes";
import { parseExcelFile, createExcelTemplate } from "./ExcelParser";
import { ImportActionButtons } from "./ImportActionButtons";
import { ValidationSummary } from "./ValidationSummary";
import { CandidatesTable } from "./CandidatesTable";
import { FooterButtons } from "./FooterButtons";

interface BulkImportFormValues {
    candidates: ParsedCandidate[];
    file: File | null;
}

interface BulkImportFormBodyProps {
    isHebrew: boolean;
    onCancel: () => void;
    isCreating: boolean;
    checkDuplicateCandidate: (email: string) => Promise<boolean>;
    validateEmail: (email: string) => Promise<{ isValid: boolean; message?: string }>;
    jobId: string;
}

export const BulkImportFormBody: React.FC<BulkImportFormBodyProps> = ({
    isHebrew,
    onCancel,
    isCreating,
    checkDuplicateCandidate,
    validateEmail,
    jobId,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [parseError, setParseError] = useState<string | null>(null);
    const { values, setFieldValue, validateForm, isSubmitting, submitForm } = useFormikContext<BulkImportFormValues>();

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files.length > 0) {
            const file = event.target.files[0];
            setFieldValue("file", file);
            setParseError(null);
            setIsLoading(true);

            parseExcelFile(
                file,
                isHebrew,
                (parsedCandidates) => {
                    setFieldValue("candidates", parsedCandidates);
                    validateForm();
                    setIsLoading(false);
                },
                (error) => {
                    setParseError(error);
                    setIsLoading(false);
                },
                setIsLoading,
                jobId,
                checkDuplicateCandidate,
                validateEmail
            );
        }
    };

    const handleDownloadTemplate = () => {
        createExcelTemplate(isHebrew);
    };

    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUpload = () => {
        fileInputRef.current?.click();
    };

    const getValidCandidatesCount = () => values.candidates.filter((c) => c.isValid && !c.isDuplicate).length;
    const getInvalidCandidatesCount = () => values.candidates.filter((c) => !c.isValid || c.isDuplicate).length;

    return (
        <Stack spacing={3}>
            <H3>Bulk Import Candidates</H3>

            <ImportActionButtons
                onDownloadTemplate={handleDownloadTemplate}
                onUploadClick={handleFileUpload}
                isLoading={isLoading}
                isCreating={isCreating}
            />
            <input
                id="file-upload"
                type="file"
                accept=".xlsx,.xls"
                hidden
                onChange={handleFileChange}
                ref={fileInputRef}
            />

            {values.file && <Body2>Selected file: {values.file.name}</Body2>}

            {isLoading && <LinearProgress />}

            {parseError && (
                <Alert severity="error">
                    <AlertTitle>Error</AlertTitle>
                    {parseError}
                </Alert>
            )}

            {values.candidates.length > 0 && (
                <>
                    <ValidationSummary
                        totalCount={values.candidates.length}
                        validCount={getValidCandidatesCount()}
                        invalidCount={getInvalidCandidatesCount()}
                    />

                    <CandidatesTable candidates={values.candidates} isHebrew={isHebrew} />
                </>
            )}

            <FooterButtons
                onCancel={onCancel}
                onImport={submitForm}
                validCount={getValidCandidatesCount()}
                isDisabled={
                    values.candidates.length === 0 ||
                    getValidCandidatesCount() === 0 ||
                    isLoading ||
                    isCreating ||
                    isSubmitting
                }
                isCreating={isCreating}
            />
        </Stack>
    );
};
