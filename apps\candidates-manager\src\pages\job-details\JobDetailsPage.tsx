import { <PERSON><PERSON>r<PERSON><PERSON>, PageContainer, PageLayout } from "@eva/shared-ui";
import { usePageTitle } from "@eva/shared-ui";
import { Stack } from "@mui/material";
import { Suspense, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useGetCandidatesByJobIdQuery, useGetJobByIdQuery } from "../../app-state/apis";
import { CandidatesSection } from "./sub-components/CandidatesSection";
import { JobDetails } from "./sub-components/JobDetails";
import { JobDetailsHeader } from "./sub-components/JobDetailsHeader";
import {
    HeaderSkeleton
} from "./sub-components/JobDetailsSkeleton";
import { JobModals } from "./sub-components/JobModals";
const JobDetailsPage = () => {
    const { jobId } = useParams<{ jobId: string }>();
    const { setSubtitle } = usePageTitle();
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isBulkImportModalOpen, setIsBulkImportModalOpen] = useState(false);
    const [isInvitationModalOpen, setIsInvitationModalOpen] = useState(false);

    const {
        data: job,
        isLoading: isJobLoading,
        refetch: refetchJob,
    } = useGetJobByIdQuery(jobId ?? "", {
        skip: !jobId,
    });

    const {
        data: candidatesData,
        isLoading: isCandidatesLoading,
        refetch: refetchCandidates,
    } = useGetCandidatesByJobIdQuery(jobId ?? "", {
        skip: !jobId,
    });

    useEffect(() => {
        if (job?.title) {
            setSubtitle(job.title);
        }

        return () => {
            setSubtitle(null);
        };
    }, [job?.title, setSubtitle]);

    if (!jobId) {
        return <ErrorPage message="Something went wrong" onRetry={refetchJob} />;
    }

    const isHebrew = job?.language === "he";

    return (
        <PageContainer>
            <PageLayout transparent>
                <Stack spacing={3}>
                    <Suspense fallback={<HeaderSkeleton />}>
                        <JobDetailsHeader
                            onOpenInvitation={() => setIsInvitationModalOpen(true)}
                            onOpenBulkImport={() => setIsBulkImportModalOpen(true)}
                            onOpenAddCandidate={() => setIsAddModalOpen(true)}
                        />
                    </Suspense>

                    <JobDetails jobId={jobId} />

                    <CandidatesSection
                        inviteDisabled={job?.status !== "ready"}
                        jobId={jobId}
                        candidates={candidatesData?.candidates || []}
                        isLoading={isCandidatesLoading}
                    />
                </Stack>
            </PageLayout>

            {job && (
                <JobModals
                    jobId={jobId}
                    isHebrew={isHebrew}
                    isAddModalOpen={isAddModalOpen}
                    isBulkImportModalOpen={isBulkImportModalOpen}
                    isInvitationModalOpen={isInvitationModalOpen}
                    onCloseAdd={() => setIsAddModalOpen(false)}
                    onCloseBulkImport={() => setIsBulkImportModalOpen(false)}
                    onCloseInvitation={() => setIsInvitationModalOpen(false)}
                    onBulkImportSuccess={refetchCandidates}
                />
            )}
        </PageContainer>
    );
};

export default JobDetailsPage;
