import { Stack } from "@mui/material";
import { NoCopyWrapper, Body2, useAppText, Language } from "@eva/shared-ui";

const hebrewExplanation = `במסך הבא יוצג לפניך סיפור מקרה ולאחריו תתבקש/י
                            לענות על מספר שאלות בהתאם למסופר בסיפור
                            המקרה.
                            ניתן יהיה לצפות שוב בסיפור המקרה לפני כל שאלה.`;

const explanationsMap: Record<Language, string> = {
    he: hebrewExplanation,
    en: "In the next screen, you will see a case story and then you will be asked a number of questions based on the story.",
};

export const SimulationStepExplanation = () => {
    const { currentLanguage } = useAppText();
    return (
        <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
            <NoCopyWrapper>
                <Body2>{explanationsMap[currentLanguage]}</Body2>
            </NoCopyWrapper>
        </Stack>
    );
};
