import { Stack } from "@mui/material";
import { useAppText, useMediaQueryContext } from "@eva/shared-ui";
import { InterviewerPrimaryButton } from "../../../../../../components/buttons/InterviewerPrimaryButton";
import { useInterviewer } from "../../../../InterviewerProvider";
import { SetupCheck } from "./sub-components/SetupCheck";

export const SetupState = () => {
    const {
        hasSetup,
        handleInterviewStart,
        handleSetupChange,
        handleInterviewStartTraining,
        isWelcomeVideoDone,
        isTrainingOn,
    } = useInterviewer();
    const { isMobile } = useMediaQueryContext();
    const { getContentForDisplay } = useAppText();
    const startInterviewText = getContentForDisplay("interviewer.setupState.startInterview");
    const startTrainingText = getContentForDisplay("interviewer.setupState.training");

    const isDisabled = !hasSetup || !isWelcomeVideoDone 

    return (
        <Stack
            data-qa="setup-state-container"
            alignItems="center"
            justifyContent="center"
            direction={isMobile ? "column" : "row"}
            gap={isMobile ? "24px" : "16px"}
            width="100%"
            paddingInline={isMobile ? 20 : 10}
            paddingTop={isMobile ? 0 : 10}
        >
            <SetupCheck changeConnectionState={handleSetupChange} />
            <Stack gap="16px">
                <InterviewerPrimaryButton
                    variant="outlined"
                    data-qa="start-training-button"
                    disabled={!hasSetup || !isWelcomeVideoDone}
                    onClick={handleInterviewStartTraining}
                    content={startTrainingText}
                />

                <InterviewerPrimaryButton
                    data-qa="start-interview-button"
                    disabled={isDisabled}
                    onClick={handleInterviewStart}
                    content={startInterviewText}
                />
            </Stack>
        </Stack>
    );
};
