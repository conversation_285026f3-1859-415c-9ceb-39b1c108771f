import { Theme } from "@mui/material/styles";
import { Typography } from "@mui/material";
import React from "react";
import { useAppTheme } from "./AppThemeProvider";

export type TextAlign = "left" | "center" | "right" | "justify";
export type TextDirection = "ltr" | "rtl";

export const typography: Theme["typography"] = {
    fontFamily: "Poppins, sans-serif",
    fontSize: 14,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
    h1: {
        fontSize: "2rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    h2: {
        fontSize: "1.5rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    button: {
        fontSize: "1rem",
        fontWeight: 500,
        lineHeight: 1.2,
    },
    caption: {
        fontSize: "0.8rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    h3: {
        fontSize: "1.2rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    h4: {
        fontSize: "1rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    h5: {
        fontSize: "0.9rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    h6: {
        fontSize: "0.8rem",
        fontWeight: 700,
        lineHeight: 1.2,
    },
    subtitle1: {
        fontSize: "1rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    subtitle2: {
        fontSize: "0.9rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    body1: {
        fontSize: "1rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    body2: {
        fontSize: "0.9rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    overline: {
        fontSize: "0.8rem",
        fontWeight: 400,
        lineHeight: 1.2,
    },
    fontWeightLight: undefined,
    htmlFontSize: 0,
    pxToRem: (px: number) => `${px / 16}rem`,
};

interface TypographyProps {
    children: React.ReactNode;
    color?: "primary" | "secondary" | "textPrimary" | "textSecondary" | "error" | "success" | "inherit" | "white";
    "data-qa"?: string;
    textAlign?: TextAlign;
    dir?: TextDirection;
}

export const H1 = ({ children, color = "textPrimary", "data-qa": dataQa, textAlign, dir }: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography textAlign={textAlign} data-qa={dataQa} variant="h1" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const H2 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography textAlign={textAlign} data-qa={dataQa} variant="h2" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Button = ({ 
    children, 
    color = "textPrimary", 
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="button" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Caption = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="caption" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const H3 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="h3" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const H4 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="h4" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const H5 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="h5" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const H6 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="h6" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Subtitle1 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="subtitle1" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Subtitle2 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="subtitle2" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Body1 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="body1" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Body2 = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography data-qa={dataQa} textAlign={textAlign} variant="body2" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

export const Overline = ({
    children,
    color = "textPrimary",
    "data-qa": dataQa,
    textAlign,
    dir
}: TypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography textAlign={textAlign} data-qa={dataQa} variant="overline" color={color} dir={direction}>
            {children}
        </Typography>
    );
};

interface ClickableTypographyProps extends TypographyProps {
    href: string; // URL is required to make text clickable
}

export const ClickableBody1 = ({
    children,
    "data-qa": dataQa,
    href,
    textAlign,
    dir
}: ClickableTypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography
            component="a"
            href={href}
            data-qa={dataQa}
            textAlign={textAlign}
            variant="body1"
            color="primary"
            style={{ textDecoration: "underline", cursor: "pointer" }}
            target="_blank"
            dir={direction}
        >
            {children}
        </Typography>
    );
};

export const ClickableBody2 = ({
    children,
    "data-qa": dataQa,
    href,
    textAlign,
    dir
}: ClickableTypographyProps) => {
    const { currentDirection } = useAppTheme();
    const direction = dir || currentDirection;
    return (
        <Typography
            component="a"
            href={href}
            data-qa={dataQa}
            textAlign={textAlign}
            variant="body2"
            color="primary"
            style={{ textDecoration: "underline", cursor: "pointer" }}
            target="_blank"
            dir={direction}
        >
            {children}
        </Typography>
    );
};
