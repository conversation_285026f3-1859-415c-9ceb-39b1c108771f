import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { CircularProgress, IconButton } from "@mui/material";
import { pdf } from "@react-pdf/renderer";
import { FC, useState } from "react";
import { useLazyGetJobQuestionsForExportQuery } from "../app-state/apis";
import { JobQuestionsPDF } from "./PDFExports/job-questions-pdf/JobQuestionsPDF";

interface ExportJobQuestionsButtonProps {
    jobId: string;
    createdAt: string;
    jobTitle: string;
    companyName: string;
}

export const ExportJobQuestionsButton: FC<ExportJobQuestionsButtonProps> = ({ jobId, companyName, createdAt, jobTitle }) => {
    const [getJobQuestions] = useLazyGetJobQuestionsForExportQuery();
    const [loading, setLoading] = useState<boolean>(false);

    const exportJobQuestions = async () => {
        try {
            setLoading(true);
            const response = await getJobQuestions(jobId).unwrap();
            const doc = (
                <JobQuestionsPDF
                    isRtl={response.isRtl}
                    companyName={companyName}
                    createdAt={createdAt}
                    jobTitle={jobTitle}
                    questions={response.questions}
                />
            );

            const asPdf = pdf();
            asPdf.updateContainer(doc);
            const blob = await asPdf.toBlob();

            const pdfUrl = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = pdfUrl;
            link.download = `${jobTitle}-${companyName}-questions.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            setLoading(false);
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    return <IconButton onClick={exportJobQuestions}>{loading ? <CircularProgress size={22} /> : <FileDownloadIcon />}</IconButton>;
};
