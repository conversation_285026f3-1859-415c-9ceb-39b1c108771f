import { FC } from 'react';
import { AddCandidateModal } from './AddCandidateModal';
import { BulkImportCandidatesModal } from './BulkImportCandidatesModal';
import { EditInvitationModal } from './EditInvitationModal';

interface JobModalsProps {
    jobId: string;
    isHebrew: boolean;
    isAddModalOpen: boolean;
    isBulkImportModalOpen: boolean;
    isInvitationModalOpen: boolean;
    onCloseAdd: () => void;
    onCloseBulkImport: () => void;
    onCloseInvitation: () => void;
    onBulkImportSuccess: () => void;
}

export const JobModals: FC<JobModalsProps> = ({
    jobId,
    isHebrew,
    isAddModalOpen,
    isBulkImportModalOpen,
    isInvitationModalOpen,
    onCloseAdd,
    onCloseBulkImport,
    onCloseInvitation,
    onBulkImportSuccess,
}) => {
    return (
        <>
            <AddCandidateModal
                isOpen={isAddModalOpen}
                onClose={onCloseAdd}
                jobId={jobId}
                isHebrew={isHebrew}
            />
            <BulkImportCandidatesModal
                isOpen={isBulkImportModalOpen}
                onClose={onCloseBulkImport}
                jobId={jobId}
                isHebrew={isHebrew}
                onSuccess={onBulkImportSuccess}
            />
            <EditInvitationModal
                isOpen={isInvitationModalOpen}
                onClose={onCloseInvitation}
                jobId={jobId}
            />
        </>
    );
}; 