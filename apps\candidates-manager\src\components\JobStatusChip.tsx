import { JobStatus } from "@eva/shared-common";
import { Chip } from "@mui/material";
import { styled } from "@mui/material/styles";

export const StatusChip = styled(Chip)(({ theme }) => ({
    borderRadius: 16,
    fontWeight: "bold",
}));

export const JobStatusChip = ({ status }: { status: JobStatus }) => {
    const statusLabel = status === "ready" ? "Ready" : status === "pending-definition" ? "Pending Definition" : "N/A";
    const statusColor = status === "ready" ? "success" : status === "pending-definition" ? "warning" : "default";
    return <StatusChip label={statusLabel} color={statusColor} />;
};
