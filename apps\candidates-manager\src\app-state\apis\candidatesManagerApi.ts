import {
    CMBulkCreateCandidatesRequest,
    CMCandidateDetailsResponse,
    CMCandidatesTableDataResponse,
    CMCreateJobRequest,
    CMCustomerThemeResponse,
    CMInviteCandidatesRequest,
    CMJobDetailsResponse,
    CMJobsTableDataResponse,
    CMUpdateCandidateRequest,
    CMUpdateJobInvitationRequest,
    CreateCandidateRequest,
    ValidateCandidatesRequest,
    ValidateCandidatesResponse,
    ValidateEmailRequest,
    ValidateEmailResponse
} from "@eva/shared-common";
import { createApi } from "@reduxjs/toolkit/query/react";
import { del, get, patch, post } from "aws-amplify/api";
import { BaseQueryArgs } from "./types";

const baseQuery = async ({ url, method, body, headers }: BaseQueryArgs) => {
    try {
        let data;
        let status;
        switch (method) {
            case "GET":
                const restAction = get({
                    apiName: "candidatesManagerApi",
                    path: url,
                });
                const response = await restAction.response;
                data = await response.body.json();
                status = response.statusCode;
                break;
            case "POST":
                const postAction = post({
                    apiName: "candidatesManagerApi",
                    path: url,
                    options: {
                        body: body,
                    },
                });
                const postResponse = await postAction.response;
                data = await postResponse.body.json();
                status = postResponse.statusCode;
                break;
            case "DELETE":
                const deleteAction = del({
                    apiName: "candidatesManagerApi",
                    path: url,
                });
                const deleteResponse = await deleteAction.response;
                data = deleteResponse.statusCode;
                status = deleteResponse.statusCode;
                break;
            case "PATCH":
                const patchAction = patch({
                    apiName: "candidatesManagerApi",
                    path: url,
                    options: {
                        body: body,
                    },
                });
                const patchResponse = await patchAction.response;
                data = await patchResponse.body.json();
                status = patchResponse.statusCode;
                break;
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
        return { data, meta: { status } };
    } catch (error: any) {
        let status = 500;
        let errorMessage = "An unknown error occurred";

        if (error?.response) {
            status = error.response.statusCode || error.response.status || 500;
            if (error.response.data) {
                errorMessage = error.response.data;
            }
        } else if (error?.statusCode) {
            status = error.statusCode;
            errorMessage = error.message || "Error occurred";
        } else if (error?.message) {
            errorMessage = error.message;
        }

        return {
            error: {
                status,
                data: errorMessage,
            },
        };
    }
};

export const candidateManagerApi = createApi({
    reducerPath: "candidateManagerApi",
    baseQuery,
    tagTypes: [
        "Job",
        "Jobs",
        "Candidate",
        "Candidates",
        "JobCandidates",
        "CustomerCandidates",
        "CandidateScore",
        "CandidateStatus",
        "DoesInterviewReportExist",
    ],
    endpoints: (builder) => ({
        getJobs: builder.query<CMJobsTableDataResponse, string>({
            query: (customerId: string) => ({
                url: `/jobs/${customerId}`,
                method: "GET",
            }),
            providesTags: ["Jobs"],
        }),
        getJobById: builder.query<CMJobDetailsResponse, string>({
            query: (jobId: string) => ({
                url: `/jobs/details/${jobId}`,
                method: "GET",
            }),
            providesTags: ["Job"],
        }),
        getCandidates: builder.query<CMCandidatesTableDataResponse, string>({
            query: (customerId: string) => ({
                url: `/candidates/${customerId}`,
                method: "GET",
            }),
            providesTags: ["CustomerCandidates"],
        }),
        getCandidatesByJobId: builder.query<CMCandidatesTableDataResponse, string>({
            query: (jobId: string) => ({
                url: `/candidates/job/${jobId}`,
                method: "GET",
            }),
            providesTags: ["JobCandidates"],
        }),
        getCustomerLogo: builder.query<any, string>({
            query: (customerId: string) => ({
                url: `/customers/logo/${customerId}`,
                method: "GET",
            }),
        }),
        createCandidate: builder.mutation<{ id: string }, CreateCandidateRequest>({
            query: (candidateData) => ({
                url: "/candidates",
                method: "POST",
                body: candidateData,
            }),
            invalidatesTags: ["JobCandidates", "CustomerCandidates"],
        }),
        bulkCreateCandidates: builder.mutation<{ count: number }, CMBulkCreateCandidatesRequest>({
            query: (data) => ({
                url: "/candidates/bulk",
                method: "POST",
                body: data,
            }),
            invalidatesTags: ["JobCandidates", "CustomerCandidates"],
        }),
        updateJobInvitation: builder.mutation<void, CMUpdateJobInvitationRequest>({
            query: (body) => ({
                url: `/jobs/${body.jobId}/invitation`,
                method: "POST",
                body: { invitation: body.invitation },
            }),
            invalidatesTags: ["Job"],
        }),
        inviteCandidates: builder.mutation<void, CMInviteCandidatesRequest>({
            query: (body) => ({
                url: `/candidates/invite`,
                method: "POST",
                body,
            }),
            invalidatesTags: ["JobCandidates"],
        }),
        getCandidateById: builder.query<CMCandidateDetailsResponse, string>({
            query: (id) => ({
                url: `/candidates/details/${id}`,
                method: "GET",
            }),
            providesTags: ["Candidate"],
        }),
        checkDuplicateCandidate: builder.query<{ exists: boolean }, { email: string; jobId: string }>({
            query: (data) => ({
                url: `/candidates/check-duplicate`,
                method: "POST",
                body: data,
            }),
        }),
        getCandidateScore: builder.query<{ score: number | null }, string>({
            query: (candidateId: string) => ({
                url: `/candidates/${candidateId}/score`,
                method: "GET",
            }),
            providesTags: ["CandidateScore"],
        }),
        getCandidateStatus: builder.query<{ status: string }, string>({
            query: (candidateId: string) => ({
                url: `/candidates/${candidateId}/status`,
                method: "GET",
            }),
            providesTags: ["CandidateStatus"],
        }),
        downloadCandidateReport: builder.query<{ base64Report: string }, string>({
            query: (candidateId: string) => ({
                url: `/candidates/${candidateId}/report`,
                method: "GET",
            }),
            providesTags: ["DoesInterviewReportExist"],
        }),
        checkIfReportExists: builder.query<{ interviewId: string | null }, string>({
            query: (candidateId: string) => ({
                url: `/candidates/${candidateId}/report-exists`,
                method: "GET",
            }),
        }),
        validateCandidatesForInvitation: builder.mutation<ValidateCandidatesResponse, ValidateCandidatesRequest>({
            query: (body) => ({
                url: `/candidates/validate-invitation`,
                method: "POST",
                body,
            }),
        }),
        validateEmail: builder.mutation<ValidateEmailResponse, ValidateEmailRequest>({
            query: (body) => ({
                url: `/candidates/validate-email`,
                method: "POST",
                body,
            }),
        }),
        getCustomerTheme: builder.query<CMCustomerThemeResponse, string>({
            query: (customerId) => ({
                url: `/customers/${customerId}/theme`,
                method: "GET",
            }),
        }),
        updateCandidate: builder.mutation<void, CMUpdateCandidateRequest>({
            query: (request) => ({
                url: `/candidates/${request.candidateId}/update`,
                method: "POST",
                body: request,
            }),
            invalidatesTags: ["Candidate", "Candidates"],
        }),
        createJob: builder.mutation<{ id: string }, CMCreateJobRequest>({
            query: (jobData) => ({
                url: "/jobs",
                method: "POST",
                body: jobData,
            }),
            invalidatesTags: ["Jobs"],
        }),
    }),
});

export const {
    useGetJobsQuery,
    useGetJobByIdQuery,
    useGetCandidatesQuery,
    useGetCandidatesByJobIdQuery,
    useGetCustomerLogoQuery,
    useCreateCandidateMutation,
    useBulkCreateCandidatesMutation,
    useUpdateJobInvitationMutation,
    useInviteCandidatesMutation,
    useGetCandidateByIdQuery,
    useLazyCheckDuplicateCandidateQuery,
    useGetCandidateScoreQuery,
    useGetCandidateStatusQuery,
    useLazyDownloadCandidateReportQuery,
    useCheckIfReportExistsQuery,
    useValidateCandidatesForInvitationMutation,
    useValidateEmailMutation,
    useGetCustomerThemeQuery,
    useUpdateCandidateMutation,
    useCreateJobMutation,
} = candidateManagerApi;
