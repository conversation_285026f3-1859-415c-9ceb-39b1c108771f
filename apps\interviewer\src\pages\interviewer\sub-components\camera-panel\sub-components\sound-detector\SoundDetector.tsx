import { useState, useEffect } from "react";
import { SoundWaves } from "./sound-waves/SoundWaves";

interface SoundDetectorProps {
    top: string;
    left: string;
}

export const SoundDetector: React.FC<SoundDetectorProps> = ({ top, left }) => {
    const [soundWave, setSoundWave] = useState<number>(0);
    const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
    const [microphoneStream, setMicrophoneStream] = useState<MediaStream | null>(null);

    const THRESHOLD = 20;
    const SMOOTHING_FACTOR = 0.9;
    let smoothedValue = 0;

    useEffect(() => {
        async function setupAudioContext() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                setMicrophoneStream(stream);

                const context = new window.AudioContext();
                setAudioContext(context);

                const analyserNode = context.createAnalyser();
                analyserNode.fftSize = 256;

                const microphone = context.createMediaStreamSource(stream);
                microphone.connect(analyserNode);

                const dataArray = new Uint8Array(analyserNode.frequencyBinCount);

                const detectSound = () => {
                    analyserNode.getByteFrequencyData(dataArray);
                    const sum = dataArray.reduce((acc, val) => acc + val, 0);
                    const average = sum / dataArray.length;

                    if (average > THRESHOLD) {
                        smoothedValue = smoothedValue * SMOOTHING_FACTOR + average * (1 - SMOOTHING_FACTOR);
                    } else {
                        smoothedValue = smoothedValue * SMOOTHING_FACTOR; 
                    }
                    setSoundWave(smoothedValue);

                    requestAnimationFrame(detectSound);
                };

                detectSound();
            } catch (error) {
                console.error("Error accessing microphone:", error);
            }
        }

        setupAudioContext();
        return () => {
            if (microphoneStream) {
                microphoneStream.getTracks().forEach((track) => track.stop());
            }
            if (audioContext) {
                audioContext.close();
            }
        };
    }, []);

    return <SoundWaves top={top} left={left} averageValue={soundWave} />;
};
