export type ReviewStatus = "in_progress" | "complete" | "not_started" | "not_available";

export interface InterviewTableData {
    id: string | null;
    runId: string;
    companyName: string;
    jobTitle: string;
    email: string | null;
    candidateName: string;
    socialId: string | null;
    language: string;
    interviewDate: string | null;
    reviewStatus: ReviewStatus;
    accountName: string;
    evaluationStatus: string;
    bucketUrl: string;
    isThereDiffsInReport: boolean;
    globalScore: number;
    retryDisabled: boolean;
}

export interface InterviewsFilterParams {
    searchText?: string;
    jobTitle?: string;
    company?: string;
    account?: string;
    reviewStatus?: string;
}

export interface InterviewsSortParams {
    field: string;
    sort: 'asc' | 'desc';
}

export interface InterviewsPaginationParams {
    page: number;
    pageSize: number;
}

// Request parameters for interviews page table data
export interface InterviewsPageTableDataRequest {
    filter?: InterviewsFilterParams;
    sort: InterviewsSortParams;
    pagination: InterviewsPaginationParams;
}

export interface InterviewsPageTableDataResponse {
    interviews: InterviewTableData[];
    totalCount: number;
}

export interface InterviewsPageFilterOptionsResponse {
    jobTitles: string[];
    companies: string[];
    accounts: string[];
}
