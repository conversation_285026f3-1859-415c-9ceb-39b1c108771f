import styled from "@emotion/styled";
import { Grid } from "@mui/material";
import { basicTheme } from "@eva/shared-ui";

interface InterviewRightSideLayoutProps {
    backgroundColor?: string | null;
}
export const InterviewCameraSideLayout = styled(Grid)<InterviewRightSideLayoutProps>(({ backgroundColor }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100dvh",
    background: backgroundColor ? backgroundColor : basicTheme.palette.primary.main,
    position: "relative",
    [basicTheme.breakpoints.down("sm")]: {
        maxHeight: "50dvh",
        flexDirection: "column",
    },
}));
export const InterviewPanelSideLayout = styled(Grid)({
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100dvh",
    gap: "22px",
    backgroundColor: basicTheme.palette.background.default,
    position: "relative",
    [basicTheme.breakpoints.down("sm")]: {
        maxHeight: "50dvh",
        gap: "16px",
        alignItems: "stretch",
        // overflow: "hidden",
    },
});
