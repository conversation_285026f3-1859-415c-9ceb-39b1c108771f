import { useMediaQueryContext } from "@eva/shared-ui";
import { AnimatePresence, motion } from "framer-motion";
import { FC, useLayoutEffect, useRef, useState } from "react";
import { useReactMediaRecorder } from "react-media-recorder";
import { useSaveCandidateImageMutation } from "../../../../../app-state/store/apis";
import { useInterviewer } from "../../../InterviewerProvider";
import { CameraOutputVideo } from "./camera-output/CameraOutput.styles";
import { MobileQuestionPreview } from "./MobileQuestionPreview";
import { RecordingControls } from "./recording-controls/RecordingControls";
import { RecordingControlsBackScreenContainer } from "./recording-controls/RecordingControls.styles";

const determineMimeType = () => {
    const mimeTypes = ["video/webm;codecs=vp9", "video/mp4;", "video/ogg"];

    for (const mimeType of mimeTypes) {
        if (MediaRecorder.isTypeSupported(mimeType)) {
            return mimeType;
        }
    }

    return "video/webm";
};

const fadeInAnimation = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.4 } },
    exit: { opacity: 0, transition: { duration: 0.3 } },
};

interface CameraPanelScreenProps {}

export const CameraPanelScreen: FC<CameraPanelScreenProps> = () => {
    const { isMobile } = useMediaQueryContext();
    const { handleRecordingStoppage, handleCountDownChange, data, isWelcomeVideoDone, trainingRecordingUrl } =
        useInterviewer();
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [saveCandidateImage] = useSaveCandidateImageMutation();
    const [isRecording, setIsRecording] = useState<boolean>(false);

    const { startRecording, stopRecording, mediaBlobUrl, previewStream } = useReactMediaRecorder({
        video: {
            width: 1280,
            height: 720,
        },
        audio: true,
        askPermissionOnMount: true,
        stopStreamsOnStop: false,
        mediaRecorderOptions: {
            mimeType: determineMimeType(),
        },
    });
    const videoRef = useRef<HTMLVideoElement>(null);

    useLayoutEffect(() => {
        if (videoRef.current && previewStream) {
            videoRef.current.srcObject = previewStream;
        }
    }, [previewStream]);

    useLayoutEffect(() => {
        if (mediaBlobUrl && !mediaBlobUrl.includes("undefined")) {
            handleRecordingStoppage(mediaBlobUrl);
        }
    }, [mediaBlobUrl]);

    const shouldDisplayVideoOutput = !isMobile || (isMobile && isWelcomeVideoDone && !trainingRecordingUrl);
    const takePicture = async (): Promise<void> => {
        if (!videoRef.current) {
            console.warn("Video element is not available.");
            return;
        }
        if (!canvasRef.current) {
            console.warn("Canvas element is not available.");
            return;
        }

        const canvas = canvasRef.current;
        const video = videoRef.current;

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        const ctx = canvas.getContext("2d");
        if (ctx) {
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            if (data?.interviewId) {
                const base64String = canvas.toDataURL("image/jpeg");
                await saveCandidateImage({
                    interviewId: data.interviewId,
                    base64Image: base64String,
                }).unwrap();
            }
        } else {
            console.warn("Canvas context is not available.");
        }
    };

    const onStartRecordingCountdownComplete = async (): Promise<void> => {
        startRecording();
        if (!data?.isImageExists) await takePicture();
        handleCountDownChange(false);
        handleRecordingChange(true);
    };

    const onStartRecordingClicked = async (): Promise<void> => {
        startRecording();
        handleRecordingChange(true);
    };

    const onRecordingComplete = (): void => {
        stopRecording();
        handleRecordingChange(false);
    };

    const handleRecordingChange = (isRecording: boolean): void => {
        setIsRecording(isRecording);
    };

    return (
        <>
            <AnimatePresence mode="wait">
                {shouldDisplayVideoOutput && (
                    <motion.div
                        key="video-output"
                        initial="initial"
                        animate="animate"
                        exit="exit"
                        variants={fadeInAnimation}
                        style={{ flexGrow: 1, width: '100%', display: 'flex' }}
                    >
                        <RecordingControlsBackScreenContainer isRecording={isRecording} />
                        <CameraOutputVideo
                            playsInline
                            data-qa="camera-output"
                            ref={isRecording ? videoRef : null}
                            autoPlay
                        />
                        <RecordingControls
                            isRecording={isRecording}
                            onRecordingComplete={onRecordingComplete}
                            onStartRecordingClicked={onStartRecordingClicked}
                            onStartRecordingCountdownComplete={onStartRecordingCountdownComplete}
                        />
                        {data && !data.isImageExists && <canvas ref={canvasRef} style={{ display: "none" }} />}
                    </motion.div>
                )}

                {isMobile && (
                    <motion.div
                        key="mobile-preview"
                        variants={fadeInAnimation}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                    >
                        <MobileQuestionPreview isRecording={isRecording} />
                    </motion.div>
                )}
            </AnimatePresence>
        </>
    );
};
