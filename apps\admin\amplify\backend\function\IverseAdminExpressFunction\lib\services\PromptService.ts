import { DB, traits, traitsFamilies } from "@eva/drizzle";
import {
    PromptsPageFamiliesResponse,
    PromptsPageTraitsResponse,
    UpdateFamilyPromptMetadataRequest,
    UpdateTraitPromptMetadataRequest,
} from "@eva/shared-common";
import { Logger } from "@eva/logger";
import { eq } from "@eva/drizzle";
export class PromptService {
    db: DB;
    logger: Logger;
    constructor(db: DB, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    async getFamiliesWithPromptMetadata(): Promise<PromptsPageFamiliesResponse> {
        try {
            const res = await this.db
                .select({
                    id: traitsFamilies.id,
                    name: traitsFamilies.family,
                    metadata: traitsFamilies.promptMetadata,
                })
                .from(traitsFamilies);

            return {
                families: res,
            };
        } catch (e) {
            this.logger.error("Error getting families with prompt metadata");
            throw e;
        }
    }

    async getTraitsWithPromptMetadata(): Promise<PromptsPageTraitsResponse> {
        try {
            const res = await this.db
                .select({
                    id: traits.id,
                    name: traits.trait,
                    metadata: traits.promptMetadata,
                })
                .from(traits);

            return {
                traits: res,
            };
        } catch (e) {
            this.logger.error("Error getting traits with prompt metadata");
            throw e;
        }
    }

    async updateFamilyPromptMetadata(request: UpdateFamilyPromptMetadataRequest) {
        try {
            this.logger.info(`Updating family prompt metadata for family ${request.id}`);
            await this.db
                .update(traitsFamilies)
                .set({
                    promptMetadata: request.metadata,
                })
                .where(eq(traitsFamilies.id, request.id));
            this.logger.info(`Updated family prompt metadata for family ${request.id}`);
        } catch (e) {
            this.logger.error("Error updating family prompt metadata");
            throw e;
        }
    }

    async updateTraitPromptMetadata(request: UpdateTraitPromptMetadataRequest) {
        try {
            this.logger.info(`Updating trait prompt metadata for trait ${request.id}`);
            await this.db
                .update(traits)
                .set({
                    promptMetadata: request.metadata,
                })
                .where(eq(traits.id, request.id));
            this.logger.info(`Updated trait prompt metadata for trait ${request.id}`);
        } catch (e) {
            this.logger.error("Error updating trait prompt metadata");
            throw e;
        }
    }
}
