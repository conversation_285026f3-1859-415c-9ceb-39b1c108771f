import { createApi } from "@reduxjs/toolkit/query/react";
import {
    InterviewResponse,
    InterviewsPageTableDataResponse,
    InterviewsPageTableDataRequest,
    ActionStatus,
    ReportVersion,
    AdditionalQuestionsResponse,
    LogsLinksResponse,
    LogsLinksRequest,
    InterviewsPageFilterOptionsResponse,
    ApproveReportVersionRequest,
    WarmupQuestionsAnswersResponse,
} from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const interviewsApi = createApi({
    reducerPath: "interviewsApi",
    baseQuery,
    tagTypes: [
        "InterviewsTableData",
        "InterviewData",
        "AdditionalQuestions",
        "LogsLinks",
        "InterviewsPageFilterOptions",
    ],
    endpoints: (builder) => ({
        getInterviewById: builder.query<InterviewResponse, string>({
            query: (interviewId: string) => ({ url: `/interview/${interviewId}`, method: "GET" }),
            providesTags: ["InterviewData"],
        }),
        getInterviewsPageTableData: builder.query<
            InterviewsPageTableDataResponse,
            InterviewsPageTableDataRequest | void
        >({
            query: (params) => {
                if (!params) {
                    return { url: "/interviews-page-table-data", method: "GET" };
                }

                const queryParams = new URLSearchParams();

                if (params.pagination) {
                    queryParams.append("page", params.pagination.page.toString());
                    queryParams.append("pageSize", params.pagination.pageSize.toString());
                }

                if (params.sort?.field && params.sort?.sort) {
                    queryParams.append("sortField", params.sort.field);
                    queryParams.append("sortOrder", params.sort.sort);
                }

                if (params.filter) {
                    if (params.filter.searchText) {
                        queryParams.append("searchText", params.filter.searchText);
                    }

                    if (params.filter.jobTitle) {
                        queryParams.append("jobTitle", params.filter.jobTitle);
                    }

                    if (params.filter.company) {
                        queryParams.append("company", params.filter.company);
                    }

                    if (params.filter.account) {
                        queryParams.append("account", params.filter.account);
                    }

                    if (params.filter.reviewStatus) {
                        queryParams.append("reviewStatus", params.filter.reviewStatus);
                    }
                }

                return {
                    url: `/interviews-page-table-data?${queryParams.toString()}`,
                    method: "GET",
                };
            },
            providesTags: ["InterviewsTableData"],
        }),
        getInterviewsPageFilterOptions: builder.query<InterviewsPageFilterOptionsResponse, void>({
            query: () => ({ url: "/interviews-page-filter-options", method: "GET" }),
            providesTags: ["InterviewsPageFilterOptions"],
        }),
        saveReviewSessionProgress: builder.mutation<void, { interviewId: string; report: ReportVersion }>({
            query: ({ interviewId, report }) => ({
                url: `/interviews/save-review-session-progress/${interviewId}`,
                method: "POST",
                body: report,
            }),
            invalidatesTags: ["InterviewData"],
        }),
        submitReport: builder.mutation<void, { interview_id: string; report: ReportVersion }>({
            query: ({ interview_id, report }) => {
                return {
                    url: `/submit-reviewed-report/${interview_id}`,
                    method: "POST",
                    body: report,
                };
            },
            invalidatesTags: ["InterviewsTableData", "InterviewData"],
        }),
        resetEditSession: builder.mutation<void, string>({
            query: (interview_id) => ({
                url: `/reset-review-session/${interview_id}`,
                method: "GET",
            }),
            invalidatesTags: ["InterviewData"],
        }),
        approveReport: builder.mutation<void, { interviewId: string; body: ApproveReportVersionRequest }>({
            query: ({ interviewId, body }) => ({
                url: `/approve-report/${interviewId}`,
                method: "POST",
                body,
            }),
            invalidatesTags: ["InterviewsTableData", "InterviewData"],
        }),
        rerunEvaluation: builder.mutation<ActionStatus, string>({
            query: (interviewId) => ({
                url: `/rerun-interview/${interviewId}`,
                method: "POST",
            }),
            onQueryStarted: async (interviewId, { dispatch, queryFulfilled }) => {
                const patchResult = dispatch(
                    interviewsApi.util.updateQueryData("getInterviewsPageTableData", undefined, (draft) => {
                        const index = draft.interviews.findIndex((interview) => interview.id === interviewId);
                        if (index !== -1) {
                            draft.interviews[index].evaluationStatus = "pending";
                            draft.interviews[index].reviewStatus = "not_available";
                        }
                    })
                );
                try {
                    await queryFulfilled;
                } catch {
                    patchResult.undo();
                }
            },
        }),
        getAdditionalQuestions: builder.query<AdditionalQuestionsResponse, string>({
            query: (interviewId) => ({
                url: `/additional-questions/${interviewId}`,
                method: "GET",
                providesTags: ["AdditionalQuestions"],
            }),
        }),
        getAnalyzerLogs: builder.query<LogsLinksResponse, LogsLinksRequest>({
            query: (body) => ({
                url: "/analyzer-logs",
                method: "POST",
                body,
            }),
            providesTags: ["LogsLinks"],
        }),
        getWarmupQuestions: builder.query<WarmupQuestionsAnswersResponse, string>({
            query: (interviewId) => ({
                url: `/warmup-questions/${interviewId}`,
                method: "GET",
            }),
        }),
    }),
});

export const {
    useGetInterviewByIdQuery,
    useGetInterviewsPageTableDataQuery,
    useSaveReviewSessionProgressMutation,
    useSubmitReportMutation,
    useResetEditSessionMutation,
    useApproveReportMutation,
    useRerunEvaluationMutation,
    useGetAdditionalQuestionsQuery,
    useGetAnalyzerLogsQuery,
    useLazyGetAnalyzerLogsQuery,
    useGetInterviewsPageFilterOptionsQuery,
    useGetWarmupQuestionsQuery,
} = interviewsApi;
