import { Logger } from '@eva/logger';
import { answers, candidates, DB, interviews, jobs, questions} from '@eva/drizzle';
import { GetReportsRequest, GetReportsResponse } from './types';
import { and, eq, gt, inArray, isNotNull, lt } from '@eva/drizzle';
import { AnalyzerReportData } from '@eva/shared-common';
import { generateCloudfrontSignedUrl, CloudfrontAccessPair } from '@eva/aws-utils';

export class ReportHandler {
    private db: DB;
    private logger: Logger;
    private cloudfrontSignedUrlSecret: CloudfrontAccessPair;
    constructor(db: DB, logger: Logger, cloudfrontSignedUrlSecret: CloudfrontAccessPair) {
        this.db = db;
        this.logger = logger;
        this.cloudfrontSignedUrlSecret = cloudfrontSignedUrlSecret;
    }

    async getReports(getReportsRequest: GetReportsRequest): Promise<GetReportsResponse> {
        const startDate = new Date(getReportsRequest.startDate);
        const endDate = getReportsRequest.endDate ? new Date(getReportsRequest.endDate) : null;

        this.logger.info('Getting reports for job', JSON.stringify(getReportsRequest));

        const timeClause = endDate
            ? and(gt(interviews.createdAt, startDate), lt(interviews.createdAt, endDate))
            : gt(interviews.createdAt, startDate);

        const res = await this.db
            .select({
                interviewId: interviews.id,
                createAt: interviews.createdAt,
                reportData: interviews.analyzerReport,
                candidateName: candidates.name,
                candidateId: candidates.id,
                candidateSocialId: candidates.socialId,
                jobName: jobs.jobTitle,
                jobId: jobs.id,
                gender: candidates.gender,
            })
            .from(jobs)
            .innerJoin(candidates, eq(jobs.id, candidates.jobId))
            .innerJoin(
                interviews,
                and(eq(candidates.id, interviews.candidateId), isNotNull(interviews.analyzerReport), timeClause),
            )
            .where(eq(jobs.id, getReportsRequest.jobId));

        const answersRes = await this.db
            .select({ interviewId: answers.interviewId, answer: answers.answer })
            .from(answers)
            .where(
                inArray(
                    answers.interviewId,
                    res.map((r) => r.interviewId),
                ),
            );

        const answersForInterviews = answersRes.filter(
            (a) => a.answer.translated_answer !== undefined && a.answer.question_id !== null,
        );

        const customAdditionalQuestions = answersRes.filter((a) => a.answer.question_id === null);

        for (const a of answersForInterviews) {
            a.answer.video_link = await generateCloudfrontSignedUrl(
                a.answer.video_link,
                2000,
                this.cloudfrontSignedUrlSecret,
            );
        }

        for (const c of customAdditionalQuestions) {
            c.answer.video_link = await generateCloudfrontSignedUrl(
                c.answer.video_link,
                2000,
                this.cloudfrontSignedUrlSecret,
            );
        }

        const questionIds = answersForInterviews.map((a) => a.answer.question_id).filter((id) => id !== null);

        const questionsRes = await this.db
            .select({ traitId: questions.traitId, questionId: questions.id })
            .from(questions)
            .where(inArray(questions.id, questionIds));

        const reports = res.map((r) => {
            const data = r.reportData as AnalyzerReportData;

            const answersForInterview = answersForInterviews
                .filter((a) => a.interviewId === r.interviewId)
                .map((a) => {
                    return {
                        trait_id: questionsRes.find((q) => q.questionId === a.answer.question_id)?.traitId!,
                        question_id: a.answer.question_id,
                        question: a.answer.question,
                        answer: a.answer.answer,
                        translated_answer: a.answer.translated_answer,
                        video_link: a.answer.video_link,
                    };
                });

            const customAdditionalQuestionsForInterview = customAdditionalQuestions
                .filter((c) => c.interviewId === r.interviewId)
                .map((c) => ({
                    question: c.answer.question,
                    answer: c.answer.answer,
                    video_link: c.answer.video_link,
                }));

            return {
                interviewId: r.interviewId,
                socialId: r.candidateSocialId,
                createdAt: r.createAt || new Date(),
                jobName: r.jobName,
                jobId: r.jobId,
                gender: r.gender,
                candidateName: r.candidateName,
                candidateId: r.candidateId,
                versions: data.report_versions,
                answers: answersForInterview,
                customAdditionalQuestions: customAdditionalQuestionsForInterview,
            };
        });

        return { reports };
    }
}
