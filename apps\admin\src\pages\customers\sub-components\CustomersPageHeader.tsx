import { Stack, TextField } from "@mui/material";
import React, { FC } from "react";
import { PrimaryButton } from "@eva/shared-ui";
import AddIcon from "@mui/icons-material/Add";

interface CustomersPageHeaderProps {
    searchText: string;
    handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    handleAddCustomerClicked: () => void;
}

const CustomersPageHeader: FC<CustomersPageHeaderProps> = ({ handleAddCustomerClicked, handleSearchChange, searchText }) => (
    <Stack direction='row' alignItems='center' justifyContent='space-between'>
        <Stack width='400px'>
            <TextField value={searchText} onChange={handleSearchChange} label='Search' fullWidth={false} />
        </Stack>
        <Stack>
            <PrimaryButton data-qa='add-customer-btn' onClick={handleAddCustomerClicked} content='Add Customer' endIcon={<AddIcon />} />
        </Stack>
    </Stack>
);


export default CustomersPageHeader;