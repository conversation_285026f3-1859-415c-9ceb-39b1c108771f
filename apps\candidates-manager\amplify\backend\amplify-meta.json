{"providers": {"awscloudformation": {"AuthRoleName": "amplify-candidatesmanager-staging-9a95a-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-candidatesmanager-staging-9a95a-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-candidatesmanager-staging-9a95a-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-candidatesmanager-staging-9a95a-deployment", "UnauthRoleName": "amplify-candidatesmanager-staging-9a95a-unauthRole", "StackName": "amplify-candidatesmanager-staging-9a95a", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-candidatesmanager-staging-9a95a/16a56ff0-0277-11f0-b905-0ed90967629b", "AmplifyAppId": "d2q6sevltntydn", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}}, "api": {"candidatesManagerApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "candidates<PERSON>anager<PERSON>unction"}], "providerPlugin": "awscloudformation", "service": "API Gateway", "output": {"ApiName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RootUrl": "https://gicxrqipbk.execute-api.us-east-1.amazonaws.com/staging", "ApiId": "gicxrqipbk"}, "lastPushTimeStamp": "2025-04-24T12:56:04.991Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/api/candidatesManagerApi-cloudformation-template.json", "logicalId": "apicandidatesManager<PERSON>pi"}}}, "auth": {"candidatesmanager": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyMinLength": 8, "passwordPolicyCharacters": []}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed", "output": {"UserPoolId": "us-east-1_qu1ElrEP8", "AppClientIDWeb": "486c95o54e2n9g0hk4afhcogvp", "AppClientID": "40t1cb32r1lo7h1vj0linqefu9", "IdentityPoolId": "us-east-1:2acc47f7-a2f8-4aca-b614-dd6b65677532", "UserPoolArn": "arn:aws:cognito-idp:us-east-1:405424696292:userpool/us-east-1_qu1ElrEP8", "IdentityPoolName": "candidatesmanager_identitypool_7eca843a__staging", "UserPoolName": "candidatesmanager_userpool_7eca843a"}, "lastPushTimeStamp": "2025-04-24T12:56:04.994Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/auth/candidatesmanager-cloudformation-template.json", "logicalId": "authcandidatesmanager"}}}, "function": {"candidatesManagerFunction": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"SMSInvitationSNSTopicArn": "arn:aws:sns:us-east-1:405424696292:candidates-sms-invitation-staging", "LambdaExecutionRoleArn": "arn:aws:iam::405424696292:role/candidatesmanagerLambdaRoledac3261b-staging", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:405424696292:function:candidatesManagerFunction-staging", "Name": "candidatesManagerFunction-staging", "LambdaExecutionRole": "candidatesmanagerLambdaRoledac3261b-staging"}, "lastPushTimeStamp": "2025-04-24T12:56:04.996Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/function/candidatesManagerFunction-cloudformation-template.json", "logicalId": "functioncandidatesManagerFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-candidatesmanager-staging-9a95a-deployment", "s3Key": "amplify-builds/candidatesManagerFunction-71325148644c71426361-build.zip"}, "lastBuildTimeStamp": "2025-04-24T12:39:06.755Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-04-24T12:39:06.822Z", "distZipFilename": "candidatesManagerFunction-71325148644c71426361-build.zip", "lastPushDirHash": "B4Op9N20TMNoPnEpf/hF3uC9Yfk="}}, "hosting": {"amplifyhosting": {"providerPlugin": "awscloudformation", "service": "amplifyhosting", "type": "manual", "output": {}, "lastPushTimeStamp": "2025-04-24T12:56:04.998Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/hosting/amplifyhosting-template.json", "logicalId": "hostingamplifyhosting"}}}}