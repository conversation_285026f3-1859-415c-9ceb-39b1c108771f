import { FC } from "react";
import { MobilePrimaryButton, PrimaryButton, PrimaryButtonProps, useMediaQueryContext } from "@eva/shared-ui";

interface InterviewerPrimaryButtonProps extends PrimaryButtonProps {}

export const InterviewerPrimaryButton: FC<InterviewerPrimaryButtonProps> = ({
    content,
    onClick,
    "data-qa": dataQa,
    disabled,
    endIcon,
    isLoading,
    timeDisabled,
    variant,
}) => {
    const { isMobile } = useMediaQueryContext();

    return isMobile ? (
        <MobilePrimaryButton
            data-qa={dataQa}
            disabled={disabled}
            onClick={onClick}
            content={content}
            endIcon={endIcon}
            isLoading={isLoading}
            timeDisabled={timeDisabled}
            variant={variant}
        />
    ) : (
        <PrimaryButton
            data-qa={dataQa}
            disabled={disabled}
            onClick={onClick}
            content={content}
            endIcon={endIcon}
            isLoading={isLoading}
            timeDisabled={timeDisabled}
            variant={variant}
        />
    );
};
