import { Document, Page } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../PDF.styles";
import { JobQuestionsPDFHeader } from "./JobQuestionsPDFHeader";
import { formatDate } from "@eva/shared-ui";
import { ReportFooter } from "../report-pdf/sub-components/report-footer/ReportFooter";
import { JobQuestionsPDFBody } from "./JobQuestionsPDFBody";

interface JobQuestionsPDFProps {
    isRtl: boolean;
    createdAt: string;
    jobTitle: string;
    companyName: string;
    questions: { question: string; trait: string }[];
}

export const JobQuestionsPDF: FC<JobQuestionsPDFProps> = ({ isRtl, companyName, createdAt, jobTitle, questions }) => {
    return (
        <Document>
            <Page style={styles.page}>
                <JobQuestionsPDFHeader
                    companyName={companyName}
                    createdAt={formatDate(createdAt)}
                    isRtl={isRtl}
                    jobTitle={jobTitle}
                />
                <JobQuestionsPDFBody isRtl={isRtl} questions={questions} />
                <ReportFooter candidateName={jobTitle} page={1} totalPages={1} isRtl={isRtl} />
            </Page>
        </Document>
    );
};
