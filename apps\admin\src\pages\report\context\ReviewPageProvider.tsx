// src/contexts/ReviewPageContext.tsx
import { useAuthenticator } from "@aws-amplify/ui-react";
import {
    AdditionalQuestionsResponse,
    FamiliesWithTraitsForDisplayMap,
    Family,
    InterviewResponse,
    ReportVersion,
    Trait,
    WarmupQuestionsAnswersResponse,
} from "@eva/shared-common";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useParams } from "react-router";
import {
    useGetInterviewByIdQuery,
    useResetEditSessionMutation,
    useSaveReviewSessionProgressMutation,
    useSubmitReportMutation,
    useGetAdditionalQuestionsQuery,
    useGetWarmupQuestionsQuery,
} from "../../../app-state/apis";
import { compareReports } from "../ReviewPage,utils";

const excludedTraits = ["A38"];

interface ReviewPageContextProps {
    interviewData: InterviewResponse | undefined;
    additionalQuestionsResponse: AdditionalQuestionsResponse | undefined;
    warmupQuestionsResponse: WarmupQuestionsAnswersResponse | undefined;
    familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap | undefined;
    isEditMode: boolean;
    editRequest: ReportVersion | undefined;
    isConfirmDialogOpen: boolean;
    isLoading: boolean;
    handleFamiliesChange: (family: Family) => void;
    handleTraitChange: (trait: Trait, familyName: string) => void;
    handleSubmitReport: () => void;
    handleEditClick: () => void;
    handleSaveClick: () => void;
    handleResetClick: () => void;
    onExitEditModeClicked: () => void;
    onApproveExitEditModeClicked: () => void;
    onCloseConfirmationDialogClicked: () => void;
}

const ReviewPageContext = createContext<ReviewPageContextProps | undefined>(undefined);

export const useReviewPageContext = () => {
    const context = useContext(ReviewPageContext);
    if (!context) {
        throw new Error("useReviewPageContext must be used within a ReviewPageProvider");
    }
    return context;
};

export const ReviewPageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { id } = useParams<{ id: string }>();
    const { user } = useAuthenticator();
    const { data: additionalQuestionsResponse } = useGetAdditionalQuestionsQuery(id!, {
        skip: !id,
    });
    const { data: warmupQuestionsResponse } = useGetWarmupQuestionsQuery(id!, {
        skip: !id,
    });
    const { data: interviewData, isFetching } = useGetInterviewByIdQuery(id!, {
        skip: !id,
        refetchOnMountOrArgChange: true,
    });

    const [resetEditSession] = useResetEditSessionMutation();
    const [updateReportEditSession] = useSaveReviewSessionProgressMutation();
    const [submitReport] = useSubmitReportMutation();
    const [isEditMode, setIsEditMode] = useState(false);
    const [editRequest, setEditRequest] = useState<ReportVersion | undefined>(undefined);
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

    const isLoading = isFetching || !interviewData || !additionalQuestionsResponse;

    useEffect(() => {
        if (interviewData?.reportEditSession) {
            setEditRequest(interviewData.reportEditSession);
            setIsEditMode(true);
        }
    }, [interviewData]);
    const handleResetClick = () => {
        if (interviewData && id) {
            setEditRequest(interviewData.report);
            resetEditSession(id);
        }
    };

    const handleEditClick = () => {
        if (interviewData) {
            setEditRequest(interviewData?.reportEditSession ? interviewData.reportEditSession : interviewData.report);
            setIsEditMode((prev) => !prev);
        }
    };

    const handleSaveClick = () => {
        if (!editRequest) return;
        if (id) {
            updateReportEditSession({
                interviewId: id,
                report: editRequest,
            });
        }
    };

    const handleFamiliesChange = (family: Family) => {
        if (editRequest) {
            const idx = editRequest.families.findIndex((oldFamily) => oldFamily.code === family.code);
            if (idx !== -1) {
                const updatedFamilies = [...editRequest.families];
                updatedFamilies.splice(idx, 1, family);
                setEditRequest({
                    ...editRequest,
                    families: updatedFamilies,
                });
            }
        }
    };

    const handleTraitChange = (trait: Trait, familyCode: string) => {
        if (editRequest && interviewData) {
            const idx = editRequest.traits.findIndex((oldTrait) => oldTrait.code === trait.code);

            if (excludedTraits.includes(trait.code)) {
                const updatedTraits = [...editRequest.traits];
                updatedTraits.splice(idx, 1, trait);
                setEditRequest({
                    ...editRequest,
                    traits: updatedTraits,
                });
                return;
            }

            if (idx !== -1 && interviewData.familiesWithTraitsMap) {
                const updatedTraits = [...editRequest.traits];
                updatedTraits.splice(idx, 1, trait);

                const familyTraitsMap = interviewData.familiesWithTraitsMap[familyCode].traitsMap;

                const traitsForCalculation = updatedTraits.filter((trait) =>
                    Object.keys(familyTraitsMap).includes(trait.code)
                );

                const traitsForScoring = traitsForCalculation.filter((trait) => trait.score > 0);

                const newFamilyScore =
                    traitsForScoring.reduce((acc, trait) => acc + trait.score, 0) / traitsForScoring.length;

                const familyIdx = editRequest.families.findIndex((family) => family.code === familyCode);

                if (familyIdx !== -1) {
                    const updatedFamilies = [...editRequest.families];

                    updatedFamilies.splice(familyIdx, 1, {
                        ...updatedFamilies[familyIdx],
                        score: newFamilyScore ? newFamilyScore : 0,
                    });

                    const familiesForScoring = updatedFamilies.filter((family) => family.score > 0);
                    const updateInterviewScore: number =
                        familiesForScoring.reduce((acc, family) => acc + family.score, 0) / familiesForScoring.length;

                    setEditRequest({
                        ...editRequest,
                        interview_score: updateInterviewScore ? Number(updateInterviewScore.toFixed(1)) : 0,
                        families: updatedFamilies,
                        traits: updatedTraits,
                    });
                }
            }
        }
    };

    const handleSubmitReport = () => {
        if (!editRequest) return;
        if (id) {
            submitReport({
                interview_id: id,
                report: {
                    ...editRequest,
                    reviewed_by: user.username,
                    review_date: new Date().toISOString(),
                },
            })
                .unwrap()
                .then(() => {
                    setIsEditMode(false);
                    setEditRequest(undefined);
                });
        }
    };

    const onExitEditModeClicked = () => {
        if (interviewData && editRequest) {
            if (compareReports(interviewData.report, interviewData?.reportEditSession, editRequest)) {
                setIsConfirmDialogOpen(true);
            } else {
                setIsEditMode(false);
                setEditRequest(undefined);
            }
        }
    };

    const onApproveExitEditModeClicked = () => {
        setIsConfirmDialogOpen(false);
        setIsEditMode(false);
        setEditRequest(undefined);
    };

    const onCloseConfirmationDialogClicked = () => {
        setIsConfirmDialogOpen(false);
    };

    return (
        <ReviewPageContext.Provider
            value={{
                isLoading,
                isEditMode,
                editRequest,
                interviewData,
                isConfirmDialogOpen,
                familiesWithTraitsMap: interviewData?.familiesWithTraitsMap,
                additionalQuestionsResponse,
                warmupQuestionsResponse,
                handleFamiliesChange,
                handleTraitChange,
                handleSubmitReport,
                handleEditClick,
                handleSaveClick,
                handleResetClick,
                onExitEditModeClicked,
                onApproveExitEditModeClicked,
                onCloseConfirmationDialogClicked,
            }}
        >
            {children}
        </ReviewPageContext.Provider>
    );
};
