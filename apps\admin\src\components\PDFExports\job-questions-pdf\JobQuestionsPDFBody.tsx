import { FC } from "react";
import { stylesPDF as styles } from "../PDF.styles";
import { Text, View } from "@react-pdf/renderer";

interface JobQuestionsPDFBodyProps {
    isRtl: boolean;
    questions: { question: string; trait: string }[];
}

export const JobQuestionsPDFBody: FC<JobQuestionsPDFBodyProps> = ({ isRtl, questions }) => {
    return (
        <View style={[styles.sectionContainer]}>
            <View style={[{ textAlign: isRtl ? "right" : "left",},styles.flexGap6, styles.displayFlex]}>
                {questions.map((question, idx) => (
                    <Text
                        key={question.trait}
                        style={[isRtl ? styles.hebrewQuestionsListText : styles.QuestionsListText]}
                    >
                        {idx + 1}.{question.trait}-{question.question}
                    </Text>
                ))}
            </View>
        </View>
    );
};
