import { InterviewQuestionAnswer } from '@eva/shared-common';
import { ReportVersion } from '@eva/shared-common/types/AnalyzerReportData';

export type EventIdsValidation = {
    iverseJobId: string | null;
    candidateExists: boolean;
    accountExists: boolean;
};

export interface IdsValidation {
    accountName: string | null;
    iverseJobId: string | null;
    candidateId: string | null;
}

export interface QuestionForProcessing {
    questionId: string;
    question: string;
}

export interface GetReportsRequest {
    jobId: string;
    startDate: Date;
    endDate: Date | null;
}

export interface GetReportsResponse {
    reports: {
        socialId: string | null;
        interviewId: string;
        createdAt: Date;
        jobName: string;
        jobId: string;
        candidateName: string;
        candidateId: string;
        versions: ReportVersion[];
        answers?: (InterviewQuestionAnswer & { trait_id: string })[];
        customAdditionalQuestions: Omit<InterviewQuestionAnswer, 'question_id'>[];
        gender: string | null;
    }[];
}

export interface CreateCustomerRequest {
    name: string;
    logo?: string;
    themeColor?: string;
}

export interface CreateCustomerResponse {
    customerId: string;
}

export interface CreateJobRequest {
    name: string;
    description: string;
    language: string;
}

export interface CreateJobResponse {
    jobId: string;
}
