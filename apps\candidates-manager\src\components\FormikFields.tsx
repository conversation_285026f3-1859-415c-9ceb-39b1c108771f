import React from "react";
import { useField } from "formik";
import {
    FormControl,
    FormHelperText,
    FormLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
    styled,
} from "@mui/material";

const StyledFormLabel = styled(FormLabel)(({ theme }) => ({
    color: theme.palette.primary.main,
}));

export interface FormikFieldProps {
    name: string;
    label: string;
    type?: string;
    required?: boolean;
    options?: { value: string; label: string }[];
    maxLength?: number;
    multiline?: boolean;
}

export const FormikTextField: React.FC<FormikFieldProps> = ({
    name,
    label,
    type = "text",
    required = false,
    maxLength,
    multiline = false,
}) => {
    const [field, meta, helpers] = useField(name);
    const hasError = meta.touched && !!meta.error;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Handle maxLength specifically for type="number" because TextField doesn't enforce it natively
        if (type === "number" && maxLength && value.length > maxLength) {
            helpers.setValue(value.slice(0, maxLength));
        } else {
            helpers.setValue(value);
        }
    };

    return (
        <FormControl fullWidth error={hasError}>
            <StyledFormLabel required={required}>{label}</StyledFormLabel>
            <TextField
                multiline={multiline}
                rows={multiline ? 4 : undefined}
                type={type}
                value={field.value}
                onChange={handleChange}
                onBlur={() => {
                    helpers.setTouched(true);
                }}
                error={hasError}
                fullWidth
                inputProps={{
                    maxLength: type !== "number" ? maxLength : undefined,
                }}
            />
            {hasError && <FormHelperText>{meta.error}</FormHelperText>}
        </FormControl>
    );
};

export const FormikSelectField: React.FC<FormikFieldProps> = ({ name, label, required, options = [] }) => {
    const [field, meta, helpers] = useField(name);
    const hasError = meta.touched && !!meta.error;

    const handleChange = (e: SelectChangeEvent<string>) => {
        helpers.setValue(e.target.value);
    };

    const handleBlur = () => {
        helpers.setTouched(true);
    };

    return (
        <FormControl fullWidth error={hasError}>
            <StyledFormLabel required={required}>{label}</StyledFormLabel>
            <Select value={field.value} onChange={handleChange} onBlur={handleBlur} fullWidth>
                {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label}
                    </MenuItem>
                ))}
            </Select>
            {hasError && <FormHelperText>{meta.error}</FormHelperText>}
        </FormControl>
    );
};
