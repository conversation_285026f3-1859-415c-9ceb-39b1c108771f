import { Logger } from "@eva/logger";
import { DB, jobs, eq } from "@eva/drizzle";
import {
    CMJobDetailsResponse,
    CMJobsTableDataResponse,
    CMCreateJobRequest,
    JobInvitationData,
    JobQuestionsData,
    JobStatus,
    createDefaultInvitationTemplates,
} from "@eva/shared-common";

const defaultQuestions: JobQuestionsData = {
    openQuestions: [],
    questionIds: [],
    computedTraits: [],
    warmupQuestions: [],
    simulations: [],
};

export class JobService {
    constructor(
        private db: DB,
        private logger: Logger
    ) {}

    async getJobs(customerId: string): Promise<CMJobsTableDataResponse> {
        try {
            const res = await this.db
                .select({
                    id: jobs.id,
                    title: jobs.jobTitle,
                    description: jobs.description,
                    status: jobs.status,
                    createdAt: jobs.createdAt,
                })
                .from(jobs)
                .where(eq(jobs.customerId, customerId));
            return {
                jobs: res.map((job) => ({
                    id: job.id,
                    title: job.title,
                    description: job.description,
                    status: job.status,
                    createdAt: job.createdAt.toISOString(),
                })),
            };
        } catch (error) {
            this.logger.error(`Error getting jobs for customer ${customerId}`, error);
            throw error;
        }
    }

    async getJobById(jobId: string): Promise<CMJobDetailsResponse> {
        try {
            const job = await this.db
                .select({
                    id: jobs.id,
                    title: jobs.jobTitle,
                    description: jobs.description,
                    createdAt: jobs.createdAt,
                    customerId: jobs.customerId,
                    language: jobs.language,
                    status: jobs.status,
                    invitation: jobs.invitation,
                })
                .from(jobs)
                .where(eq(jobs.id, jobId))
                .then((res) => res[0]);

            if (!job) {
                throw new Error(`Job with ID ${jobId} not found`);
            }

            return {
                id: job.id,
                title: job.title,
                description: job.description,
                createdAt: job.createdAt.toISOString(),
                customerId: job.customerId,
                language: job.language,
                invitation: job.invitation,
                status: job.status,
            };
        } catch (error) {
            this.logger.error(`Error getting job with ID ${jobId}`, error);
            throw error;
        }
    }

    async updateJobInvitation(jobId: string, invitation: JobInvitationData): Promise<void> {
        try {
            await this.db.update(jobs).set({ invitation }).where(eq(jobs.id, jobId));
        } catch (error) {
            this.logger.error(`Error updating job invitation for job ${jobId}`, error);
            throw error;
        }
    }

    async createJob(jobData: CMCreateJobRequest): Promise<{ id: string }> {
        try {
            const templates = createDefaultInvitationTemplates();
            const defaultInvitation = {
                emailTag: "[email]",
                nameTag: "[name]",
                phoneTag: "[phone]",
                mailTemplateOption: templates.mail,
                smsTemplateOption: templates.sms,
            };

            const newJob = {
                jobTitle: jobData.jobTitle,
                description: jobData.description,
                language: jobData.language,
                customerId: jobData.customerId,
                questions: defaultQuestions,
                invitation: defaultInvitation,
                status: "pending-definition" as JobStatus,
            };

            const res = await this.db.insert(jobs).values(newJob).returning({ id: jobs.id });
            this.logger.info(`Job created successfully with ID: ${res[0].id}`);
            return { id: res[0].id };
        } catch (error) {
            this.logger.error("Error creating job", error);
            throw error;
        }
    }
}
