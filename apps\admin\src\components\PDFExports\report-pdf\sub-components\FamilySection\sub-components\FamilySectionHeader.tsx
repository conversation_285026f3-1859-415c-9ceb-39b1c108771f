import { Text, View } from "@react-pdf/renderer";
import { FamilySVGIcon } from "../../icons/FamilySVGIcon";
import { stylesPDF as styles } from "../../../../PDF.styles";

interface FamilySectionHeaderProps {
    isRtl: boolean;
    familyName: string;
    scoreForDisplay: string;
    overallScoreText: string;
    familyCode: string;
}

export const FamilySectionHeader = ({ familyCode, familyName, isRtl, overallScoreText, scoreForDisplay }: FamilySectionHeaderProps) => {
    return (
        <View style={[styles.familySectionHeaderContainer, isRtl ? styles.flexReverse : {}]}>
            <View
                style={[styles.displayFlex, styles.flexGap6, styles.flewDirectionRow, styles.alignItemsCenter, isRtl ? styles.flexReverse : {}]}
            >
                <FamilySVGIcon size='12px' familyCode={familyCode} />
                <View>
                    <Text style={isRtl ? styles.hebrewTextBoldHeader : styles.textBoldHeader}>{familyName}</Text>
                </View>
            </View>
            <View style={[styles.displayFlex, isRtl ? styles.alignStart : styles.alignEnd]}>
                <Text style={styles.textBoldHeader}>{scoreForDisplay}</Text>
                <Text style={isRtl ? styles.hebrewFamilyOverallScoreText : styles.familyOverallScoreText}>{overallScoreText}</Text>
            </View>
        </View>
    );
};
