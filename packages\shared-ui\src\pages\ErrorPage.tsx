import React from 'react';
import { PageLayout } from '../toolkit/layout/PageLayout';
import { PageContainer } from '../toolkit/layout/PageContainer';
import { ErrorMessage } from '../toolkit/components';

interface ErrorPageProps {
  message?: string;
  onRetry?: () => void;
}

export const ErrorPage: React.FC<ErrorPageProps> = ({ 
  message = "Something went wrong. Please try again later.", 
  onRetry 
}) => {
  return (
    <PageContainer>
      <PageLayout>
        <ErrorMessage 
          message={message}
          onRetry={onRetry}
        />
      </PageLayout>
    </PageContainer>
  );
}; 