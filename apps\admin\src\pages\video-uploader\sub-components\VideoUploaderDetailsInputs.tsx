import {
    CustomersResponse,
    FamiliesWithQuestionsForBuilderResponse,
    LanguageVariation,
    QuestionOptions,
    QuestionsLanguageVariations,
} from "@eva/shared-common";
import { capitalize, Collapse, SelectChangeEvent, Stack } from "@mui/material";
import { useField } from "formik";
import { FC, useEffect } from "react";
import { languageMapper, languages } from "../../../app-state/appText";
import { CustomSelect, CustomSelectOption } from "@eva/shared-ui";

interface VideoUploaderDetailsInputsProps {
    customersData: CustomersResponse;
    questionsData: FamiliesWithQuestionsForBuilderResponse;
    hideTypeSelection?: boolean;
}
const determineQuestionOptionsByCustomerAndLanguage = (
    customerId: string,
    language: keyof QuestionsLanguageVariations,
    questionOptions: QuestionOptions
) => {
    if (questionOptions.options[customerId]) return questionOptions.options[customerId][language].male.text;
    else return questionOptions.options.default[language].male.text;
};

export const VideoUploaderDetailsInputs: FC<VideoUploaderDetailsInputsProps> = ({ questionsData, customersData, hideTypeSelection = false }) => {
    const [videoTypeField, , { setValue: setTypeValue }] = useField("type");
    const [languageField, , { setValue: setLanguageValue }] = useField("language");
    const [customerField, , { setValue: setCustomerValue }] = useField("customerId");
    const [questionField, , { setValue: setQuestionValue }] = useField("questionId");
    const [genderField, , { setValue: setGenderValue }] = useField("gender");

    const questionsLanguageForDisplayKey: string = languageField.value ? languageField.value : "en";
    const customerOptions: CustomSelectOption[] = [
        {
            label: "All",
            value: "all",
        },
        ...customersData.customers.map((customer) => ({
            value: customer.id,
            label: customer.companyName,
        })),
    ];

    const languageOptions: CustomSelectOption[] = languages.map((language) => ({
        value: language,
        label: languageMapper(language),
    }));

    const genderOptions: CustomSelectOption[] = [
        {
            value: "all",
            label: "All",
        },
        {
            value: "male",
            label: "Male",
        },
        {
            value: "female",
            label: "Female",
        },
    ];

    const typeOptions: CustomSelectOption[] = [
        { value: "question", label: "Question" },
        { value: "intro", label: "Intro" },
    ];

    const questionOptions =
        customerField.value && languageField.value
            ? questionsData.families.reduce((acc, family) => {
                  const familyQuestions = family.questions.map((question) => ({
                      value: question.question_id,
                      label: `${
                          question.trait[questionsLanguageForDisplayKey as keyof LanguageVariation]
                      } - ${determineQuestionOptionsByCustomerAndLanguage(customerField.value, languageField.value, question.question)}`,
                  }));
                  return [...acc, ...familyQuestions];
              }, [] as CustomSelectOption[])
            : [];

    const handleLanguageChange = (event: SelectChangeEvent<string>) => {
        setLanguageValue(event.target.value);
    };

    const handleCustomerChange = (event: SelectChangeEvent<string>) => {
        setCustomerValue(event.target.value);
    };

    const handleQuestionInputChange = (event: SelectChangeEvent<string>) => {
        setQuestionValue(event.target.value);
    };

    const handleGenderChange = (event: SelectChangeEvent<string>) => {
        setGenderValue(event.target.value);
    };

    const handleVideoTypeChange = (event: SelectChangeEvent<string>) => {
        setTypeValue(event.target.value);
    };

    const isQuestionInputVisible = videoTypeField.value === "question";
    const isQuestionsInputDisabled = !languageField.value || !customerField.value || (languageField.value === "he" && !genderField.value);
    const isGenderFieldVisible = languageField.value === "he";

    useEffect(() => {
        if (isQuestionsInputDisabled || isQuestionsInputDisabled) setQuestionValue("");
        if (!isGenderFieldVisible) setGenderValue("all");
    }, [isQuestionsInputDisabled, isGenderFieldVisible]);

    return (
        <>
            <Stack direction='row' gap={2}>
                {!hideTypeSelection && (
                    <CustomSelect options={typeOptions} label='Type' onChange={handleVideoTypeChange} value={videoTypeField.value} />
                )}
                <CustomSelect
                    label='Language'
                    options={languageOptions}
                    value={languageField.value}
                    onChange={handleLanguageChange}
                    mapper={languageMapper}
                />
                <CustomSelect options={customerOptions} label='Customers' onChange={handleCustomerChange} value={customerField.value} />
                {isGenderFieldVisible && (
                    <CustomSelect options={genderOptions} label='Gender' onChange={handleGenderChange} value={genderField.value} />
                )}
            </Stack>
            {isQuestionInputVisible && (
                <Collapse in={isQuestionInputVisible}>
                    <CustomSelect
                        disabled={isQuestionsInputDisabled}
                        width={900}
                        options={questionOptions}
                        label='Questions'
                        onChange={handleQuestionInputChange}
                        value={questionField.value}
                    />
                </Collapse>
            )}
        </>
    );
};
