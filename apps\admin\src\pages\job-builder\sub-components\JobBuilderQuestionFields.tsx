import { Stack, TextField } from "@mui/material";
import { FieldArray, useField } from "formik";
import { FC } from "react";
import { H3, PrimaryButton, SecondaryButton, useAppText } from "@eva/shared-ui";

interface JobBuilderQuestionFieldsProps {
    disabled: boolean;
    fieldName: string;
    titleKey: string;
    addButtonKey: string;
    removeButtonKey: string;
}

export const JobBuilderQuestionFields: FC<JobBuilderQuestionFieldsProps> = ({
    disabled,
    fieldName,
    titleKey,
    addButtonKey,
    removeButtonKey,
}) => {
    const { getContentForDisplay } = useAppText();
    const [field, _, helpers] = useField(fieldName);
    const questions = field.value as string[];

    const handleInputChange = (index: number, event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const newValues = [...questions];
        newValues[index] = event.target.value;
        helpers.setValue(newValues);
    };

    const values = field.value as string[];

    return (
        <Stack gap={4}>
            <FieldArray
                name={fieldName}
                render={({ push, remove }) => (
                    <Stack gap={2} padding={2}>
                        <PrimaryButton
                            width="fit-content"
                            disabled={disabled}
                            content={getContentForDisplay(addButtonKey)}
                            onClick={() => push("")}
                        />
                        {questions.map((_, index) => (
                            <Stack key={index} direction="row" gap={2}>
                                <TextField
                                    fullWidth
                                    value={values[index]}
                                    onChange={(event) => handleInputChange(index, event)}
                                />
                                <SecondaryButton
                                    content={getContentForDisplay(removeButtonKey)}
                                    onClick={() => remove(index)}
                                />
                            </Stack>
                        ))}
                    </Stack>
                )}
            />
        </Stack>
    );
};
