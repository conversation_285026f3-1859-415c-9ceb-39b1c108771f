import React from 'react';
import { Box, Typography, Button, styled } from '@mui/material';
import InboxIcon from '@mui/icons-material/Inbox';
import { useTheme } from '@mui/material/styles';

interface NoDataMessageProps {
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
}

const NoDataContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(4),
  textAlign: 'center',
  height: '100%',
  minHeight: '200px',
  width: '100%',
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  color: theme.palette.grey[500],
}));

export const NoDataMessage: React.FC<NoDataMessageProps> = ({ 
  message = "No data available", 
  actionLabel,
  onAction 
}) => {
  const theme = useTheme();

  return (
    <NoDataContainer>
      <IconWrapper>
        <InboxIcon sx={{ fontSize: 64 }} />
      </IconWrapper>
      <Typography variant="h5" gutterBottom sx={{ color: theme.palette.text.primary }}>
        No Data Found
      </Typography>
      <Typography variant="body1" color="textSecondary" sx={{ maxWidth: '500px', mb: 3 }}>
        {message}
      </Typography>
      {onAction && actionLabel && (
        <Button 
          variant="outlined" 
          color="primary" 
          onClick={onAction}
        >
          {actionLabel}
        </Button>
      )}
    </NoDataContainer>
  );
}; 