import { GridColDef, GridRowSelectionModel } from "@mui/x-data-grid";
import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { CMCandidate } from "@eva/shared-common";
import { CustomDataGridClient } from "@eva/shared-ui";
import { formatDate } from "@eva/shared-ui";
import { CandidateScore } from "./CandidateScore";
import { CandidateStatus } from "./CandidateStatus";
import { DownloadReportComponent } from "./DownloadReportComponent";

const columns = (shouldShowJobTitle: boolean): GridColDef[] => {
    const columns: GridColDef[] = [
        {
            field: "name",
            headerName: "Candidate Name",
            type: "string",
            width: 250,
            disableColumnMenu: true,
            renderCell: (params) => <Link to={`/candidates/${params.row.id}`}>{params.row.name}</Link>,
        },
        {
            field: "createdAt",
            headerName: "Created At",
            type: "string",
            disableColumnMenu: true,
            width: 200,
            renderCell: (params) => (params.value ? formatDate(params.value) : ""),
        },
        {
            field: "interviewStatus",
            headerName: "Status",
            type: "string",
            width: 200,
            disableColumnMenu: true,
            sortable: false,
            renderCell: (params) => <CandidateStatus candidateId={params.row.id} />,
        },
    ];

    if (shouldShowJobTitle) {
        columns.splice(1, 0, {
            field: "jobTitle",
            headerName: "Job Title",
            type: "string",
            width: 250,
            disableColumnMenu: true,
        });
    }

    columns.push({
        field: "score",
        headerName: "Score",
        type: "string",
        width: 100,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) => <CandidateScore candidateId={params.row.id} />,
    });
    columns.push({
        field: "downloadReport",
        headerName: "",
        type: "string",
        width: 150,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) => <DownloadReportComponent candidateName={params.row.name} candidateId={params.row.id} />,
    });

    return columns;
};

interface CandidatesDataTableProps {
    candidates: CMCandidate[];
    height?: string;
    handleSelect?: (selectedIds: GridRowSelectionModel) => void;
    selectionModel?: GridRowSelectionModel;
    shouldShowJobTitle?: boolean;
}

export const CandidatesDataTable: FC<CandidatesDataTableProps> = ({
    candidates,
    height = "80dvh",
    handleSelect,
    selectionModel,
    shouldShowJobTitle = false,
}) => {
    return (
        <CustomDataGridClient
            enableSelection={!!handleSelect}
            handleSelect={handleSelect}
            selectionModel={selectionModel}
            tableName="candidates-table"
            defaultSortField="createdAt"
            rows={candidates}
            columns={columns(shouldShowJobTitle)}
            height={height}
        />
    );
};
