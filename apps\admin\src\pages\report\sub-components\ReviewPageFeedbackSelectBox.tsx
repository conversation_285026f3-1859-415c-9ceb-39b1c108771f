import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
} from "@mui/material";
import React from "react";

const options = [
    { value: "", label: "Select" },
    { value: "good_response", label: "Good Response" },
    {
        value: "response_indicates_technical_issues",
        label: "Response indicates technical issues",
    },
    {
        value: "response_does_not_indicate_good_behavior_and_style",
        label: "Response does not indicate good behavior and style",
    },
    {
        value: "response_does_not_indicate_anything_useful",
        label: "Response does not indicate anything useful",
    },
];

interface ReviewPageFeedbackSelectBoxProps {
    feedback?: string;
    handleFeedbackChange: (feedback: string) => void;
}

export const ReviewPageFeedbackSelectBox: React.FC<
    ReviewPageFeedbackSelectBoxProps
> = ({ handleFeedbackChange, feedback }) => {
    const handleChange = (event: SelectChangeEvent) => {
        handleFeedbackChange(event.target.value as string);
    };

    return (
        <FormControl fullWidth>
            <InputLabel id="select-label">Feedback</InputLabel>
            <Select
                labelId="select-label"
                value={feedback ? feedback : ""}
                onChange={handleChange}
                label="Feedback"
            >
                {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    );
};
