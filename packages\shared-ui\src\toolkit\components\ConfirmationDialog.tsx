import WarningIcon from "@mui/icons-material/Warning";
import { Dialog, DialogActions, DialogContent, DialogTitle, Paper, Stack, styled } from "@mui/material";
import React from "react";
import { H5 } from "../theme/Typography";
import { PrimaryButton } from "./buttons/PrimaryButton";
import { SecondaryButton } from "./buttons/SecondaryButton";

const StyledDialogTitle = styled(DialogTitle)({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
});

interface ConfirmationDialogProps {
    isOpen: boolean;
    message: string;
    onClose: () => void;
    onSubmit: () => void;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({ isOpen, message, onClose, onSubmit }) => {
    return (
        <Dialog open={isOpen} onClose={onClose}>
            <Paper>
                <Stack padding={4}>
                    <StyledDialogTitle>
                        <WarningIcon color="warning" />
                    </StyledDialogTitle>
                    <DialogContent>
                        <H5>{message}</H5>
                    </DialogContent>
                    <DialogActions>
                        <SecondaryButton content="Back" data-qa="back-button" onClick={onClose} />
                        <PrimaryButton content="Confirm" data-qa="submit-button" onClick={onSubmit} />
                    </DialogActions>
                </Stack>
            </Paper>
        </Dialog>
    );
};

