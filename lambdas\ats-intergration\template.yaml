AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
    ats-integration-api

Globals:
    Function:
        Timeout: 25
        Tracing: Active
    Api:
        TracingEnabled: true

Parameters:
    Env:
        Type: String
        Default: staging
        AllowedValues:
            - dev
            - staging
            - prod
    Region:
        Type: String
        Default: us-east-1
        AllowedValues:
            - us-east-1

Resources:
    ATSIntegrationFunction:
        Type: AWS::Serverless::Function
        Properties:
            FunctionName: !Sub "${Env}-ats-integration"
            MemorySize: 256
            CodeUri: src/dist/
            Handler: app.lambdaHandler
            Runtime: nodejs20.x
            Environment:
                Variables:
                    ENV: !Ref Env
                    REGION: !Ref Region
            Architectures:
                - x86_64
            Events:
                CreateCustomerEvent:
                    Type: Api
                    Properties:
                        Path: /accounts/{accountId}/customers
                        Method: post
                        RestApiId: !Ref ServerlessRestApi
                        Auth:
                            ApiKeyRequired: true
                CreateJobEvent:
                    Type: Api
                    Properties:
                        Path: /customers/{customerId}/jobs
                        Method: post
                        RestApiId: !Ref ServerlessRestApi
                        Auth:
                            ApiKeyRequired: true
            Policies:
                - Statement:
                      Effect: Allow
                      Action:
                          - secretsmanager:GetSecretValue
                      Resource:
                          - !Sub arn:aws:secretsmanager:us-east-1:************:secret:${Env}/iverse-db/postgres-*
                          - !Sub arn:aws:secretsmanager:us-east-1:************:secret:${Env}/iverse-db-*
                - Statement:
                      Effect: Allow
                      Action:
                          - sqs:SendMessage
                          - sqs:ReceiveMessage
                          - sqs:DeleteMessage
                          - sqs:GetQueueAttributes
                      Resource: !Sub "arn:aws:sqs:${AWS::Region}:************:${Env}_analyzer.fifo"
                - Statement:
                      Effect: Allow
                      Action:
                          - s3:GetObject
                          - s3:ListBucket
                      Resource:
                          - "arn:aws:s3:::iverse-integration-credentials"
                          - "arn:aws:s3:::iverse-integration-credentials/*"
                - Statement:
                      Effect: Allow
                      Action:
                          - sns:Publish
                      Resource: !Sub arn:aws:sns:${AWS::Region}:************:analyzer_${Env}_errors
            VpcConfig:
                SecurityGroupIds:
                    - sg-0e5ec430bb34861cd
                SubnetIds:
                    - subnet-0c7f1790620ba14cd
                    - subnet-0dcbafdbc0f986f26
                    - subnet-0d04bdd454254e45f
      

Outputs:
    ATSIntegrationApi:
        Description: API Gateway endpoint URL for the ATSIntegration function
        Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/ats"

    ATSIntegrationFunction:
        Description: ATSIntegration Lambda Function ARN
        Value: !GetAtt ATSIntegrationFunction.Arn

    ATSIntegrationFunctionIamRole:
        Description: IAM Role created for ATSIntegration function
        Value: !GetAtt ATSIntegrationFunctionRole.Arn
