import { Image, Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../PDF.styles";

interface PerformanceSectionProps {
    isRtl: boolean;
    candidateName: string;
    chartImage: string;
}

export const PerformanceSection: FC<PerformanceSectionProps> = ({ isRtl, candidateName, chartImage }) => {
    const headerText = isRtl ? "מצולע ביצועים" : "Performance Polygon";

    return (
        <View style={[styles.polygonContainer]}>
            <View
                style={[
                    styles.fullWidth,
                    styles.displayFlex,
                    styles.flewDirectionRow,
                    styles.justifyStart,
                    styles.paddingHorizonta10,
                    isRtl ? styles.justifyEnd : styles.justifyStart,
                ]}
            >
                <Text style={isRtl ? styles.hebrewTextBoldMainHeader : styles.textBoldMainHeader}>{headerText}</Text>
            </View>
            <Image source={chartImage} style={{ width: 450, height: 400 }} />
            
        </View>
    );
};
