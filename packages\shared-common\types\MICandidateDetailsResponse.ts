export interface MICandidateDetailsResponse {
    statusCode: number;
    errorCode: number;
    callId: string;
    statusReason: string;
    time: string;
    data: MICandidateDetailsData;
}
export interface MICandidateDetailsData {
    apiKey: string;
    company_id: string;
    location: string;
    creatorId: string;
    candidate_id: string;
    email: string;
    job_id: string;
    status: string;
    username: string;
    link: string;
    communicationPreference: string[];
    isPublic: boolean;
    api_created: boolean;
    enableNotifications: boolean;
    jobTitle: string;
    statusId: string;
    kanban_column_name: string;
    kanban_order: number;
    creator_name: string;
    time_viewed: number;
    date: string;
    auto_rejected: boolean;
    createdAt: string;
    updatedAt: string;
    attachments: any[];
    lastCandidateActivity: string;
    video_id: string;
    completedAt: string;
    progress: number;
    thumb: string;
    parsedName: {
        firstname: string;
        lastname: string;
    };
}