import { ReportVersion } from "../types/AnalyzerReportData";

export const isThereDiffsInReport = (reviewReport: ReportVersion, originalReport: ReportVersion): boolean => {
    const reviewTraits = reviewReport.traits;
    const originalTraits = originalReport.traits;

    for (let i = 0; i < reviewTraits.length; i++) {
        if (reviewTraits[i].score !== originalTraits[i].score) {
            return true;
        }
    }

    const reviewFamilies = reviewReport.families;
    const originalFamilies = originalReport.families;

    for (let i = 0; i < reviewFamilies.length; i++) {
        if (reviewFamilies[i].summary !== originalFamilies[i].summary) {
            return true;
        }
    }
    
    return false;
};
