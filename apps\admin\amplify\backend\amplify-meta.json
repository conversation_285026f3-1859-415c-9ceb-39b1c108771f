{"providers": {"awscloudformation": {"AuthRoleName": "amplify-iverseadminclient-staging-e62dc-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-staging-e62dc-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-staging-e62dc-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-iverseadminclient-staging-e62dc-deployment", "UnauthRoleName": "amplify-iverseadminclient-staging-e62dc-unauthRole", "StackName": "amplify-iverseadminclient-staging-e62dc", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-iverseadminclient-staging-e62dc/8cdb6330-4546-11ef-b611-0e0a5af16d8f", "AmplifyAppId": "d2trb4<PERSON>nygq6", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}}, "api": {"IverseAdminExpressApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "IverseAdminExpressFunction"}], "providerPlugin": "awscloudformation", "service": "API Gateway", "output": {"ApiName": "IverseAdminExpressApi", "RootUrl": "https://dvr2i04ycc.execute-api.us-east-1.amazonaws.com/staging", "ApiId": "dvr2i04ycc"}, "lastPushTimeStamp": "2025-04-24T08:43:24.968Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/api/IverseAdminExpressApi-cloudformation-template.json", "logicalId": "apiIverseAdminExpressApi"}}}, "auth": {"iverseadminclient": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyMinLength": 8, "passwordPolicyCharacters": []}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed", "output": {"UserPoolId": "us-east-1_szReER0h5", "AppClientIDWeb": "4a3fomj3u7f6vp6riaqavonkg3", "AppClientID": "6j3vu4jpj5612p4of8ok38nfb5", "IdentityPoolId": "us-east-1:b3b21898-65b1-4fde-abd3-024b2906fe2c", "UserPoolArn": "arn:aws:cognito-idp:us-east-1:405424696292:userpool/us-east-1_szReER0h5", "IdentityPoolName": "iverseadminclient_identitypool_90996916__staging", "UserPoolName": "iverseadminclient_userpool_90996916"}, "lastPushTimeStamp": "2025-04-24T08:43:24.970Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/auth/iverseadminclient-cloudformation-template.json", "logicalId": "authiverseadminclient"}}, "userPoolGroups": {"dependsOn": [{"category": "auth", "resourceName": "iverseadminclient", "attributes": ["UserPoolId", "AppClientIDWeb", "AppClientID", "IdentityPoolId"]}], "providerPlugin": "awscloudformation", "service": "Cognito-UserPool-Groups", "output": {"psychologistsGroupRole": "arn:aws:iam::405424696292:role/us-east-1_szReER0h5-psychologistsGroupRole"}, "lastPushTimeStamp": "2025-04-24T08:43:25.704Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/auth/userPoolGroups-cloudformation-template.json", "logicalId": "authuserPoolGroups"}}}, "function": {"IverseAdminExpressFunction": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::405424696292:role/iverseadminclientLambdaRole29b4ae0b-staging", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:405424696292:function:IverseAdminExpressFunction-staging", "Name": "IverseAdminExpressFunction-staging", "LambdaExecutionRole": "iverseadminclientLambdaRole29b4ae0b-staging"}, "lastPushTimeStamp": "2025-04-24T08:43:24.972Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/function/IverseAdminExpressFunction-cloudformation-template.json", "logicalId": "functionIverseAdminExpressFunction"}, "lastBuildTimeStamp": "2025-04-24T08:16:14.466Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-04-24T08:16:14.535Z", "distZipFilename": "IverseAdminExpressFunction-7a41593959444e314475-build.zip", "s3Bucket": {"deploymentBucketName": "amplify-iverseadminclient-staging-e62dc-deployment", "s3Key": "amplify-builds/IverseAdminExpressFunction-7a41593959444e314475-build.zip"}, "lastPushDirHash": "k9hm6WMKBljxDztTN82z36YdbXM="}}, "hosting": {"amplifyhosting": {"providerPlugin": "awscloudformation", "service": "amplifyhosting", "type": "manual", "output": {}, "lastPushTimeStamp": "2025-04-24T08:43:24.973Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/hosting/amplifyhosting-template.json", "logicalId": "hostingamplifyhosting"}}}, "storage": {"uploads": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3", "output": {"BucketName": "iverse-admin-uploadse62dc-staging", "Region": "us-east-1"}, "lastPushTimeStamp": "2025-04-24T08:43:25.813Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/storage/cloudformation-template.json", "logicalId": "storageuploads"}}}}