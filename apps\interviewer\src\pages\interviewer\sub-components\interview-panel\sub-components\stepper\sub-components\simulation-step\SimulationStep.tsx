import { Stack } from "@mui/material";
import { useLayoutEffect, useState } from "react";
import { useInterviewer } from "../../../../../../InterviewerProvider";
import { SimulationControlButton } from "./sub-components/SimulationControlButton";
import { SimulationStepExplanation } from "./sub-components/SimulationStepExplanation";
import { SimulationStepQuestionContent } from "./sub-components/SimulationStepQuestionContent";
import { Body2, NoCopyWrapper, useMediaQueryContext } from "@eva/shared-ui";
import { motion, AnimatePresence } from "framer-motion";
import { styled } from "@mui/material/styles";

const containerAnimation = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.4, staggerChildren: 0.1 } },
    exit: { opacity: 0, transition: { duration: 0.3 } },
};

const slideAnimation = {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0, transition: { duration: 0.5, ease: "easeOut" } },
    exit: { opacity: 0, x: -30, transition: { duration: 0.3 } },
};

const fadeUpAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.4 } },
    exit: { opacity: 0, y: 10, transition: { duration: 0.3 } },
};

const MobileButtonContainer = styled('div')({
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    padding: '0 16px',
    backgroundColor: 'white',
    zIndex: 100,
});

export type SimulationScreen = "explanation" | "story" | "question";

export const SimulationStep = () => {
    const { data, currentStep, currentSimulationScreen } = useInterviewer();
    const { isMobile } = useMediaQueryContext();

    const [screen, setScreen] = useState<SimulationScreen>(currentSimulationScreen);
    const [isStoryVideoDone, setIsStoryVideoDone] = useState(false);
    const [isQuestionPreviewDone, setIsQuestionPreviewDone] = useState(false);

    const question = currentStep?.simulation?.question.text;
    const questionVideoLink = currentStep?.simulation?.question.videoLink;
    const story = currentStep?.simulation?.story;
    const storyVideoLink = currentStep?.simulation?.videoLink;

    useLayoutEffect(() => {
        setIsQuestionPreviewDone(false);
    }, [question, questionVideoLink]);

    useLayoutEffect(() => {
        setIsStoryVideoDone(!storyVideoLink);
    }, [storyVideoLink]);

    const goToScreen = (targetScreen: SimulationScreen) => {
        setScreen(targetScreen);
    };


    const onQuestionVideoEnd = () => {
        setIsQuestionPreviewDone(true);
    };

    const renderContent = () => {
        switch (screen) {
            case "explanation":
                return (
                    <motion.div
                        key="explanation"
                        variants={slideAnimation}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                    >
                        <SimulationStepExplanation />
                    </motion.div>
                );
            case "story":
                return story ? (
                    <motion.div key="story" variants={slideAnimation} initial="initial" animate="animate" exit="exit">
                        <Stack
                            alignItems="center"
                            justifyContent="center"
                            gap="20px"
                            position="relative"
                            paddingInline={isMobile ? 20 : 10}
                            overflow="auto"
                            maxHeight={isMobile ? "35dvh" : "40dvh"}
                            paddingTop={4}
                            paddingBottom={isMobile ? 10 : 4}
                        >
                            <NoCopyWrapper>
                                <Body2>{story}</Body2>
                            </NoCopyWrapper>
                        </Stack>
                    </motion.div>
                ) : null;
            case "question":
                return question ? (
                    <motion.div
                        key="question"
                        variants={slideAnimation}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                        style={{ marginBottom: isMobile ? '80px' : '0' }}
                    >
                        <SimulationStepQuestionContent
                            notifyOnVideoEnd={onQuestionVideoEnd}
                            isVideoQuestionPreviewDone={isQuestionPreviewDone}
                            content={question}
                            videoUrl={questionVideoLink || null}
                        />
                    </motion.div>
                ) : null;
            default:
                return null;
        }
    };

    return (
        data && (
            <motion.div 
                variants={containerAnimation} 
                initial="initial" 
                animate="animate" 
                exit="exit"
                style={{ position: 'relative', height: '100%' }}
            >
                <AnimatePresence mode="wait">{renderContent()}</AnimatePresence>
                
                {isMobile ? (
                    <MobileButtonContainer>
                        <SimulationControlButton
                            currentScreen={screen}
                            goToScreen={goToScreen}
                            isStoryVideoDone={isStoryVideoDone}
                            hasStoryVideo={!!storyVideoLink}
                        />
                    </MobileButtonContainer>
                ) : (
                    <motion.div variants={fadeUpAnimation}>
                        <SimulationControlButton
                            currentScreen={screen}
                            goToScreen={goToScreen}
                            isStoryVideoDone={isStoryVideoDone}
                            hasStoryVideo={!!storyVideoLink}
                        />
                    </motion.div>
                )}
            </motion.div>
        )
    );
};
