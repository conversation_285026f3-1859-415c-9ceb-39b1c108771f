import { QuestionVideo, QuestionVideosResponse } from "@eva/shared-common";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { IconButton, SelectChangeEvent } from "@mui/material";
import { FC, useEffect, useState } from "react";
import { CustomSelect, CustomTextField, DataTableFilterWrapper } from "@eva/shared-ui";


interface QuestionsCardsListFilterFields {
    searchText: string;
    customer: string;
    trait: string;
}

interface QuestionsCardsListFilterProps {
    questionsVideosData: QuestionVideosResponse;
    handleVideosChange: (filterFields: QuestionVideo[]) => void;
}

export const QuestionsCardsListFilter: FC<QuestionsCardsListFilterProps> = ({ questionsVideosData, handleVideosChange }) => {
    const [filteredCount, setFilteredCount] = useState<number>(questionsVideosData.videos.length);
    const [filterFields, setFilterFields] = useState<QuestionsCardsListFilterFields>({
        searchText: "",
        customer: "",
        trait: "",
    });

    const handleSearchChange = (event: React.ChangeEvent<{ value: string }>) => {
        setFilterFields((prev) => ({ ...prev, searchText: event.target.value }));
    };

    const handleCustomerChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, customer: event.target.value }));
    };

    const handleGenderChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, gender: event.target.value }));
    };

    const handleTraitChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, trait: event.target.value }));
    };

    const handleLanguageChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, language: event.target.value }));
    };

    const uniqueCustomers = [...new Set(questionsVideosData.videos.map((video) => video.customerName))];
    const uniqueTraits = [...new Set(questionsVideosData.videos.map((video) => video.trait))];

    const customerOptions = [
        {
            value: "",
            label: "All",
        },
        ...uniqueCustomers.map((customer) => ({
            value: customer,
            label: customer,
        })),
    ];
    const traitOptions = [
        {
            value: "",
            label: "All",
        },
        ...uniqueTraits.map((trait) => ({
            value: trait,
            label: trait,
        })),
    ];

    const handleReset = () => {
        setFilterFields({
            searchText: "",
            customer: "",
            trait: "",
        });
    };

    useEffect(() => {
        const filteredVideos = questionsVideosData.videos.filter(
            (video) =>
                video.customerName.toLowerCase().includes(filterFields.customer.toLowerCase()) &&
                video.trait.toLowerCase().includes(filterFields.trait.toLowerCase()) &&
                video.question.toLowerCase().includes(filterFields.searchText.toLowerCase())
        );
        handleVideosChange(filteredVideos);
        setFilteredCount(filteredVideos.length);
    }, [filterFields, questionsVideosData]);
    return (
        <DataTableFilterWrapper resultsCount={filteredCount}>
            <CustomTextField width={400} label='Search' value={filterFields.searchText} onChange={handleSearchChange} />
            <CustomSelect width={200} label='Customer' value={filterFields.customer} onChange={handleCustomerChange} options={customerOptions} />
            <CustomSelect width={200} label='Trait' value={filterFields.trait} onChange={handleTraitChange} options={traitOptions} />
            <IconButton onClick={handleReset}>
                <RestartAltIcon />
            </IconButton>
        </DataTableFilterWrapper>
    );
};
