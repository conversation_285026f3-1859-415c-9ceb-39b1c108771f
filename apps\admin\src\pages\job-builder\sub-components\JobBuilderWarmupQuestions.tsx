import { FC } from "react";
import { JobBuilderQuestionFields } from "./JobBuilderQuestionFields";

interface JobBuilderWarmupQuestionsProps {
    disabled: boolean;
}

export const JobBuilderWarmupQuestions: FC<JobBuilderWarmupQuestionsProps> = ({ disabled }) => {
    return (
        <JobBuilderQuestionFields 
            disabled={disabled}
            fieldName="warmupQuestions"
            titleKey="jobBuilderForm.warmupQuestions.title"
            addButtonKey="jobBuilderForm.warmupQuestions.addQuestion"
            removeButtonKey="jobBuilderForm.warmupQuestions.removeQuestion"
        />
    );
};
