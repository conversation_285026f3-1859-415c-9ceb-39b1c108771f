import { createDefaultInvitationTemplates, JobInvitationData } from "@eva/shared-common";
import { Body2, H3, H6 } from "@eva/shared-ui";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { Box, Chip, Divider, IconButton, Paper, Stack, styled, TextField, Tooltip } from "@mui/material";
import { Form, useFormikContext } from "formik";
import React from "react";
import { PrimaryButton } from "@eva/shared-ui";

interface EditInvitationFormBodyProps {
    onCancel: () => void;
    jobLanguage: string;
}

const ContentStack = styled(Stack)(({ theme }) => ({
    spacing: 6,
    gap: theme.spacing(4),
}));

const TagsContainer = styled(Stack)(({ theme }) => ({
    flexDirection: "row",
    flexWrap: "wrap",
    gap: theme.spacing(1.5),
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
}));

const TemplateContainer = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    border: `1px solid ${theme.palette.divider}`,
}));

const TemplateTextField = styled(TextField)(({ theme }) => ({
    marginTop: theme.spacing(3),
}));

const ActionButtons = styled(Stack)(({ theme }) => ({
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: theme.spacing(2),
    marginTop: theme.spacing(2),
}));

const SectionTitle = styled(H6)({
    marginBottom: "16px",
});

const AVAILABLE_TAGS = [
    { id: "name", label: "Candidate Name", value: "[name]", description: "The candidate's full name" },
    { id: "job", label: "Job Title", value: "[job]", description: "The job title" },
    { id: "company", label: "Company Name", value: "[company]", description: "Your company name" },
];

const LabelContainer = styled(Box)({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
});

const TagsInfo = styled(Box)(({ theme }) => ({
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(2),
    color: theme.palette.text.primary,
    fontSize: "0.875rem",
}));

export const EditInvitationFormBody: React.FC<EditInvitationFormBodyProps> = ({ onCancel, jobLanguage }) => {
    const formikProps = useFormikContext<JobInvitationData>();
    const { values, touched, errors, handleChange, handleBlur, isSubmitting, handleSubmit } = formikProps;

    const language = jobLanguage === "he" ? "he" : "en";

    const insertTag = (field: string, tag: string) => {
        const currentValue = values[field as keyof JobInvitationData];

        if (typeof currentValue === "string") {
            formikProps.setFieldValue(field, currentValue + tag);
        } else if (typeof currentValue === "object") {
            const cursorPosition = (document.activeElement as HTMLTextAreaElement)?.selectionStart || 0;
            const currentText = currentValue[language];
            const newText = currentText.substring(0, cursorPosition) + tag + currentText.substring(cursorPosition);

            formikProps.setFieldValue(`${field}.${language}`, newText);
        }
    };

    const handleTagClick = (tag: { id: string; label: string; value: string }) => {
        const activeField = document.activeElement?.id;
        if (activeField?.includes("mail")) {
            insertTag("mailTemplateOption", tag.value);
        } else if (activeField?.includes("sms")) {
            insertTag("smsTemplateOption", tag.value);
        }
    };

    const handleSaveClick = () => {
        handleSubmit();
    };

    const handleResetEmailTemplate = () => {
        formikProps.setFieldValue(`mailTemplateOption.${language}`, createDefaultInvitationTemplates().mail[language]);
    };

    const handleResetSmsTemplate = () => {
        formikProps.setFieldValue(`smsTemplateOption.${language}`, createDefaultInvitationTemplates().sms[language]);
    };

    return (
        <Form>
            <ContentStack>
                <H3>Edit Invitation Templates</H3>
                <TagsInfo>
                    These templates will be used when inviting candidates. The available tags will be replaced with the
                    actual values when sending invitations.
                </TagsInfo>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ my: 3 }}>
                    <TemplateContainer elevation={0}>
                        <LabelContainer>
                            <Body2>{language === "he" ? "Email Template (Hebrew)" : "Email Template (English)"}</Body2>
                            <Tooltip title="Reset to default template">
                                <IconButton onClick={handleResetEmailTemplate} size="small">
                                    <RestartAltIcon />
                                </IconButton>
                            </Tooltip>
                        </LabelContainer>
                        <TemplateTextField
                            id={`mailTemplateOption.${language}`}
                            name={`mailTemplateOption.${language}`}
                            value={values.mailTemplateOption[language]}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                                touched.mailTemplateOption?.[language] && Boolean(errors.mailTemplateOption?.[language])
                            }
                            helperText={touched.mailTemplateOption?.[language] && errors.mailTemplateOption?.[language]}
                            multiline
                            rows={8}
                            fullWidth
                            variant="outlined"
                            dir={language === "he" ? "rtl" : "ltr"}
                        />
                    </TemplateContainer>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ my: 3 }}>
                    <TemplateContainer elevation={0}>
                        <LabelContainer>
                            <Body2>{language === "he" ? "SMS Template (Hebrew)" : "SMS Template (English)"}</Body2>
                            <Tooltip title="Reset to default template">
                                <IconButton onClick={handleResetSmsTemplate} size="small">
                                    <RestartAltIcon />
                                </IconButton>
                            </Tooltip>
                        </LabelContainer>
                        <TemplateTextField
                            id={`smsTemplateOption.${language}`}
                            name={`smsTemplateOption.${language}`}
                            value={values.smsTemplateOption[language]}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                                touched.smsTemplateOption?.[language] && Boolean(errors.smsTemplateOption?.[language])
                            }
                            helperText={touched.smsTemplateOption?.[language] && errors.smsTemplateOption?.[language]}
                            multiline
                            rows={3}
                            fullWidth
                            variant="outlined"
                            dir={language === "he" ? "rtl" : "ltr"}
                        />
                    </TemplateContainer>
                </Box>

                <Box sx={{ mb: 2 }}>
                    <SectionTitle color="textPrimary">Available Tags</SectionTitle>
                    <TagsContainer>
                        {AVAILABLE_TAGS.map((tag) => (
                            <Tooltip key={tag.id} title={`${tag.description} - Insert with: ${tag.value}`} arrow>
                                <Chip
                                    label={`${tag.label} ${tag.value}`}
                                    color="primary"
                                    onClick={() => handleTagClick(tag)}
                                />
                            </Tooltip>
                        ))}
                    </TagsContainer>
                </Box>

                <ActionButtons>
                    <PrimaryButton onClick={onCancel} content="Cancel" variant="outlined" />
                    <PrimaryButton
                        onClick={handleSaveClick}
                        content="Save Changes"
                        disabled={isSubmitting}
                        isLoading={isSubmitting}
                    />
                </ActionButtons>
            </ContentStack>
        </Form>
    );
};
