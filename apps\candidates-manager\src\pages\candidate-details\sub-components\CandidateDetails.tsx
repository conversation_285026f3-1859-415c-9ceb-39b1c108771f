import { H3, H4, PageLayout } from "@eva/shared-ui";
import { Assessment, Download } from "@mui/icons-material";
import { Box, Grid, Stack, styled } from "@mui/material";
import { FC } from "react";
import { useGetCandidateByIdQuery } from "../../../app-state/apis/candidatesManagerApi";
import { CandidateScore } from "../../candidates/sub-components/CandidateScore";
import { DownloadReportComponent } from "../../candidates/sub-components/DownloadReportComponent";
import { JobInfoSection } from "./JobInfoSection";
import { LoadingSkeleton } from "./LoadingSkeleton";
import { MetadataItem } from "./MetadataItem";
import { PersonalInfoSection } from "./PersonalInfoSection";
import { ProfileHeader } from "./ProfileHeader";

interface CandidateDetailsComponentProps {
    candidateId: string;
}

const DetailsSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
}));

export const CandidateDetails: FC<CandidateDetailsComponentProps> = ({ candidateId }) => {
    const { data: candidate, isLoading } = useGetCandidateByIdQuery(candidateId, {
        skip: !candidateId,
    });

    if (isLoading) {
        return <LoadingSkeleton />;
    }

    if (!candidate) {
        return <H3 color="error">Candidate not found</H3>;
    }

    return (
        <PageLayout>
            <ProfileHeader name={candidate.name} imageLink={candidate.imageLink} />

            <DetailsSection>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <PersonalInfoSection
                            email={candidate.email || ""}
                            phoneNumber={candidate.phoneNumber}
                            gender={candidate.gender}
                            socialId={candidate.socialId}
                        />
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <JobInfoSection jobTitle={candidate.jobTitle} createdAt={candidate.createdAt} />
                    </Grid>
                    <Grid item xs={12}>
                        <Stack spacing={2}>
                            <H4>Results</H4>
                            <MetadataItem
                                icon={<Assessment fontSize="small" />}
                                label="Score"
                                value={<CandidateScore candidateId={candidateId} />}
                            />
                            <MetadataItem
                                icon={<Download fontSize="small" />}
                                label="Report"
                                value={
                                    <DownloadReportComponent candidateId={candidateId} candidateName={candidate.name} />
                                }
                            />
                        </Stack>
                    </Grid>
                </Grid>
            </DetailsSection>
        </PageLayout>
    );
};
