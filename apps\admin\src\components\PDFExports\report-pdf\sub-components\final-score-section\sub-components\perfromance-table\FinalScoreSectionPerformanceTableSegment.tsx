import { View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../../../PDF.styles";
import { determineColor } from "../../../../../PDF.utils";

interface FinalScoreSectionPerformanceTableSegmentProps {
    segment: number;
    score: number;
    isRtl: boolean;
}

const calculateFillPercentage = (segment: number, score: number) => {
    if (score >= segment) {
        return 100;
    } else if (score < segment - 1) {
        return 0;
    } else {
        return (score - (segment - 1)) * 100;
    }
};

const determineRoundedBorders = (segment: number, isRtl: boolean) => {
    if (segment === 1) {
        return isRtl
            ? { borderTopRightRadius: 10, borderBottomRightRadius: 10 }
            : { borderTopLeftRadius: 10, borderBottomLeftRadius: 10 };
    } else if (segment === 5) {
        return isRtl
            ? { borderTopLeftRadius: 10, borderBottomLeftRadius: 10 }
            : { borderTopRightRadius: 10, borderBottomRightRadius: 10 };
    } else {
        return {};
    }
};

const determineSegmentWidth = (segment: number) => {
    if (segment === 1) {
        return "85%";
    } else if (segment === 5) {
        return "85%";
    } else {
        return "98%";
    }
};

export const FinalScoreSectionPerformanceTableSegment: FC<FinalScoreSectionPerformanceTableSegmentProps> = ({
    segment,
    score,
    isRtl,
}) => {
    const fillColor = determineColor(score);
    const fillPercentage = calculateFillPercentage(segment, score);

    return (
        <View
            style={[
                { height: 12, backgroundColor: "#f0f0f0", overflow: "hidden" },
                determineRoundedBorders(segment, isRtl),
                { width: determineSegmentWidth(segment) },
                styles.displayFlex,
                isRtl ? styles.flexReverse : {},
            ]}
        >
            <View
                style={[
                    
                    {
                        width: `${fillPercentage}%`,
                        backgroundColor: fillColor,
                        height: 15,
                    },
                ]}
            />
        </View>
    );
};
