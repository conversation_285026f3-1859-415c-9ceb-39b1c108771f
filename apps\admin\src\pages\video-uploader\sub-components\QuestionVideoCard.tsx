import { AbsolutePositionWrapper, Body2, ConfirmationDialog, DeleteButton, H5 } from "@eva/shared-ui";
import { Card, CardContent, CardMedia, Grid, Stack, Tooltip, capitalize } from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { Suspense, useState } from "react";
import { useDeleteQuestionVideoMutation } from "../../../app-state/apis";
import { languageMapper } from "../../../app-state/appText";

const StyledCard = styled(Card)({
    width: "100%",
    display: "flex",
    flexDirection: "column",
});

interface CardComponentProps {
    videoUrl: string;
    customerName: string;
    trait: string;
    content: string;
    gender: string | null;
    language: string;
    videoId: string;
}

export const QuestionVideoCard: React.FC<CardComponentProps> = ({ language, gender, videoUrl, customerName, trait, content, videoId }) => {
    const [deleteVideo] = useDeleteQuestionVideoMutation();
    const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
    const handleDeleteClick = async () => {
        setIsConfirmationModalOpen((prev) => !prev);
    };

    const handleDeleteConfirmation = async () => {
        await deleteVideo(videoId);
        setIsConfirmationModalOpen(false);
    };

    const deleteMessage = "Are you sure you want to delete this video?";

    return (
        <>
            <Grid item width={400} position='relative'>
                <AbsolutePositionWrapper right='-15px' top='25px' zIndex={10}>
                    <DeleteButton onClick={handleDeleteClick} />
                </AbsolutePositionWrapper>
                <StyledCard key={videoUrl}>
                    <CardMedia component='video' src={videoUrl} controls />
                    <Tooltip title={content} placement='bottom'>
                        <CardContent>
                            <Stack direction='row' gap={1}>
                                <H5>Customer:</H5>
                                <Body2 color='primary'>{customerName}</Body2>
                            </Stack>
                            <Stack direction='row' gap={1}>
                                <H5>Trait:</H5>
                                <Body2 color='secondary'>{trait}</Body2>
                            </Stack>
                            <Stack direction='row' gap={1}>
                                <H5>Gender:</H5>
                                <Body2>{gender ? capitalize(gender) : "All"}</Body2>
                            </Stack>
                            <Stack direction='row' gap={1}>
                                <H5>Language:</H5>
                                <Body2>{languageMapper(language)}</Body2>
                            </Stack>
                        </CardContent>
                    </Tooltip>
                </StyledCard>
            </Grid>
            <Suspense fallback={null}>
                {isConfirmationModalOpen && (
                    <ConfirmationDialog
                        isOpen={isConfirmationModalOpen}
                        onClose={handleDeleteClick}
                        message={deleteMessage}
                        onSubmit={handleDeleteConfirmation}
                    />
                )}
            </Suspense>
        </>
    );
};
