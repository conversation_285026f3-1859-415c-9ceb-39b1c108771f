import React from "react";
import { Stack } from "@mui/material";
import { PrimaryButton } from "@eva/shared-ui";

export interface FooterButtonsProps {
    onCancel: () => void;
    onImport: () => void;
    validCount: number;
    isDisabled: boolean;
    isCreating: boolean;
}

export const FooterButtons: React.FC<FooterButtonsProps> = ({ 
    onCancel, 
    onImport, 
    validCount, 
    isDisabled, 
    isCreating 
}) => (
    <Stack direction="row" spacing={2} justifyContent="flex-end">
        <PrimaryButton 
            variant="outlined" 
            content="Cancel" 
            onClick={onCancel} 
            width="auto" 
        />
        <PrimaryButton
            variant="contained"
            content={isCreating ? "Importing..." : `Import ${validCount} Candidates`}
            onClick={onImport}
            disabled={isDisabled}
            width="auto"
        />
    </Stack>
); 