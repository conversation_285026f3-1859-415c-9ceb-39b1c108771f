import { Stack } from "@mui/material";
import { useLayoutEffect } from "react";
import { H4, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useMediaQueryContext } from "@eva/shared-ui";
import { TalkingHeadAnimation } from "../../../../../../../../../components/TalkingHeadAnimation";
import { VideoPlayer } from "../../../../../../../../../components/video-player/VideoPlayer";
import { useInterviewer } from "../../../../../../../InterviewerProvider";

interface QuestionPreviewContentProps {
    videoUrl?: string | null;
    content: string;
    hideContent?: boolean;
}

export const QuestionStepContent = ({ content, videoUrl, hideContent }: QuestionPreviewContentProps) => {
    const { isMobile } = useMediaQueryContext();
    const { handleCountDownChange } = useInterviewer();
    const onVideoEnd = () => {
        handleCountDownChange(true);
    };

    useLayoutEffect(() => {
        if (!videoUrl) {
            handleCountDownChange(true);
        }
    }, [videoUrl]);

    return (
        <>
            <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
                {!isMobile && (
                    <>
                        {videoUrl ? (
                            <VideoPlayer
                                onVideoEnd={onVideoEnd}
                                data-qa="question-video-preview"
                                width="100%"
                                height="50dvh"
                                videoUrl={videoUrl}
                            />
                        ) : (
                            <TalkingHeadAnimation />
                        )}
                    </>
                )}
                {!hideContent && (
                    <NoCopyWrapper>
                        <H4 data-qa="question-text-preview">
                            {content}
                        </H4>
                    </NoCopyWrapper>
                )}
            </Stack>
        </>
    );
};
