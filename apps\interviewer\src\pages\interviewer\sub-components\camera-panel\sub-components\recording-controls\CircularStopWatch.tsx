import { FC } from "react";
import { CountdownCircleTimer } from "react-countdown-circle-timer";
import { H4 } from "@eva/shared-ui";
import { Stack } from "@mui/material";

interface CircularStopWatchProps {
    duration: number;
    onComplete: () => void;
	text:string
}

export const CircularStopWatch: FC<CircularStopWatchProps> = ({ duration, onComplete,text }) => (
    <CountdownCircleTimer
        isPlaying
        duration={duration}
        colors={["#0000FF", "#3333FF", "#6666FF", "#b4e4ee", "#cbf6ff"]}
        colorsTime={[7, 5, 2, 0]}
        onComplete={onComplete}
        size={150}
    >
        {({ remainingTime }) => (
            <Stack gap={3} alignItems='center'>
                <H4>{text}</H4>
                <H4>{remainingTime}</H4>
            </Stack>
        )}
    </CountdownCircleTimer>
);
