import { FC } from "react";
import { Box, Avatar, styled } from "@mui/material";
import { H3 } from "@eva/shared-ui";

interface ProfileHeaderProps {
    name: string;
    imageLink?: string;
}

const HeaderSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    borderBottom: `1px solid ${theme.palette.divider}`,
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(2),
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
    width: 80,
    height: 80,
    backgroundColor: theme.palette.primary.main,
    fontSize: "2rem",
}));

export const ProfileHeader: FC<ProfileHeaderProps> = ({ name, imageLink }) => {
    const getInitials = () => {
        return `${name.charAt(0)}`.toUpperCase();
    };

    return (
        <HeaderSection>
            {imageLink ? (
                <Avatar src={imageLink} alt={name} sx={{ width: 80, height: 80 }} />
            ) : (
                <ProfileAvatar>{getInitials()}</ProfileAvatar>
            )}
            <H3>{name}</H3>
        </HeaderSection>
    );
}; 