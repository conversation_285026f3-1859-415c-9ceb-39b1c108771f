import { Stack } from "@mui/material";
import { BackButton, PrimaryButton } from "@eva/shared-ui";

interface JobDetailsHeaderProps {
    onOpenInvitation: () => void;
    onOpenBulkImport: () => void;
    onOpenAddCandidate: () => void;
}

export const JobDetailsHeader = ({ onOpenInvitation, onOpenBulkImport, onOpenAddCandidate }: JobDetailsHeaderProps) => {
    return (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
            <BackButton />
            <Stack direction="row" gap={2}>
                <PrimaryButton
                    variant="outlined"
                    content="Edit Invitation"
                    onClick={onOpenInvitation}
                    width="auto"
                />
                <PrimaryButton
                    variant="contained"
                    content="Import Candidates"
                    onClick={onOpenBulkImport}
                    width="auto"
                />
                <PrimaryButton
                    variant="contained"
                    content="Add Candidate"
                    onClick={onOpenAddCandidate}
                    width="auto"
                />
            </Stack>
        </Stack>
    );
}; 