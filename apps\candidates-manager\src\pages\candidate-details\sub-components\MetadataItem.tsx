import { FC, ReactNode } from "react";
import { Box, Stack, Typography, styled } from "@mui/material";

interface MetadataItemProps {
    icon: ReactNode;
    label: string;
    value: ReactNode;
}

const StyledStack = styled(Stack)(({ theme }) => ({
    marginBottom: theme.spacing(1.5),
}));

const LabelText = styled(Typography)(({ theme }) => ({
    fontWeight: "bold",
    color: theme.palette.text.primary,
    display: "inline",
}));

const ValueText = styled(Typography)(({ theme }) => ({
    color: theme.palette.text.primary,
    display: "inline",
}));

export const MetadataItem: FC<MetadataItemProps> = ({ icon, label, value }) => {
    return (
        <StyledStack direction="row" alignItems="center">
            <Box color="primary.main" sx={{ mr: 1 }}>
                {icon}
            </Box>
            <Stack direction="row" alignItems="center" gap={1}>
                <LabelText variant="body2">{label}: </LabelText>
                <ValueText variant="body2">{value}</ValueText>
            </Stack>
        </StyledStack>
    );
};
