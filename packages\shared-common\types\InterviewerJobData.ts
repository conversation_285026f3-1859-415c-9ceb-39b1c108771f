
export interface InterviewerJobQuestion {
    text: string;
    videoLink?: string
    questionId?: string
}

export interface InterviewerJobData {
    candidateName: string;
    customerId: string;
    customerName: string;
    jobTitle: string;
    logoLink: string | null;
    welcomeVideoLink: string | null;
    language: string;
    interviewId: string;
    gender: string | null;
    isImageExists: boolean;
    themeColor: string | null;
}
