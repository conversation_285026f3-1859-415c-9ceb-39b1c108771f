import { FC } from "react";
import { Stack } from "@mui/material";
import { Body1, NoCopyWrapper } from "@eva/shared-ui";
import { VideoPlayer } from "../../../../../../../../../components/video-player/VideoPlayer";

interface SimulationStoryPreviewProps {
    videoLink: string;
    story: string;
    onStoryVideoEnd: () => void;
    autoPlayDisabled: boolean;
}

export const SimulationStoryView: FC<SimulationStoryPreviewProps> = ({
    autoPlayDisabled,
    story,
    videoLink,
    onStoryVideoEnd,
}) => {
    return (
        <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
            <VideoPlayer
                autoPlayDisabled={autoPlayDisabled}
                videoUrl={videoLink}
                width="100%"
                height="50dvh"
                onVideoEnd={onStoryVideoEnd}
            />
            <NoCopyWrapper>
                <Body1>{story}</Body1>
            </NoCopyWrapper>
        </Stack>
    );
};
