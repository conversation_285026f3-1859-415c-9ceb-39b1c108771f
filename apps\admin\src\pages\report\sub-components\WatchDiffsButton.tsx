import { H4, <PERSON><PERSON><PERSON>on } from "@eva/shared-ui";
import { useReviewPageContext } from "../context/ReviewPageProvider";
import { useState } from "react";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import { CenteredModal } from "@eva/shared-ui";
import { FamiliesWithTraitsForDisplayMap, InterviewResponse } from "@eva/shared-common";
import { Divider, Paper, Stack, styled, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { basicTheme, Body2, H3, H5 } from "@eva/shared-ui";

const StyledFamiliesStackContainer = styled(Stack)({
    overflow: "hidden",
    whiteSpace: "normal",
    wordBreak: "break-word",
});

const markDifferentProperty = (isDifferent: boolean): React.CSSProperties =>
    isDifferent ? { backgroundColor: basicTheme.palette.success.light } : {};

const isThereDiffs = (interviewData: InterviewResponse | undefined): boolean => {
    if (!interviewData) return false;
    if (!interviewData.originalReport) return false;

    for (const trait of interviewData.report.traits) {
        const originalTrait = interviewData.originalReport.traits.find((t) => t.code === trait.code);
        if (!originalTrait) return true;
        if (originalTrait.score !== trait.score) return true;
    }

    for (const family of interviewData.report.families) {
        const originalFamily = interviewData.originalReport.families.find((f) => f.code === family.code);
        if (!originalFamily) return true;
        if (originalFamily.summary !== family.summary) return true;
    }

    return false;
};

const createTraitsIdMapping = (familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap): Record<string, string> => {
    const traitsIdMapping: Record<string, string> = {};
    for (const family of Object.values(familiesWithTraitsMap)) {
        for (const id of Object.keys(family.traitsMap)) {
            traitsIdMapping[id] = family.traitsMap[id].trait.en;
        }
    }
    return traitsIdMapping;
};

export const WatchDiffsButton = () => {
    const { interviewData, familiesWithTraitsMap } = useReviewPageContext();
    const [isDiffsModalOpen, setIsDiffsModalOpen] = useState<boolean>(false);
    const shouldRenderButton = isThereDiffs(interviewData);
    const handleClick = () => {
        setIsDiffsModalOpen(true);
    };
    const onClose = () => {
        setIsDiffsModalOpen(false);
    };

    const traitsIdMapping: Record<string, string> = familiesWithTraitsMap ? createTraitsIdMapping(familiesWithTraitsMap) : {};

    const originalTraits = interviewData?.originalReport?.traits ?? [];
    const reviewedTraits = interviewData?.report.traits ?? [];

    const originalFamilies = interviewData?.originalReport?.families ?? [];
    const reviewedFamilies = interviewData?.report.families ?? [];
    return shouldRenderButton ? (
        <>
            <PrimaryButton onClick={handleClick} content='See Changes' endIcon={<RemoveRedEyeIcon />} />
            <CenteredModal width={1200} height={900} onClose={onClose} isOpen={isDiffsModalOpen}>
                <Stack gap={4}>
                    <H3>Traits</H3>
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>Trait</TableCell>
                                    <TableCell>Original Score</TableCell>
                                    <TableCell>Reviewed Score</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {originalTraits.map((trait, index) => {
                                    const reviewedTrait = reviewedTraits.find((t) => t.code === trait.code);
                                    const isDifferent = reviewedTrait ? reviewedTrait.score !== trait.score : false;
                                    return (
                                        <TableRow key={trait.code} style={markDifferentProperty(isDifferent)}>
                                            <TableCell>{traitsIdMapping[trait.code]}</TableCell>
                                            <TableCell>{trait.score}</TableCell>
                                            <TableCell>{reviewedTrait ? reviewedTrait.score : "N/A"}</TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <Divider />
                    <H3>Family Summaries</H3>
                    <Stack direction='row' justifyContent='space-between' gap={4}>
                        <Stack width='150px'>
                            <H4>Family</H4>
                        </Stack>
                        <Stack width='50%'>
                            <H5>Original</H5>
                        </Stack>
                        <Stack width='50%'>
                            <H5>Reviewed</H5>
                        </Stack>
                    </Stack>
                    {familiesWithTraitsMap && (
                        <>
                            {originalFamilies.map((family, index) => {
                                const isDifferent = reviewedFamilies[index].summary !== family.summary;
                                return (
                                    <StyledFamiliesStackContainer
                                        key={family.code}
                                        direction='row'
                                        justifyContent='space-between'
                                        gap={4}
                                        style={markDifferentProperty(isDifferent)}
                                    >
                                        <>
                                            <Stack width='150px'>
                                                <H5>{familiesWithTraitsMap[family.code].family.en}</H5>
                                            </Stack>
                                            <Stack alignItems='start' width='50%'>
                                                <Body2>{family.summary}</Body2>
                                            </Stack>
                                            <Stack alignItems='start' width='50%'>
                                                <Body2>{reviewedFamilies[index].summary}</Body2>
                                            </Stack>
                                        </>
                                    </StyledFamiliesStackContainer>
                                );
                            })}
                        </>
                    )}
                </Stack>
            </CenteredModal>
        </>
    ) : (
        <></>
    );
};
