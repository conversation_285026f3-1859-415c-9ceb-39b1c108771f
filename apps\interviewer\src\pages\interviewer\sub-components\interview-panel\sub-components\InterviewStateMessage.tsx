import { Stack } from "@mui/material";
import { useAppText, H3 } from "@eva/shared-ui";

export const InterviewStateMessage = () => {
    const { getContentForDisplay } = useAppText();
    const interviewStateMessage = getContentForDisplay("interviewer.interviewStateMessage");

    return (
        <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
            <H3 data-qa="interview-state-message">{interviewStateMessage}</H3>
        </Stack>
    );
};
