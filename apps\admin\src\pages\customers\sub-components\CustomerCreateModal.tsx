import { CreateCustomerFormInputs } from "@eva/shared-common";
import { uploadData } from "aws-amplify/storage";
import { Formik } from "formik";
import { FC, useState } from "react";
import { toast } from "react-toastify";
import * as yup from "yup";
import { useCreateCustomerMutation } from "../../../app-state/apis";
import { basicTheme as theme, generateUniqueString, CenteredModal, ModalLoadingSpinner } from "@eva/shared-ui";
import { CustomerCreateForm } from "./CustomerCreateForm";

interface CustomerCreateModalProps {
    isOpen: boolean;
    onClose: () => void;
    customerForEditValues: CreateCustomerFormInputs | null;
}

const CustomerCreateModal: FC<CustomerCreateModalProps> = ({ customerForEditValues, isOpen, onClose }) => {
    const [createCustomer, { isLoading }] = useCreateCustomerMutation();
    const [isUploading, setIsUploading] = useState(false);
    const initialValues: CreateCustomerFormInputs = customerForEditValues
        ? { ...customerForEditValues, themeColor: customerForEditValues.themeColor || theme.palette.primary.main }
        : {
              themeColor: theme.palette.primary.main,
              customerName: "",
              accountId: "",
              integration: "",
              customerId: null,
              logoFile: null,
              logoFilePath: null,
          };

    const formType = customerForEditValues ? "edit" : "create";
    const validationSchema = yup.object().shape({
        customerName: yup.string().required("Customer name is required"),
        accountId: yup.string().required("Account is required"),
        customerId: customerForEditValues ? yup.string().required("Customer ID is required") : yup.string().nullable().notRequired(),
        integration: yup.string(),
        backgroundColor: yup.string(),
        logoFile: yup
            .mixed()
            .test("fileType", "Only .svg or .png files are allowed", (value) => {
                return value ? ["image/svg+xml", "image/png"].includes((value as File).type) : true;
            })
            .notRequired(),
        logoFilePath: yup.string().when("logoFile", (logoFile, schema) => {
            return logoFile ? schema.notRequired() : schema.required("File path is required");
        }),
    });

    const onSubmit = async (values: CreateCustomerFormInputs) => {
        let path: string | null = null;
        try {
            if (values.logoFile) {
                path = `public/uploads/logos/${generateUniqueString(16)}.${values.logoFile.name.split(".").pop()}`;
                setIsUploading(true);
                await uploadData({
                    path,
                    data: values.logoFile,
                    options: {
                        contentType: values.logoFile?.type,
                    },
                }).result;
            }
            await createCustomer({
                themeColor: values.themeColor,
                accountId: values.accountId,
                customerName: values.customerName,
                customerId: values.customerId,
                logoFilePath: path,
                integration: values.integration,
            });
            const customerId = values.customerId;
            onClose();
            toast.success(`Customer ${customerId ? "updated" : "created"} successfully`);
        } catch (error) {
            toast.error("Failed to create customer");
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <CenteredModal width={600} height={800} isOpen={isOpen} onClose={onClose}>
            {isLoading || isUploading ? (
                <ModalLoadingSpinner />
            ) : (
                <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit} validateOnChange={false}>
                    <CustomerCreateForm formType={formType} />
                </Formik>
            )}
        </CenteredModal>
    );
};

export default CustomerCreateModal;
