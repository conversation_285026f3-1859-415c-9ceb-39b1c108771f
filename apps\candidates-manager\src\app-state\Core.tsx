import { useAuthenticator } from "@aws-amplify/ui-react";
import { Box, Skeleton, Stack, styled } from "@mui/material";
import React, { Suspense, useEffect } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import { AuthenticatorPage, BackdropLoading, LoadingScreen } from "@eva/shared-ui";
import { UserDetailsProvider } from "./context/UserDetailsProvider";
import JobDetailsPage from "../pages/job-details/JobDetailsPage";

const CandidatesPage = React.lazy(() => import("../pages/candidates/CandidatesPage"));
const JobsPage = React.lazy(() => import("../pages/jobs/JobsPage"));
const CandidateDetailsPage = React.lazy(() => import("../pages/candidate-details/CandidateDetailsPage"));
const NavigationDrawer = React.lazy(() => import("../components/drawer/NavigationDrawer"));
const Header = React.lazy(() => import("../components/header/Header"));
const ContentContainer = styled(Box)({
    flexGrow: 1,
});

export const Core = () => {
    const { route, isPending, authStatus } = useAuthenticator((context) => [
        context.route,
        context.isPending,
        context.authStatus,
        context.user,
    ]);

    useEffect(() => {
        if (authStatus === "unauthenticated") {
            window.localStorage.clear();
        }
    }, [authStatus]);

    if (route === "transition" || isPending) return <BackdropLoading isLoading={route === "transition"} />;

    return route === "authenticated" ? (
        <UserDetailsProvider>
            <Router>
                
                <Stack direction="row">
                    <Suspense fallback={<Skeleton variant="rectangular" width="100%" height="100%" />}>
                        <NavigationDrawer />
                    </Suspense>
                    <ContentContainer>
                        <Suspense
                            fallback={<Skeleton variant="rectangular" width="100%" height="64px" animation="wave" />}
                        >
                            <Header />
                        </Suspense>
                        <Suspense
                            fallback={<Skeleton variant="rectangular" width="100%" height="64px" animation="wave" />}
                        ></Suspense>
                        <Suspense fallback={<LoadingScreen />}>
                            <Routes>
                                <Route path="/" element={<CandidatesPage />} />
                                <Route path="/jobs" element={<JobsPage />} />
                                <Route path="/jobs/:jobId" element={<JobDetailsPage />} />
                                <Route path="/candidates" element={<CandidatesPage />} />
                                <Route path="/candidates/:candidateId" element={<CandidateDetailsPage />} />
                                <Route path="/*" element={<CandidatesPage />} />
                            </Routes>
                        </Suspense>
                    </ContentContainer>
                </Stack>
            </Router>
        </UserDetailsProvider>
    ) : (
        <AuthenticatorPage />
    );
};
