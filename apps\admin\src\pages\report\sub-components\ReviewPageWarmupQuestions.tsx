import { Card } from "@aws-amplify/ui-react";
import { AdditionalQuestion } from "@eva/shared-common";
import { Box, CardContent, CardMedia, Paper, Stack } from "@mui/material";
import { Body1, H3, LightPaper, useAppText } from "@eva/shared-ui";
import { useReviewPageContext } from "../context/ReviewPageProvider";

export const ReviewPageWarmupQuestions = () => {
    const { warmupQuestionsResponse } = useReviewPageContext();
    const { currentLanguage } = useAppText();

    if (!warmupQuestionsResponse || !warmupQuestionsResponse.warmupQuestionsAnswers.length) {
        return null;
    }

    const headerText = currentLanguage === "en" ? "Warmup Questions" : "שאלות פתיחה";

    return (
        <Stack padding={3}>
            <Paper>
                <Stack padding={3} gap={6}>
                    <H3 color="secondary">{headerText}</H3>
                    <Stack direction="row" gap={3}>
                        {warmupQuestionsResponse.warmupQuestionsAnswers.map(
                            (item: AdditionalQuestion, index: number) => (
                                <Box key={index} maxWidth="300px" minWidth="300px">
                                    <Card>
                                        <LightPaper>
                                            <CardMedia controls component="video" src={item.video_link} />
                                            <CardContent>
                                                <Body1>{item.question}</Body1>
                                            </CardContent>
                                        </LightPaper>
                                    </Card>
                                </Box>
                            )
                        )}
                    </Stack>
                </Stack>
            </Paper>
        </Stack>
    );
};
