import FileUploadIcon from "@mui/icons-material/FileUpload";
import { Stack, Tab, Tabs } from "@mui/material";
import { useState } from "react";
import { PrimaryButton, CenteredModal, PageContainer, PageLayout } from "@eva/shared-ui";
import { QuestionsCardsList } from "./sub-components/QuestionsCardsList";
import { VideoUploaderForm } from "./sub-components/VideoUploaderForm";
import { IntroVideosCardsList } from "./sub-components/IntroVideosCardsList";

const VideosPage = () => {
    const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
    const [tabValue, setTabValue] = useState(0);

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const handleModalChange = () => {
        setIsUploadModalOpen((prev) => !prev);
    };

    return (
        <PageContainer>
            <PageLayout>
                <Stack direction='row' justifyContent='space-between' padding={3} height={60}>
                    <Tabs value={tabValue} onChange={handleTabChange}>
                        <Tab label='Questions' id='questions' />
                        <Tab label='Intro Videos' id='intros' />
                    </Tabs>
                    <Stack>
                        <PrimaryButton content='Upload Video' onClick={handleModalChange} endIcon={<FileUploadIcon />} />
                    </Stack>
                </Stack>
                {tabValue === 0 && <QuestionsCardsList />}
                {tabValue === 1 && <IntroVideosCardsList />}
                <CenteredModal isOpen={isUploadModalOpen} onClose={handleModalChange} width={1200} height='fit-content'>
                    <VideoUploaderForm />
                </CenteredModal>
            </PageLayout>
        </PageContainer>
    );
};

export default VideosPage;
