import { AppText } from "@eva/shared-ui";

export const appLanguagesTextMap: AppText = {
    en: {
        direction: "ltr",
        interviewer: {
            uploadingMessage: "Uploading answer",
            submittingInterviewMessage: "Submitting interview, please wait",
            trainingQuestionPreview: {
                goBack: "Back",
                startInterview: "Start Interview",
            },
            interviewStateMessage: "Interview is done! Thank you and good luck!",
            setupState: {
                continueInterview: "Continue Interview",
                startInterview: "Start Interview",
                training: "Tryout",
                setupCheck: {
                    camera: {
                        externalInputType: "Camera",
                        ready: "Available",
                        disabled: "Not available",
                    },
                    microphone: {
                        externalInputType: "Microphone",
                        ready: "Available",
                        disabled: "Not available",
                    },
                    internet: {
                        externalInputType: "Internet",
                        ready: "Available",
                        disabled: "Not available",
                    },
                },
            },
            cameraPreview: {
                recordingControls: {
                    recordingStopWatchMessage: "Recording in",
                    startRecording: "Record Answer",
                    stopRecording: "Stop Recording",
                    retryRecording: "Retry Recording",
                },
            },
        },
    },
    he: {
        direction: "rtl",
        interviewer: {
            submittingInterviewMessage: "מגי<PERSON> ראיון, אנ<PERSON> המתן",
            uploadingMessage: "מעלה תשובה",
            trainingQuestionPreview: {
                goBack: "חזור",
                startInterview: "התחל ראיון",
            },
            interviewStateMessage: "ראיון הסתיים! תודה ובהצלחה!",
            setupState: {
                continueInterview: "המשך ראיון",
                startInterview: "התחל ראיון",
                training: "התנסות",
                setupCheck: {
                    camera: {
                        externalInputType: "מצלמה",
                        ready: "זמין",
                        disabled: "לא זמין",
                    },
                    microphone: {
                        externalInputType: "מיקרופון",
                        ready: "זמין",
                        disabled: "לא זמין",
                    },
                    internet: {
                        externalInputType: "אינטרנט",
                        ready: "זמין",
                        disabled: "לא זמין",
                    },
                },
            },
            cameraPreview: {
                recordingControls: {
                    recordingStopWatchMessage: "מקליט בעוד",
                    startRecording: "התחל הקלטה",
                    stopRecording: "עצור הקלטה",
                    retryRecording: "נסה שוב",
                },
            },
        },
    },
};