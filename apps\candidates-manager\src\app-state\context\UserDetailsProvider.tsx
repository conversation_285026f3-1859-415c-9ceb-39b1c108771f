import { createContext, useContext, useEffect, useLayoutEffect, useState } from "react";
import { fetchUserAttributes } from "aws-amplify/auth";
import { BackdropLoading } from "@eva/shared-ui";
import { useAuthenticator } from "@aws-amplify/ui-react";
import { useGetCustomerThemeQuery } from "../apis";
import { useAppTheme } from "@eva/shared-ui";

interface UserDetails {
    id: string;
    email: string;
    customerId: string;
}

interface UserDetailsValue {
    email: string;
    customerId: string;
    id: string;
}

export const UserDetailsContext = createContext<UserDetailsValue | undefined>(undefined);

export const UserDetailsProvider = ({ children }: { children: React.ReactNode }) => {
    const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
    const { route, isPending } = useAuthenticator((context) => [context.route]);
    const { changeMainColor } = useAppTheme();
    useLayoutEffect(() => {
        fetchUserAttributes().then((attributes) => {
            setUserDetails({
                id: attributes.sub!,
                email: attributes.email!,
                customerId: attributes["custom:customerId"]!,
            });
        });
    }, []);

    const {
        data: themeData,
        isLoading,
        error,
    } = useGetCustomerThemeQuery(userDetails?.customerId ?? "", {
        skip: !userDetails?.customerId,
    });

    useEffect(() => {
        if (themeData && themeData.themeColor) {
            changeMainColor(themeData.themeColor);
        }
    }, [themeData]);

    return userDetails && !isLoading && route === "authenticated" ? (
        <UserDetailsContext.Provider
            value={{ email: userDetails!.email, customerId: userDetails!.customerId, id: userDetails!.id }}
        >
            {children}
        </UserDetailsContext.Provider>
    ) : (
        <BackdropLoading isLoading={true} />
    );
};

export const useUserDetails = () => {
    const context = useContext(UserDetailsContext);
    if (!context) {
        throw new Error("useUserDetails must be used within a UserDetailsProvider");
    }
    return context;
};
