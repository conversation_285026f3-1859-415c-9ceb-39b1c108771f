import { Button, Heading, Text, useAuthenticator, useTheme, View } from "@aws-amplify/ui-react";
import { Stack, styled, CircularProgress } from "@mui/material";

const CustomLogoImage = styled("img")({
    width: "auto",
    maxWidth: "80%",
    height: "100px",
    objectFit: "contain",
    margin: "0 auto 20px",
    display: "block",
});

const defaultLogoSrc = "https://d3qfmqub9fv9wg.cloudfront.net/public/assets/logo.png";

export const components = {
    Header() {
        return (
            <Stack paddingBottom={4} textAlign="center" justifyContent="center">
                <CustomLogoImage src={defaultLogoSrc} alt="logo" />
            </Stack>
        );
    },

    Footer() {
        const { tokens } = useTheme();

        return (
            <View textAlign="center" padding={tokens.space.medium}>
                <Text color={tokens.colors.neutral[80]}>&copy; {new Date().getFullYear()} All Rights Reserved</Text>
            </View>
        );
    },

    SignIn: {
        FormFields() {
            const { isPending } = useAuthenticator();
            return (
                <>
                    <View>
                        <Button
                            isDisabled={isPending}
                            type="submit"
                            variation="primary"
                            isLoading={isPending}
                            loadingText="Signing in..."
                        >
                            {isPending ? (
                                <Stack direction="row" alignItems="center" spacing={1}>
                                    <CircularProgress size={20} color="inherit" />
                                    <Text>Signing in...</Text>
                                </Stack>
                            ) : (
                                "Sign in"
                            )}
                        </Button>
                    </View>
                </>
            );
        },
        Footer() {
            const { toForgotPassword } = useAuthenticator();

            return (
                <View textAlign="center" padding="16px 0 0">
                    <Button fontWeight="normal" onClick={toForgotPassword} size="small" variation="link">
                        Forgot Password?
                    </Button>
                </View>
            );
        },
    },

    SignUp: {
        Header() {
            const { tokens } = useTheme();

            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Create a new account
                </Heading>
            );
        },
        Footer() {
            const { toSignIn } = useAuthenticator();

            return (
                <View textAlign="center">
                    <Button fontWeight="normal" onClick={toSignIn} size="small" variation="link">
                        Back to Sign In
                    </Button>
                </View>
            );
        },
    },
    ConfirmSignUp: {
        Header() {
            const { tokens } = useTheme();
            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Enter Information:
                </Heading>
            );
        },
        Footer() {
            return <Text>Footer Information</Text>;
        },
    },
    SetupTotp: {
        Header() {
            const { tokens } = useTheme();
            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Enter Information:
                </Heading>
            );
        },
        Footer() {
            return <Text>Footer Information</Text>;
        },
    },
    ConfirmSignIn: {
        Header() {
            const { tokens } = useTheme();
            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Enter Information:
                </Heading>
            );
        },
        Footer() {
            return <Text>Footer Information</Text>;
        },
    },
    ForgotPassword: {
        Header() {
            const { tokens } = useTheme();
            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Enter Information:
                </Heading>
            );
        },
        Footer() {
            return <Text>Footer Information</Text>;
        },
    },
    ConfirmResetPassword: {
        Header() {
            const { tokens } = useTheme();
            return (
                <Heading padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`} level={3}>
                    Enter Information:
                </Heading>
            );
        },
        Footer() {
            return <Text>Footer Information</Text>;
        },
    },
};

export const formFields = {
    signIn: {
        username: {
            placeholder: "Email or Username",
            label: "Email or Username",
        },
        password: {
            placeholder: "Password",
            label: "Password",
        },
    },
    signUp: {
        password: {
            label: "Password:",
            placeholder: "Password:",
            isRequired: false,
            order: 2,
        },
        confirm_password: {
            label: "Confirm Password:",
            order: 1,
        },
    },
    forceNewPassword: {
        password: {
            placeholder: "Enter your Password:",
        },
    },
    forgotPassword: {
        username: {
            placeholder: "Enter your email:",
        },
    },
    confirmResetPassword: {
        confirmation_code: {
            placeholder: "Enter your Confirmation Code:",
            label: "New Label",
            isRequired: false,
        },
        confirm_password: {
            placeholder: "Enter your Password Please:",
        },
    },
    setupTotp: {
        QR: {
            totpIssuer: "test issuer",
            totpUsername: "amplify_qr_test_user",
        },
        confirmation_code: {
            label: "New Label",
            placeholder: "Enter your Confirmation Code:",
            isRequired: false,
        },
    },
    confirmSignIn: {
        confirmation_code: {
            label: "New Label",
            placeholder: "Enter your Confirmation Code:",
            isRequired: false,
        },
    },
};
