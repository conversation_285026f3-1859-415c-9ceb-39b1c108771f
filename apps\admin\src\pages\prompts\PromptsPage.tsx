import { Tab, Tabs } from "@mui/material";
import { useState } from "react";
import { PageContainer, PageLayout, useAppText } from "@eva/shared-ui";
import { FamiliesPromptsTab } from "./sub-components/FamiliesPromptsTab";
import { TraitsPromptsTab } from "./sub-components/TraitsPromptsTab";

export const PromptsPage = () => {
    const { currentLanguage } = useAppText();
    const [tabValue, setTabValue] = useState(0);
    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };
    return (
        <PageContainer>
            <PageLayout>
                <Tabs value={tabValue} onChange={handleTabChange}>
                    <Tab label={currentLanguage === "he" ? "משפחות" : "Families"} id='families' />
                    <Tab label={currentLanguage === "he" ? "תכונות" : "Traits"} id='traits' />
                </Tabs>
                {tabValue === 0 && <FamiliesPromptsTab />}
                {tabValue === 1 && <TraitsPromptsTab />}
            </PageLayout>
        </PageContainer>
    );
};
