import { Container, styled } from "@mui/material";
import React from "react";

interface PageLayoutContainerProps {
    centered: boolean;
}

export const PageLayoutContainer = styled(Container)<PageLayoutContainerProps>(({ centered }) => ({
    marginBlock: "20px",
    ...(centered ? {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
    } : {}),
}));

interface PageContainerProps {
    children: React.ReactNode;
    centered?: boolean;
}

export const PageContainer: React.FC<PageContainerProps> = ({ children, centered = false }) => {
    return <PageLayoutContainer maxWidth='xl' centered={centered}>{children}</PageLayoutContainer>;
};
