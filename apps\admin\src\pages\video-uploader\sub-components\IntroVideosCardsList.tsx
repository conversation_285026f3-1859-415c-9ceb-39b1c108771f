import { Grid, styled, Pagination } from "@mui/material";
import { useGetAllIntroVideosQuery } from "../../../app-state/apis";
import { H3, LoadingScreen } from "@eva/shared-ui";
import { IntroVideoCard } from "./IntroVideoCard";
import { IntroVideosCardsListFilter } from "./IntroVideosCardsListFilter";
import { useState } from "react";
import { IntroVideo } from "@eva/shared-common";

const ITEMS_PER_PAGE = 8;

const CustomGrid = styled(Grid)({
    overflowX: "hidden",
    overflowY: "scroll",
    height: "60dvh",
});

const StyledPaginationContainer = styled("div")({
    display: "flex",
    justifyContent: "center",
    marginTop: "16px",
});

export const IntroVideosCardsList = () => {
    const { data, isLoading } = useGetAllIntroVideosQuery();
    const [videos, setVideos] = useState(data?.videos ? data.videos : []);
    const [currentPage, setCurrentPage] = useState(1);

    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    const currentVideos = videos.slice(startIndex, endIndex);
    const pageCount = Math.ceil(videos.length / ITEMS_PER_PAGE);

    const handleVideosChange = (filteredVideos: IntroVideo[]) => {
        setVideos(filteredVideos);
        setCurrentPage(1);
    };

    const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
        setCurrentPage(value);
    };

    return !isLoading && data ? (
        <>
            <IntroVideosCardsListFilter handleVideosChange={handleVideosChange} introVideosData={data} />
            <CustomGrid container overflow="scroll" gap={4}>
                {currentVideos.length === 0 && (
                    <Grid item>
                        <H3>No videos found</H3>
                    </Grid>
                )}
                {currentVideos.map((video) => (
                    <IntroVideoCard
                        videoId={video.id}
                        key={video.videoLink}
                        videoUrl={video.videoLink}
                        customerName={video.customerName}
                        gender={video.gender}
                        language={video.language}
                    />
                ))}
            </CustomGrid>
            {pageCount > 1 && (
                <StyledPaginationContainer>
                    <Pagination count={pageCount} page={currentPage} onChange={handlePageChange} />
                </StyledPaginationContainer>
            )}
        </>
    ) : (
        <LoadingScreen />
    );
};
