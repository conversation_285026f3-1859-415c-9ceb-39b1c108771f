import { AppText, Language } from "@eva/shared-ui";

export type LanguageMapper = Record<Language, string>;

export const languageMapper = (language: string): string => {
    const languagesMap: LanguageMapper = {
        en: "English",
        he: "Hebrew",
    };
    return languagesMap[language as Language];
};

export const languages: Language[] = ["en", "he"];

export const appText: AppText = {
    en: {
        direction: "ltr",
        jobsPage: {
            addJob: "Add Job +",
        },
        reviewPage: {
            exitReviewMode: "Exit Review Mode",
            saveChanges: "Save Changes",
            resetChanges: "Reset Changes",
            submitReview: "Submit Review",
            export: "Export",
            sendReport: "Send Report",
            backToReviewMode: "Back to review mode",
            startReview: "Start Review",
            score: "Score",
            candidateName: "Candidate Name",
            socialId: "Social ID",
            email: "Email",
            companyName: "Company Name",
            jobTitle: "Job Title",
            language: "Language",
            interviewDateText: "Interview Date",
            reviewDate: "Review Date",
            reviewedBy: "Reviewed By",
            dialogModal: {
                title: "Send Report",
                content: "Are you sure you want to submit the report?",
                cancel: "Cancel",
                send: "Submit",
            },
        },
        jobBuilderForm: {
            jobDetails: "Job Details",
            reset: "Reset",
            createJob: "Create Job",
            connectJob: "Connect Job",
            updateJob: "Update Job",
            questions: "Questions",
            computedTraits: "Computed Traits",
            tabs: {
                startJobQuestions: "General Questions *",
                warmupQuestions: "Warmup Questions",
                openQuestions: "Open Questions",
                simulations: "Simulations",
            },
            warmupQuestions: {
                title: "Warmup Questions",
                addQuestion: "Add Question",
                removeQuestion: "Remove",
            },
            openQuestions: {
                title: "Open Questions",
                addQuestion: "Add Question",
                removeQuestion: "Remove",
            },
            jobGeneralDetails: {
                customerSelect: "Customer",
                languageSelect: "Language",
                titleInput: "Title",
                descriptionInput: "Description",
                myInterviewJobId: "MI Job ID",
            },
            simulations: {
                story: "Story",
                questions: "Questions",
            },
        },
        appBar: {
            navBar: {
                interviews: "Interviews",
                jobs: "Jobs",
                videos: "Videos",
                customers: "Customers",
                prompts: "Prompts",
            },
            userMenu: {
                logout: "Logout",
            },
        },
    },
    he: {
        direction: "rtl",
        jobsPage: {
            addJob: "הוסף משרה +",
        },
        reviewPage: {
            exitReviewMode: "יציאה ממצב ביקורת",
            saveChanges: "שמור שינויים",
            resetChanges: "איפוס שינויים",
            submitReview: "שמור ביקורת",
            export: "הורד",
            sendReport: "שלח דוח",
            backToReviewMode: "חזור למצב ביקורת",
            startReview: "התחל ביקורת",
            score: "ציון",
            candidateName: "שם מועמד",
            socialId: "ת''ז",
            email: "דואר אלקטרוני",
            companyName: "שם חברה",
            jobTitle: "תפקיד",
            language: "שפה",
            interviewDateText: "תאריך ראיון",
            reviewDate: "תאריך ביקורת",
            reviewedBy: "נבדק על ידי",
            dialogModal: {
                title: "שלח דוח",
                content: "האם אתה בטוח שברצונך לשמור את הדוח?",
                cancel: "ביטול",
                send: "לשמור",
            },
        },
        jobBuilderForm: {
            jobDetails: "פרטי משרה",
            reset: "איפוס",
            createJob: "צור משרה",
            updateJob: "עדכן משרה",
            connectJob: "חבר משרה",
            questions: "שאלות",
            computedTraits: "מאפיינים מחושבים",
            tabs: {
                startJobQuestions: "שאלות כלליות *",
                warmupQuestions: "שאלות חימום",
                openQuestions: "שאלות פתוחות",
                simulations: "סימולציות",
            },
            warmupQuestions: {
                title: "שאלות חימום",
                addQuestion: "הוסף שאלה",
                removeQuestion: "הסר",
            },
            openQuestions: {
                title: "שאלות פתוחות",
                addQuestion: "הוסף שאלה",
                removeQuestion: "הסר",
            },
            jobGeneralDetails: {
                customerSelect: "לקוח",
                languageSelect: "שפה",
                titleInput: "כותרת",
                descriptionInput: "תיאור",
                myInterviewJobId: "מזהה משרה",
            },
            simulations: {
                story: "סיפור",
                questions: "שאלות",
            },
        },
        appBar: {
            navBar: {
                interviews: "ראיונות",
                jobs: "משרות",
                videos: "סרטונים",
                customers: "לקוחות",
                prompts: "פרומפטים",
            },
            userMenu: {
                logout: "התנתק",
            },
        },
    },
};
