CREATE
    EXTENSION IF NOT EXISTS "uuid-ossp";



CREATE TABLE IF NOT EXISTS accounts
(
    id         VARCHAR(255) PRIMARY KEY NOT NULL,
    name       VARCHAR(255)             NOT NULL,
    created_at TIMESTAMP                NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP                NOT NULL DEFAULT NOW()
);


CREATE TABLE IF NOT EXISTS traits_families
(
    id
               VARCHAR(10) NOT NULL UNIQUE
        PRIMARY
            KEY,
    family     json,
    prompt_metadata json,
    created_at TIMESTAMP   NOT NULL DEFAULT NOW
                                            (
                                            ),
    updated_at TIMESTAMP   NOT NULL DEFAULT NOW
                                            (
                                            )
);

CREATE TABLE IF NOT EXISTS traits
(
    id                  VARCHAR(10) NOT NULL UNIQUE PRIMARY KEY,
    trait               json,
    family_id           VARCHAR(10) NOT NULL,
    trait_summaries_map json,
    computed_by         json,
    prompt_metadata      json,
    created_at          TIMESTAMP   NOT NULL DEFAULT NOW
                                                     (
                                                     ),
    updated_at          TIMESTAMP   NOT NULL DEFAULT NOW
                                                     (
                                                     ),
    FOREIG<PERSON> KEY
        (
         family_id
            ) REFERENCES traits_families
        (
         id
            ) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS questions
(
    id                 VARCHAR(10) NOT NULL UNIQUE PRIMARY KEY,
    options            json        NOT NULL,
    grading_principles json,
    trait_id           VARCHAR(10) NOT NULL,
    created_at         TIMESTAMP   NOT NULL DEFAULT NOW
                                                    (
                                                    ),
    updated_at         TIMESTAMP
                                   NOT NULL DEFAULT NOW
                                                    (
                                                    ),
    FOREIGN KEY
        (
         trait_id
            ) REFERENCES traits
        (
         id
            ) ON DELETE CASCADE
);



CREATE TABLE IF NOT EXISTS customers
(
    id
                 UUID
        PRIMARY
            KEY
                                       DEFAULT
                                           uuid_generate_v4
                                           (
                                           ),
    company_name VARCHAR(255) NOT NULL UNIQUE,
    theme_color  VARCHAR(255),
    account_id   VARCHAR(255) NOT NULL,
    logo_link    VARCHAR(255),
    integration  VARCHAR(255),
    created_at   TIMESTAMP    NOT NULL DEFAULT NOW
                                               (
                                               ),
    updated_at   TIMESTAMP
                              NOT NULL DEFAULT NOW
                                               (
                                               ),
    FOREIGN KEY (account_id) REFERENCES accounts (id)
);

CREATE TABLE IF NOT EXISTS welcome_videos
(
    id
                UUID
        PRIMARY
            KEY
                                      DEFAULT
                                          uuid_generate_v4
                                          (
                                          ),
    customer_id UUID         NULL,
    video_link  VARCHAR(255) NOT NULL,
    language    VARCHAR(255) NOT NULL,
    gender      VARCHAR(255) NOT NULL,
    created_at  TIMESTAMP    NOT NULL DEFAULT NOW
                                              (
                                              ),
    updated_at  TIMESTAMP
                             NOT NULL DEFAULT NOW
                                              (
                                              ),
    FOREIGN KEY
        (
         customer_id
            ) REFERENCES customers
        (
         id
            )
        ON DELETE NULL,
    CONSTRAINT uq_welcome_video_customer_language_gender UNIQUE (customer_id, language, gender)
);

CREATE TABLE IF NOT EXISTS jobs
(
    id          UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    customer_id UUID         NOT NULL,
    language    VARCHAR(255) NOT NULL,
    job_title   VARCHAR(255) NOT NULL,
    description TEXT         NOT NULL,
    questions   JSON         NOT NULL,
    status      VARCHAR(255) NOT NULL,
    invitation  JSON,
    created_at  TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMP    NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_customer FOREIGN KEY (customer_id)
        REFERENCES customers (id) ON DELETE CASCADE,
    CONSTRAINT uq_customer_job_title UNIQUE (customer_id, job_title)
);

CREATE TABLE IF NOT EXISTS candidates
(
    id         UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    email      VARCHAR(255),
    social_id  VARCHAR(255),
    name       VARCHAR(255) NOT NULL,
    image_link VARCHAR(255),
    gender     VARCHAR(255),
    phone_number VARCHAR(255),
    job_id     UUID         NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS interviews
(
    id
                    UUID
        PRIMARY
            KEY
                                          DEFAULT
                                              uuid_generate_v4
                                              (
                                              ),
    candidate_id    UUID         NOT NULL,
    state json,
    event           json,
    run_id          VARCHAR(255),
    status          VARCHAR(255) NOT NULL,
    analyzer_report json,
    questions       json,
    created_at      TIMESTAMP    NOT NULL DEFAULT NOW
                                                  (
                                                  ),
    updated_at      TIMESTAMP    NOT NULL DEFAULT NOW
                                                  (
                                                  ),
    FOREIGN KEY
        (
         candidate_id
            ) REFERENCES candidates
        (
         id
            ) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS answers
(
    id
                 UUID
        PRIMARY
            KEY
                                    DEFAULT
                                        uuid_generate_v4
                                        (
                                        ),
    interview_id UUID      NOT NULL,
    answer       json      NOT NULL,
    created_at   TIMESTAMP NOT NULL DEFAULT NOW
                                            (
                                            ),
    updated_at   TIMESTAMP
                           NOT NULL DEFAULT NOW
                                            (
                                            ),
    FOREIGN KEY
        (
         interview_id
            ) REFERENCES interviews
        (
         id
            )
        ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS prompt_templates
(
    id         UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    type       VARCHAR(255) NOT NULL,
    template   TEXT         NOT NULL,
    language   VARCHAR(255) NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP    NOT NULL DEFAULT NOW()
);


CREATE TABLE IF NOT EXISTS translation_corrections
(
    id         UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    language   VARCHAR(255) NOT NULL,
    text       TEXT         NOT NULL,
    correction TEXT         NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP    NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS interview_summary_sentences
(
    id         UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    sentence   TEXT         NOT NULL,
    score      INT          NOT NULL,
    language   VARCHAR(255) NOT NULL,
    created_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP    NOT NULL DEFAULT NOW()
);


CREATE TABLE IF NOT EXISTS job_id_mapping
(
    id              UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    my_interview_id VARCHAR(255) NOT NULL,
    iverse_id       UUID         NOT NULL,
    created_at      TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMP    NOT NULL DEFAULT NOW(),
    CONSTRAINT job_id_mapping_unique UNIQUE (iverse_id, my_interview_id),
    FOREIGN KEY
        (
         iverse_id
            ) REFERENCES jobs
        (
         id
            )
        ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS candidate_id_mapping
(
    id              UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    my_interview_id VARCHAR(255) NOT NULL,
    iverse_id       UUID         NOT NULL,
    created_at      TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMP    NOT NULL DEFAULT NOW(),
    CONSTRAINT candidate_id_mapping_unique UNIQUE (iverse_id, my_interview_id),
    FOREIGN KEY
        (
         iverse_id
            ) REFERENCES candidates
        (
         id
            )
        ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS question_videos
(
    id          UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    question_id VARCHAR(255) NOT NULL,
    customer_id UUID         NULL,
    video_link  VARCHAR(255) NOT NULL,
    gender      VARCHAR(255) NULL,
    language    VARCHAR(255) NOT NULL,
    created_at  TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMP    NOT NULL DEFAULT NOW(),
    FOREIGN KEY
        (
         question_id
            ) REFERENCES questions
        (
         id
            )
        ON DELETE CASCADE,
    FOREIGN KEY
        (
         customer_id
            ) REFERENCES customers
        (
         id
            ) ON DELETE SET NULL,
    CONSTRAINT uq_question_video_customer_question_gender_language UNIQUE (customer_id, question_id, gender, language)
);


CREATE TABLE IF NOT EXISTS polling_states
(
    id             UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    consumer       VARCHAR(255) NOT NULL,
    last_polled_at TIMESTAMP    NOT NULL DEFAULT NOW(),
    created_at     TIMESTAMP    NOT NULL DEFAULT NOW()
);


CREATE TABLE IF NOT EXISTS interview_id_mapping
(
    id           UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    external_id    VARCHAR(255) NOT NULL,
    interview_id UUID         NOT NULL,
    created_at   TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMP    NOT NULL DEFAULT NOW(),
    CONSTRAINT interview_id_mapping_unique UNIQUE (interview_id, pilat_id),
    FOREIGN KEY (interview_id) REFERENCES interviews (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS customer_id_mapping
(
    id           UUID PRIMARY KEY      DEFAULT uuid_generate_v4(),
    internal_id     UUID NOT NULL,
    external_id  VARCHAR(255)         NOT NULL,
    created_at   TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMP    NOT NULL DEFAULT NOW(),
    CONSTRAINT customer_id_mapping_unique UNIQUE (internal_id, external_Id ),
    FOREIGN KEY (internal_id) REFERENCES customers (id) ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS simulations
(
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data JSON NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);


CREATE
    OR REPLACE FUNCTION update_modified_column()
    RETURNS TRIGGER AS
$$
BEGIN
    NEW.updated_at
        = NOW();
    RETURN NEW;
END;
$$
    language 'plpgsql';

CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON customers
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON jobs
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON candidates
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON questions
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON traits_families
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON traits
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON answers
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON interviews
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON welcome_videos
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON prompt_templates
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON translation_corrections
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON interview_summary_sentences
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON job_id_mapping
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON candidate_id_mapping
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON accounts
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON question_videos
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON interview_id_mapping
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON customer_id_mapping
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_modified_time
    BEFORE UPDATE
    ON simulations
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();


INSERT INTO accounts (id, name)
VALUES ('1f90c3b0-6e76-46a4-b83b-022ca347d72c', 'Pilat'),
       ('c6385c4d-5f05-4b83-96b2-c7d0cf9d24f4', 'Iverse'),
       ('c68d2877-c1dd-4277-a209-c1044b9386c2', 'Cynet');

INSERT INTO customers (company_name, account_id)
VALUES ('Pilat', '1f90c3b0-6e76-46a4-b83b-022ca347d72c'),
       ('Iverse', 'c6385c4d-5f05-4b83-96b2-c7d0cf9d24f4');


INSERT INTO customers (company_name, account_id)
VALUES ('Cynet', 'c68d2877-c1dd-4277-a209-c1044b9386c2'),
       ('Mizrahi Tefahot Bank', '1f90c3b0-6e76-46a4-b83b-022ca347d72c'),
       ('ביטוח לאומי', '1f90c3b0-6e76-46a4-b83b-022ca347d72c'),
       ('אלטמן', '1f90c3b0-6e76-46a4-b83b-022ca347d72c');


INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ראויה לשבח', 'גבוהה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ראויה לציון', 'גבוהה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ראוי לשבח', 'גבוה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ראוי לציון', 'גבוה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'מפגין בקיאות', 'מפגין יכולת');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', ' מאבק ', ' קושי ');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', ' חזקים ', ' טובים ');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'יכולת בולטת', 'יכולת גבוהה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'יכולת בולטת', 'יכולת טובה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'רמה בולטת', 'רמה גבוהה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'רמה בולטת', 'רמה טובה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'התאמה חזקה', 'התאמה טובה מאוד');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'יכולת חזקה', 'יכולת טובה מאוד');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'רמה חזקה', 'רמה טובה מאוד');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'כישרון חזק', 'כישרון טוב');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'חזקה', 'טובה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'חזקה', 'גבוהה');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', ' מוצקה ', ' טובה ');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'המועמד נמוך ב', 'המועמד מתקשה ב');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', ' מסומנת ', ' מתאפיינת ');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציה במשימה', 'משימתיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציית המשימה', 'משימתיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציית המשימות', 'משימתיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציה משימה', 'משימתיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציה שירות', 'שירותיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'אוריינטציית השירות', 'שירותיות');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ובנעימה למדי', 'ובנעימות שלו');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', ' ציות ', ' קבלת מרות ');
INSERT INTO translation_corrections (language, text, correction)
VALUES ('he', 'ממוצע של כישורים', 'כישורים ממוצעים');

INSERT INTO prompt_templates (type, language, template)
VALUES ('should_grade_answer_check', 'en',
        'This is a job interview. You are the interviewer. You are an expert occupational psychologist. ' ||
        'The candidate is evaluated on the following personality trait: {trait_name}. You ask the candidate the following question: {question_text}. ' ||
        'The candidate answers by {answer_text}. You need to tell if it''s possible to grade the candidate on the following criterion: {grading_principle_description}. ' ||
        'If you can apply the criterion to the candidate''s answer, give a score of 1, and if you can''t, give a score of 0. ' ||
        'Answer ONLY with one of these numbers. \n In the following cases the criterion cannot be applied: ' ||
        '\n- The candidate says they have no sufficient or relevant experience to address the question ' ||
        '\n- The candidate provides an answer that is too generic ' ||
        '\n- The candidate uses plural language (e.g., “we did...”) instead of talking about himself or  his experience ' ||
        '\n- The candidate didn''t answer the relevant question \n- The answer of the candidate includes gibberish or incoherent text.' ||
        '\nScore:'),

       ('answer_grading', 'en',
        'This is a job interview. You are the interviewer. ' ||
        'You are an expert occupational psychologist. The candidate is evaluated on the following personality trait: {trait}.' ||
        ' You ask the candidate the following question: {question}. The candidate answers by ''{answer}''.' ||
        ' You need to grade the candidate on the following criterion: ' ||
        '{grading_principle} with a score between 1,2,3,4 and 5 concerning the answer of the candidate and the following grading principles. ' ||
        'Answer ONLY with one of these numbers. \n Grading principles: ' ||
        '\n- Score 1 if the candidate {level_1} ' ||
        '\n- Score 2 if the candidate {level_2} ' ||
        '\n- Score 3 if the candidate {level_3} ' ||
        '\n- Score 4 if the candidate {level_4}' ||
        '\n- Score 5 if the candidate {level_5}' ||
        '\n Score:'),


--

       ('trait_score', 'en',
        'This is a job interview. You are an expert occupational psychologist.' ||
        ' The candidate is evaluated on the following personality trait: {trait}. ' ||
        'You ask the candidate the following question: {question}. ' ||
        'The candidate''s response is: {answer}.' ||
        ' You need to grade this answer by giving a score between 1 and 5 based on the answer of the candidate, ' ||
        'the scores description below and the following grades criterions and their -1 to 5 grades with explanation. ' ||
        '\n-1 to 5 grades based on the candidate’s answer: {grading_principles}.' ||
        '\n Score description: ' ||
        '\n1 is the lowest score ' ||
        '\n2 below average ' ||
        '\n3 average ' ||
        '\n4 above the average ' ||
        '\n5 highest score. ' ||
        '\nScore:'),

       ('trait_score_grading_principles', 'en',
        '\n Criterion {principle_number}. ' ||
        'Title: {principle_description}. ' ||
        'Grade: {score}. Explanation: ' ||
        'The candidate {score_explanation}'),

       ('trait_summary_intro_composer', 'en', 'The summary written by psychologists:'),


       ('trait_summary_intro', 'en',
        'We are at the end of a job interview. ' ||
        'The candidate was evaluated on the following personality trait: {trait}. ' ||
        'The candidate received the {score} score. ' ||
        'You have 2 inputs: /n1. Remarks from psychologists as follow: {composer} {psychologist_summary} ' ||
        './n2 ' ||
        'The grades the candidate received on a scale of 0-5 on several criteria as follows: {grading_principle_explanation}.'),

       ('trait_summary_ending', 'en',
        '\n\nWrite a short and clear summary (2 sentences) based on your inputs and the score that the candidate received.' ||
        'Here are some instructions you need to follow to write it in the best way:' ||
        '/n1 .Base your summary on the two inputs but don’t share the source of the information or input. Just describe the candidate performance. ' ||
        '/n2. The summary must be focused on the candidate. ' ||
        '/n3.Don''t talk about the interview, talk about the high and low conclusions found in your inputs. ' ||
        ' Talk only about high (4-5 grades) criteria if the candidate received the ' ||
        '{level_5} or {level_4}  score. Talk only about low criterions (1-2 grades) criterions if the candidate received the ' ||
        '{level_1} or {level_2} score. If the candidate received the {level_1} or the {level_5} don"t use the word "however" or sentence with a contrast word. ' ||
        'If the candidate received {level_3} underline the high, medium and the low remarks he received. /n4.The summary must be written in the present tense. ' ||
        '/n5 .' ||
        'Don''t be inconclusive. ' ||
        'Don''t judge if the candidate is suitable for the job or not, focus only on the presentation of his skills. ' ||
        '/n6. Use only the pronoun ''he'' when you are talking about the candidate or say ''the candidate'', nothing else. ' ||
        '/n7. Don''t use the word ''role'' but use the word ''job'' instead. /n8.Don''t use the word ''capacity'' but use the word ''capability'' instead. ' ||
        '/n9. Don''t use the word ''Framework''./n10. Stay objective, unbiased, impartial and profesionnal. ' ||
        'Use only HR profesionnal vocabulary. Don''t use ''overall'' sentences.' ||
        'Don''t use positive or negative extreme superlatives (like ''excels'' or ''exceptional'' for example). ' ||
        'Do not add that the candidate ''needs improvement''. ' ||
        'Do not be too positive or too negative about the candidate. Stay neutral. ' ||
        '/n11.' ||
        'Be logical, try making sense and do not contradict yourself.\n\nThe summary:'),


       ('families_summary', 'en',
        'We are at the end of a job interview. ' ||
        'The candidate was evaluated on several personality traits. ' ||
        'The candidate receives detailed remarks: '' {traits_summaries} '' about {family_trait_name}. ' ||
        'Rephrase it to receive a clear and short final summary of {family_trait_name} for the profile of the candidate based on the detailed remarks you received.' ||
        ' Here are some instructions you need to follow to write the best final summary: ' ||
        ' \n1. The summary must be written in the present simple. ' ||
        '\n2. The summary must be concise and organized with strong and/or weak qualifications (if there are both) of the candidate underlined in the detailed remarks for each personality trait. ' ||
        'If there are only positive remarks, don''t say that there aren''t any negative qualifications. ' ||
        'If there are only negative remarks, don''t say that there are no positive qualifications. ' ||
        '\n3. Don''t use positive or negative extreme superlatives (like ''excels'', ''exceptional'', ''solid'', ''impressive'', ' ||
        'or ''excellent'' for example), stay objective, unbiased, impartial, and professional. ' ||
        '\n4. Don''t be inconclusive. Don''t judge if the candidate is suitable for the job or not, ' ||
        'focus only on the presentation of his skills. Do not tell that the candidate ''needs improvement'' but that ''he''s low in ...'' for example. ' ||
        '\n5. Do not be too positive or too negative about the candidate. Stay neutral. \n6. Do not use ''overall'' sentences. ' ||
        'Do not conclude anything about the relation between the summary and whether the candidate is a good match for the job. ' ||
        'Do not explain literally that ''this summary reflects the candidate''s {family_trait_name}. ' ||
        '\n7. Use the pronoun ''he'' or the expression ''the candidate'' only when you are talking about the candidate, nothing else. ' ||
        '\n8. Be logical and decisive. If there are contradictions in the remarks you received, avoid them, and don''t talk about them.' ||
        ' Do not say about a trait that is both average and high level for example. Avoid repetitions. ' ||
        '\n9. Start the summary with: ''The candidate'' and mention the name: {family_trait_name}. ' ||
        '\n10. When a certain attribute cannot be evaluated, there is no need to write about ''difficulty'' or ''struggle'' in evaluating the attribute. ' ||
        'Do not write about this attribute at all. If the interview did not provide enough information to evaluate the candidate''s ' ||
        'suitability for the position for this attribute do not add this attribute to the final summary. ' ||
        '\n11. Do not use the expression ''family trait'' or ''family attribute''. \nThe final summary:'),

       ('trait_summary_grading_principles_explanation', 'en',
        'The candidate received {score} at {description} criterion. It means that he {explanation}'),

       ('summary_correction_system', 'en',
        'You are a perfect corrector. Correct the following text based on the following remarks if the candidate received sufficient information to output a conclusion. Else, if the interview does not provide enough information to evaluate, conclude by the same way. \nRemarks: \n1. In every set of characteristics, we will always request that the verbal descriptions follow this order:\na. First: a verbal description of the traits with high scores (4.5).\nb. Next, traits with medium scores.\nc. Finally, traits with low scores.\n2.Connecting words should appear between the traits.\n3. It is generally permissible and necessary to shorten the text because there is currently redundancy in describing the abilities within the traits. It should be defined that for each trait, there will only be 2 sentences, no more.\n4. It is essential to avoid any mention of numerical scores or references to criteria within the text. For example, "as found in the low score in criterion X.".\n5. It is imperative to omit any references related to examples of answers the candidate gave, such as "he describes a situation where he was generous."\n6. When the score of the trait is average – 3, the description should be as follows – first the good aspects of the trait, a word of connection like "however" and then the cones. Not to divide the trait into different paragraphs. \n7. Make sure there is no mentioning of scored of grading principles and of the traits. \n8. In the trait of commitment and loyalty there should be no mention of authentic answers or behavior. \n9. No mention of "he" at all – should be replaced with the words – "the candidate" and if the sentence contains the pronoun "his" it should be replaced with the word ' +
        "one's" +
        ' or rephrase the sentence. \n10. In the trait of prioritization there should not be any mentioning of level of thinking – not one or two or different – please tell the engine to ignore this grading principle while reporting. \n11. In the trait of motivation there should be no reference to decision making.'),

       ('trait_summary', 'he', 'אנו בסיום ראיון עבודה. המועמד הוערך על פי התכונה האישית הבאה: {trait}. המועמד קיבל את הדירוג {score}. לרשותך שני קלטים:
            1. הערות מפסיכולוגים כדלקמן: {trait_summaries}.
            2. הציונים שקיבל המועמד בסולם של 0-5 על פי מספר קריטריונים כדלהלן: {grading_principles_explanations}.

            כתוב סיכום קצר וברור בשתי משפטים בהתבסס על שני הקלטים הללו ועל הדירוג שקיבל המועמד. הנה כמה הוראות שעליך לעקוב אחריהן כדי לכתוב את זה בצורה הטובה ביותר:
            1. הסיכום יבוסס על שני הקלטים אך לא ישתף את מקור המידע או הקלט. רק תתאר את ביצועי המועמד.
            2. הסיכום צריך להתמקד במועמד.
            3. אל תדבר על הראיון, דבר על המסקנות הגבוהות והנמוכות שנמצאו בקלטים שלך. דבר רק על קריטריונים גבוהים (דירוגים 4-5) אם המועמד קיבל את הדירוג גבוה או הגבוה ביותר. דבר רק על קריטריונים נמוכים (דירוגים 1-2) אם המועמד קיבל את הדירוג נמוך או הנמוך ביותר. אם המועמד קיבל את הדירוג נמוך או הנמוך ביותר או גבוה או הגבוה ביותר, אל תשתמש במילה 'אולם' או במשפט עם מילת ניגוד. אם המועמד קיבל ממוצע, הדגש את ההערות הגבוהות, הבינוניות והנמוכות שהוא קיבל.
            4. הסיכום חייב להיכתב בזמן ההווה.
            5. אל תהיה לא חד משמעי. אל תשפוט אם המועמד מתאים לעבודה או לא, רק תתמקד בהצגת היכולות שלו.
            6. השתמש רק בכינוי גוף ''הוא'' כשאתה מדבר על המועמד או אמור ''המועמד'', דבר אחר לא.
            7. אל תשתמש במילה ''תפקיד'' אלא במילה ''עבודה''.
            8. אל תשתמש במילה ''יכולת'' אלא במילה ''יכולתו''.
            9. אל תשתמש במילה ''מסגרת''.
            10. שמור על אובייקטיביות, חוסר נטייה, חוסר פנייה ומקצועיות. השתמש רק בלשון מקצועית בתחום משאבי אנוש. אל תשתמש במשפטים כוללניים. אל תשתמש בסופרלטיבים קיצוניים חיוביים או שליליים (כמו ''מצטיין'' או ''יוצא מן הכלל'' לדוגמה). אל תוסיף שהמועמד ''זקוק לשיפור''. אל תהיה יתר חיובי או שלילי מדי לגבי המועמד. שמור על נייטרליות.
            11.הסיכום ייכתב בשני משפטים , תוך הקפדה שלא תהיה סתירה ביניהם. אם המשפט הראשון מתאר תכונה חיובית, המשפט השני צריך להרחיב, להסביר או לפרט אותה ולא לערער עליה.
12.יש להימנע מסתירות בין חלקי המשפט. אם המשפט מתחיל בתיאור חיובי, יש להמשיך בטון חיובי ולחזק את הנאמר, ללא הוספת חלקים שמערערים על האמירה הראשונית.

            הסיכום:
            '),

       ('families_summary', 'he', 'אנו בסוף ראיון עבודה. המועמד הוערך על מספר תכונות אישיות. המועמד קיבל את ההערות המפורטות הבאות: ''{traits_summaries}'' לגבי משפחת התכונות {family_trait_name} המורכבות מתכונות הבאות {traits}. נא לנסח מחדש כדי לקבל סיכום סופי קצר וברור על תכונה זו ממשפחת התכונות לפרופיל המועמד, בהתבסס על ההערות המפורטות שקיבלת. הנה כמה הוראות שעליך לעקוב אחריהן כדי לכתוב את הסיכום הסופי הטוב ביותר:
1. הסיכום חייב להיכתב בזמן ההווה הפשוט.
2. הסיכום חייב להיות תמציתי ומאורגן עם הדגשה על הכישורים החזקים ו/או החלשים (אם ישנם שניהם) של המועמד שהודגשו בהערות המפורטות לכל תכונת אישיות. אם יש הערות חיוביות בלבד, אל תגיד שאין כישורים שליליים. אם יש הערות שליליות בלבד, אל תגיד שאין כישורים חיוביים.
3. אל תשתמש בסופרלטיבים חיוביים או שליליים קיצוניים (כמו ''מצטיין'' או ''יוצא מן הכלל'' לדוגמה), שמור על אובייקטיביות, חוסר נטייה, חוסר פנייה ומקצועיות.
4. אל תהיה לא חד משמעי. אל תשפוט אם המועמד מתאים לעבודה או לא, התמקד רק בהצגת היכולות שלו. אל תגיד שהמועמד ''זקוק לשיפור'' אלא ש''הוא חלש ב...'' לדוגמה.
5. אל תהיה יתר חיובי או שלילי על המועמד. שמור על נייטרליות.
6. אל תשתמש במשפטים כוללניים.
7. השתמש בכינוי גוף ''הוא'' או בביטוי ''המועמד'' בלבד כאשר אתה מדבר על המועמד, דבר אחר לא.
8. תהיה לוגי. אם יש סתירות בהערות שקיבלת, הימנע מהן, אל תדבר עליהן. חמוק מחזוריות.
9. התחל ב: ''המועמד'' וציין שזה נוגע לתכונה זו ממשפחת התכונות: {family_trait_name}.
10. תדבר על כל התכונות שאתה מקבל בהערות.
11. אם הראיון לא סיפק מספיק מידע להערכת התאמת המועמד לעבודה לגבי תכונה זו, אל תוסיף תכונה זו לסיכום הסופי.
12.תדבר על כל התכונות שנמצאות בהערות אם יש מספיק מידע לגביהן

הסיכום הסופי:

'),
       ('language_gender_correction', 'he', 'Prompt for each family attribute summary:
Rewrite the following Hebrew text using {gender} grammatical forms where applicable: {hebrew_text}'),

       ('summary_correction', 'he',
        'Correct the following text in HEBREW based on the following remarks.
Remarks: {remarks}.
Hebrew Text: {text}'),

       ('summary_correction_system', 'he',
        'You are an expert occupational psychologist and Hebrew language specialist. Your task is to review and correct a candidate summary related to the {family}. Only incorporate corrections if they align logically with the remarks provided. Do not introduce any attributes or information not present in the remarks or the text. Ensure the final text is coherent, logically consistent, and contradictions-free. In other words, a contradictions-free text is one where all statements align logically without conflicting information about the candidate.'),

       ('summary_correction_cognitive_remarks', 'he', '- יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה. 
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",   
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
- גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".  
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- אין להתייחס בתוך הפסקאות להתאמה לעבודה.  
- להבחנה בין עיקר לטפל, יש להוריד רמות חשיבה. במקום "מתייחס ל-3-2 רמות חשיבה", יש לכתוב "מתייחס למעט קריטריונים בעת תיעדוף משימות". במקום "מעט קריטריונים", יש לכתוב "מספר קריטריונים".  
- במקרה של יצירתיות טובה: "המועמד מפגין חשיבה יצירתית ומציג רעיונות חדשניים וייחודיים".  
- במקרה של היעדר יצירתיות: "המועמד אינו מפגין חשיבה יצירתית ואינו מעלה רעיונות חדשניים".  
- אין לנסח התייחסויות מעורפלות, כגון "רמת היצירתיות שלו ממוצעת" או "לעיתים יש ביטוי ליצירתיות".'),


       ('summary_correction_performance_remarks', 'he', '- יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה.
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",     
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
- גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".  
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- לא להתייחס בתוך הפסקאות להתאמה לעבודה
- אם רשום ״המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי״ צריך להוריד או לשנות ב- ״המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי״
- לפעמים רשום "המועמדת מציגה כישורי ביצוע עם משימתיות גבוהה..." צריך לכתוב - "המועמדת מציגה משימתיות גבוהה..." כלומר, להוריד את המילים כישורי ביצוע.
- אם ביסודיות כתוב את המשפט ״מעיד על עצמו כיסודי״ צריך להוריד.
- אם אין ברשימת התכונות משימתיות אל תכתוב שהמועמד מציג משימתיות
- אל תתייחס ליכולות חשיבה בפרק של מאפייני ביצוע'),


       ('summary_correction_interpersonal_remarks', 'he', '-יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה.
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",   
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא לכתוב רק "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
- גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".    
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- אין להתייחס בתוך הפסקאות להתאמה לעבודה.  
- אם רשום "המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי", יש לשנות ל"המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי".  
- בתכונת שירותיות, במקום לכתוב "עם זאת, הוא אינו עונה על הקריטריון של ויסות רגשי אל מול עלבון מצד לקוח ומשקיע זמן מסוים בלבד במציאת פתרון יעיל והגיוני ללקוח", יש לכתוב: "עם זאת, הוויסות הרגשי שלו אל מול עלבון מצד לקוח חלקי/אינו מיטבי, והוא משקיע זמן מסוים בלבד במציאת פתרון יעיל והגיוני ללקוח".  
- בתכונת מודעות עצמית, אין לציין שהמועמד מסוגל לנמק או לא מסוגל לנמק או להסביר את הבחירות שלו, וגם אין להתייחס לאותנטיות שלו.  
- בתכונת עבודת צוות, אין לכתוב שהמועמד לוקח על עצמו "עבודה חברתית", אלא "תפקיד חברתי".  
- אם אין התייחסות לתכונת שירותיות, אין להתייחס לקשב ללקוחות.'),

       ('summary_correction_organizational_remarks', 'he', '- יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה. 
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",   
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- "מפגינה כישורים חזקים" צריך שיופיע במקום "חזקים" המילים: טובים, גבוהים.  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא לכתוב רק "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה. למשל, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה או מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תשאיר במסוגלות של המועמד בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם ...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם משהו כללי על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- גם אם הציון גבוה, לא להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן", "משמעותית". יש להשתמש במונחים כמו "גבוה", "טוב מאוד".  
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- לא להתייחס בתוך הפסקאות להתאמה לעבודה.  
- אם רשום "המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי", יש לשנות זאת ל"המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי".');

       ('summary_correction_cognitive_remarks', 'he', '1. כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
2. לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
3. יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
4. במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
5. אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
6. לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
7. אל תכתוב בתחילת פסקה "המועמד מציג כישורים טובים". יש לכתוב את שם הכישורים הרלוונטי, לדוגמה: "כישורי ביצוע" בפסקה של ביצוע, או "כישורי חשיבה" בפסקה של חשיבה.  
8. אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
9. במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
10. אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
11. אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
12. גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".  
13. יש להימנע מסתירות פנימיות במשפטים. אם מתארים תכונה חיובית, יש לוודא שהמשך המשפט אינו מערער עליה ללא הבהרה. לדוגמה, במקום לכתוב "היא מפגינה חשיבה יצירתית ומציעה פתרונות שאינם קונבנציונליים, אך הרעיונות שלה בעלי חדשנות מועטה", יש לכתוב: "היא מפגינה חשיבה יצירתית עם דגש על פתרונות שאינם קונבנציונליים, אך לעיתים הרעיונות דורשים חידוד נוסף לחדשנות".  
14. אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
15. אל תשתמש במילים "כישורים מגוונים".  
16. אין להתייחס בתוך הפסקאות להתאמה לעבודה.  
17. להבחנה בין עיקר לטפל, יש להוריד רמות חשיבה. במקום "מתייחס ל-3-2 רמות חשיבה", יש לכתוב "מתייחס למעט קריטריונים בעת תיעדוף משימות". במקום "מעט קריטריונים", יש לכתוב "מספר קריטריונים".  
18. במקרה של יצירתיות טובה: "המועמד מפגין חשיבה יצירתית ומציג רעיונות חדשניים וייחודיים".  
19. במקרה של היעדר יצירתיות: "המועמד אינו מפגין חשיבה יצירתית ואינו מעלה רעיונות חדשניים".  
20. אין לנסח התייחסויות מעורפלות, כגון "רמת היצירתיות שלו ממוצעת" או "לעיתים יש ביטוי ליצירתיות".  '),

       ('summary_correction_performance_remarks', 'he', '- יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה.
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",     
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
- גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".  
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- לא להתייחס בתוך הפסקאות להתאמה לעבודה
- אם רשום ״המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי״ צריך להוריד או לשנות ב- ״המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי״
- לפעמים רשום "המועמדת מציגה כישורי ביצוע עם משימתיות גבוהה..." צריך לכתוב - "המועמדת מציגה משימתיות גבוהה..." כלומר, להוריד את המילים כישורי ביצוע.
- אם ביסודיות כתוב את המשפט ״מעיד על עצמו כיסודי״ צריך להוריד.
- אם אין ברשימת התכונות משימתיות אל תכתוב שהמועמד מציג משימתיות
- אל תתייחס ליכולות חשיבה בפרק של מאפייני ביצוע'),


       ('summary_correction_interpersonal_remarks', 'he', '-יש להימנע מסתירות פנימיות במשפטים. אם מתחילים בתיאור תכונה חיובית, יש לוודא שהמשך המשפט מחזק או מסביר את התכונה ולא סותר אותה.
- כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
- אל תכתוב בתחילת פסקה "המועמד מציג כישורי",   
- לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
- יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
- במקום "מפגינה כישורים חזקים", יש לכתוב "טובים" או "גבוהים".  
- אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא לכתוב רק "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה בלבד. לדוגמה, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
- לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה" או "מפגינה".  
- אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תתמקד במסוגלות שלו בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם...".  
- במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
- אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם בצורה כללית על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
- אם תכונה מקבלת ציון אפס, אין להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
- גם אם הציון גבוה, אין להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן" או "משמעותית". יש להשתמש במונחים כמו "גבוה" או "טוב מאוד".    
- אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
- אל תשתמש במילים "כישורים מגוונים".  
- אין להתייחס בתוך הפסקאות להתאמה לעבודה.  
- אם רשום "המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי", יש לשנות ל"המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי".  
- בתכונת שירותיות, במקום לכתוב "עם זאת, הוא אינו עונה על הקריטריון של ויסות רגשי אל מול עלבון מצד לקוח ומשקיע זמן מסוים בלבד במציאת פתרון יעיל והגיוני ללקוח", יש לכתוב: "עם זאת, הוויסות הרגשי שלו אל מול עלבון מצד לקוח חלקי/אינו מיטבי, והוא משקיע זמן מסוים בלבד במציאת פתרון יעיל והגיוני ללקוח".  
- בתכונת מודעות עצמית, אין לציין שהמועמד מסוגל לנמק או לא מסוגל לנמק או להסביר את הבחירות שלו, וגם אין להתייחס לאותנטיות שלו.  
- בתכונת עבודת צוות, אין לכתוב שהמועמד לוקח על עצמו "עבודה חברתית", אלא "תפקיד חברתי".  
- אם אין התייחסות לתכונת שירותיות, אין להתייחס לקשב ללקוחות.'),

       ('summary_correction_organizational_remarks', 'he', '1. כאשר מציינים כישורים או ביצועים, יש להוסיף תיאור שמבהיר את הרמה (לדוגמה: גבוהים, ממוצעים או נמוכים), כדי לספק מידע מלא וברור.  
2. לא להתייחס לדירוג של התכונה במלל, לא לכתוב שהדירוג ממוצע או נמוך או גבוה.  
3. יש לנסח משפטים שלמים ומלאים בלבד, ללא שימוש בחצאי משפטים או ניסוחים חלקיים. לדוגמה, במקום לכתוב "המועמדת מציגה כישורי חשיבה", יש לנסח "המועמדת מציגה כישורי חשיבה טובים או ממוצעים או נמוכים או גבוהים" בהתאם לציון.  
4. "מפגינה כישורים חזקים" צריך שיופיע במקום "חזקים" המילים: טובים, גבוהים.  
5. אל תכתוב דברים כמו "בנוגע לתכונה זו ממשפחת התכונה:...". לדוגמה, לא לכתוב "ביסודיות, היא מפגינה תשומת לב לפרטים", אלא לכתוב רק "היא מפגינה תשומת לב לפרטים". גם לא "הכישורים של המועמדת בתכונה מסוימת", אלא תיאור התכונה. למשל, במקום "הכישורים של המועמדת במודעות עצמית", לכתוב "המועמדת מציגה מודעות עצמית".  
6. לא לכתוב "הכישורים של המועמדת מפגינים או מראים", אלא "המועמדת מציגה או מפגינה".  
7. אל תכתוב בתחילת פסקה "המועמד מציג כישורים טובים", אלא תתחיל עם שם הכישורים (למשל: "כישורי ביצוע" בפסקה של ביצוע, "כישורי חשיבה" בפסקה של חשיבה).  
8. אל תכתוב שמועמד "מתאר משהו ספציפי". אם אתה מתקן משהו, תשאיר במסוגלות של המועמד בתכונות שכתובות בפסקה. לדוגמה, במקום "הוא מתאר מצב של ארבע", תכתוב: "מסוגל להתמודד עם ...".  
9. במקום לכתוב "המועמד, בנוגע לכישורים", יש לכתוב "הכישורים של המועמד".  
10. אם יש תכונות שבהן המועמד טוב ותכונות שבהן הוא חלש יותר, אל תסכם משהו כללי על הכישורים. יש להתייחס לכל תכונה בנפרד. לדוגמה, לא לכתוב "למועמד כישורים מגוונים".  
11. אם תכונה מקבלת ציון אפס, לא להתייחס לכך שאי אפשר להעריך אותה בגלל חוסר מידע.  
12. גם אם הציון גבוה, לא להשתמש במונחים סופרלטיביים מדי כמו "מרשימה", "יוצא דופן", "משמעותית". יש להשתמש במונחים כמו "גבוה", "טוב מאוד".  
13. יש להימנע מסתירות פנימיות במשפטים. אם מתארים תכונה חיובית, יש לוודא שהמשך המשפט אינו מערער עליה ללא הבהרה. במקום לכתוב משפט כמו "היא מפגינה חשיבה יצירתית ומציעה פתרונות שאינם קונבנציונליים, אך הרעיונות שלה בעלי חדשנות מועטה", יש לנסח מחדש כך שהמשפט יהיה ברור ועקבי. לדוגמה: "היא מפגינה חשיבה יצירתית עם דגש על פתרונות שאינם קונבנציונליים, אך לפעמים הרעיונות דורשים חידוד נוסף לחדשנות".  
14. אין להשתמש בנקודתיים במשפטים. אם יש צורך בהסבר או ברשימה, יש לנסח את הדברים בצורה שונה כך שלא יידרשו נקודתיים.  
15. אל תשתמש במילים "כישורים מגוונים".  
16. לא להתייחס בתוך הפסקאות להתאמה לעבודה.  
17. אם רשום "המוטיבציה נובעת מחשיבה ערכית ותפיסה חברתית משמעותית כמו גם רצון למימוש עצמי", יש לשנות זאת ל"המוטיבציה נובעת מרצון לתרום למערכת (או לארגון) כמו גם רצון למימוש עצמי".  
');



INSERT INTO interview_summary_sentences (sentence, score, language)
VALUES ('We didn''t acquire sufficient information during the interview to determine an overall rating.', 0, 'en'),
       ('We couldn''t gather enough data during the interview to establish a comprehensive rating.', 0, 'en'),
       ('The interview didn''t provide us with adequate information to formulate a final evaluation.', 0, 'en'),
       ('The interview fell short in yielding the necessary insights for an overall assessment.', 0, 'en'),
       ('We were unable to collect the required details during the interview to make a conclusive rating.', 0, 'en'),
       ('The interview didn''t furnish us with the complete information needed for an overall judgment.', 0, 'en'),
       ('לא קיבלנו מספיק מידע במהלך הראיון כדי לקבוע ציון סופי.', 0, 'he'),
       ('לא הצלחנו לאסוף מידע מספיק במהלך הראיון כדי להגיע לציון סופלא הצלחנו לאסוף מספיק נתונים במהלך הראיון כדי לקבוע ציון סופי',
        0, 'he'),
       ('הראיון לא סיפק לנו מידע מספק על מנת לגבש הערכה סופית.', 0, 'he'),
       ('הראיון לא הצליח להניב את התובנות הדרושות להערכה כוללת.', 0, 'he'),
       ('לא הצלחנו לאסוף את הפרטים הנדרשים במהלך הראיון על מנת לתת ציון סופי.', 0, 'he'),
       ('הראיון לא סיפק לנו את המידע המלא הנדרש להערכה סופית.', 0, 'he'),
       ('הראיון לא סיפק לנו את המידע המלא הדרוש להערכה כוללת.', 0, 'he'),
       ('Limited alignment with the role''s requirements.', 1, 'en'),
       ('Insufficient alignment with the role.', 1, 'en'),
       ('Inadequate suitability for the position.', 1, 'en'),
       ('A poor fit for the job.', 1, 'en'),
       ('Limited correspondence with the role''s requirements.', 1, 'en'),
       ('A mismatch with to position.', 1, 'en'),
       ('התאמה גבולית לדרישות התפקיד.', 1, 'he'),
       ('התאמה לא מספקת לדרישות התפקיד.', 1, 'he'),
       ('התאמה לא טובה לתפקיד.', 1, 'he'),
       ('אי התאמה לתפקיד.', 1, 'he'),
       ('חוסר התאמה לדרישות התפקיד.', 1, 'he'),
       ('התאמה שאינה מספקת ביחס לדרישות התפקיד.', 1, 'he'),
       ('Limited alignment with the role''s requirements.', 2, 'en'),
       ('Insufficient alignment with the role.', 2, 'en'),
       ('Inadequate suitability for the position.', 2, 'en'),
       ('A poor fit for the job.', 2, 'en'),
       ('Limited correspondence with the role''s requirements.', 2, 'en'),
       ('A mismatch with to position.', 2, 'en'),
       ('התאמה גבולית לדרישות התפקיד.', 2, 'he'),
       ('התאמה לא מספקת לדרישות התפקיד.', 2, 'he'),
       ('התאמה לא טובה לתפקיד.', 2, 'he'),
       ('אי התאמה לתפקיד.', 2, 'he'),
       ('חוסר התאמה לדרישות התפקיד.', 2, 'he'),
       ('התאמה שאינה מספקת ביחס לדרישות התפקיד.', 2, 'he'),
       ('An intermediate fit for the job.', 3, 'en'),
       ('A moderate degree of alignment with the role.', 3, 'en'),
       ('A fair suitability for the position.', 3, 'en'),
       ('An average fit for the job.', 3, 'en'),
       ('A reasonable match to the position.', 3, 'en'),
       ('An adequate fit for the job.', 3, 'en'),
       ('התאמה בינונית לתפקיד.', 3, 'he'),
       ('מידת ההתאמה ממוצעת לתפקיד.', 3, 'he'),
       ('התאמה הוגנת ביחס לדרישות התפקיד.', 3, 'he'),
       ('התאמה סבירה ביחס לדרישות התפקיד.', 3, 'he'),
       ('התאמה נאותה לתפקיד.', 3, 'he'),
       ('התאמה ממוצעת לתפקיד.', 3, 'he'),
       ('A remarkable fit for the job.', 4, 'en'),
       ('Very strong alignment with the role''s requirements.', 4, 'en'),
       ('An impressive fit for the job.', 4, 'en'),
       ('A standout candidate for the role.', 4, 'en'),
       ('A noteworthy candidate for the role.', 4, 'en'),
       ('A robust correspondence with the role''s demands.', 4, 'en'),
       ('התאמה מעולה לתפקיד.', 4, 'he'),
       ('התאמה חזקה לדרישות התפקיד.', 4, 'he'),
       ('התאמה מרשימה לתפקיד.', 4, 'he'),
       ('התאמה ראויה לציון ביחס לדרישות התפקיד.', 4, 'he'),
       ('התאמה בולטת ביחס לדרישות התפקיד.', 4, 'he'),
       ('התכתבות איתנה עם דרישות התפקיד.', 4, 'he'),
       ('An excellent suitability for the position.', 5, 'en'),
       ('An outstanding match to the role.', 5, 'en'),
       ('A perfect correspondence with the position.', 5, 'en'),
       ('A superb suitability for the position.', 5, 'en'),
       ('A remarkable alignment with the position.', 5, 'en'),
       ('A superior fit for the role.', 5, 'en'),
       ('התאמה יוצאת-דופן לתפקיד.', 5, 'he'),
       ('התאמה מצוינת לתפקיד.', 5, 'he'),
       ('התכתבות מושלמת עם דרישות התפקיד.', 5, 'he'),
       ('התאמה עילאית לתפקיד.', 5, 'he'),
       ('התאמה נהדרת לתפקיד.', 5, 'he'),
       ('מידת ההתאמה לתפקיד יוצאת מן הכלל.', 5, 'he');

insert into customers (id, company_name, account_id)
values ('1b1b587d-423f-4470-adab-2d2f3a59ccef', 'IDF', '1f90c3b0-6e76-46a4-b83b-022ca347d72c');