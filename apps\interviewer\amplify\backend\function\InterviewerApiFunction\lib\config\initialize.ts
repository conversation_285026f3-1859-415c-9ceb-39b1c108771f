import { NextFunction, Request, Response } from "express";
import { DB, initDB } from "@eva/drizzle";
import { Logger } from "@eva/logger";
import { InterviewService } from "../services/InterviewService";
import { CloudfrontAccessPair, getSecret } from "@eva/aws-utils";
import { PilatSecrets } from "@eva/shared-common";
import { setupLogging } from "@eva/logger";

let db: DB | null = null;
let logger: Logger = setupLogging();
let pilatSecrets: PilatSecrets | null = null;
let interviewService: InterviewService;
let cloudfrontSignedUrlSecret: CloudfrontAccessPair | null = null;
export const getLogger = () => logger;
export const getInterviewService = () => interviewService;

const initializeResources = async () => {
    if (!db) {
        logger.info("Initializing DB connection");
        db = await initDB();
        logger.info("DB connection initialized");
    }

    if (!cloudfrontSignedUrlSecret) {
        logger.info("Initializing cloudfront secrets");
        cloudfrontSignedUrlSecret = await getSecret<CloudfrontAccessPair>("cloudfront_access_pair");
        logger.info("Cloudfront secrets: ", cloudfrontSignedUrlSecret);
    }

    if (!pilatSecrets) {
        logger.info("Initializing pilat secrets");
        pilatSecrets = await getSecret<PilatSecrets>("pilat/secrets");
        logger.info("Pilat secrets: ", pilatSecrets);
    }

    if (!interviewService) {
        logger.info("Initializing interview service");
        interviewService = new InterviewService(db, logger, pilatSecrets, cloudfrontSignedUrlSecret);
        logger.info("Interview service initialized");
    }

 
};

export const initializeMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
        await initializeResources();
        next();
    } catch (error) {
        logger.error("Error initializing resources", error);
        next(error);
    }
}; 