{"name": "@eva/interviewer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-amplify/ui-react": "^6.1.6", "@aws-sdk/client-s3": "^3.717.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/poppins": "^5.0.12", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/x-data-grid": "^7.23.2", "@reduxjs/toolkit": "^2.2.3", "@types/styled-components": "^5.1.34", "aws-amplify": "^6.0.25", "formik": "^2.4.6", "framer-motion": "^12.6.3", "lottie-react": "^2.4.1", "react": "^18.2.0", "react-countdown": "^2.3.5", "react-countdown-circle-timer": "^3.2.1", "react-dom": "^18.2.0", "react-media-recorder": "^1.6.6", "react-redux": "^9.1.0", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.3", "styled-components": "^6.1.15", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "@eva/shared-common": "workspace:*", "@eva/shared-ui": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-toastify": "^4.0.2", "@types/stylis": "^4.2.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.4.5", "vite": "^4.4.5"}}