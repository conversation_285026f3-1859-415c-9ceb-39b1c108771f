import { createLogger, format, transports, Logger } from 'winston';

class CustomFormatter {
    private run_id: string;

    constructor(run_id?: string) {
        this.run_id = run_id || crypto.randomUUID();
    }

    formatLog(info: any): any {
        info.run_id = this.run_id;
        return info;
    }
}

export const setupLogging = (run_id?: string):Logger => {
    const formatter = new CustomFormatter(run_id);

    const logger = createLogger({
        level: 'info',
        format: format.combine(
            format((info) => formatter.formatLog(info))(),
            format.timestamp(),
            format.printf(
                ({ timestamp, run_id, level, message }) =>
                    `${timestamp} - [${run_id}] - ${level.toUpperCase()} - ${message}`,
            ),
        ),
        transports: [new transports.Console()],
    });

    return logger;
};
