import {
    BuilderSimulation,
    BuilderSimulationQuestionsResponse,
    createLambdaResponseToClientAction,
    FamiliesWithQuestionsForBuilderResponse,
    FamilyForBuilder,
    FamilyId,
    JobDetailsDataResponse,
    JobQuestionsData,
    JobQuestionsForExportResponse,
    JobsPageTablaDataResponse,
    LambdaResponseToClientAction,
    LanguageVariation,
    PilatJobDefinitionNotification,
    PilatSecrets,
    QuestionOptions,
    TableJobData,
    UpdateJobRequest,
} from "@eva/shared-common";
import {
    accounts,
    customerIdMappings,
    customers,
    DB,
    jobs,
    questions,
    simulations,
    traits,
    traitsFamilies,
    and,
    eq,
    inArray,
    sql,
} from "@eva/drizzle";
import { Logger } from "@eva/logger";
import axios from "axios";
export class JobService {
    db: DB;
    logger: Logger;
    familiesIds: FamilyId[] = ["A46", "A47", "A48", "A49"];
    pilatSecrets: PilatSecrets;

    constructor(db: DB, logger: Logger, pilatSecrets: PilatSecrets) {
        this.db = db;
        this.logger = logger;
        this.pilatSecrets = pilatSecrets;
    }

    async getJobsPageTableData(): Promise<JobsPageTablaDataResponse> {
        this.logger.info("Getting jobs page table data");

        const res = await this.db
            .select({
                id: jobs.id,
                companyName: customers.companyName,
                accountName: accounts.name,
                accountId: customers.accountId,
                createdAt: jobs.createdAt,
                jobTitle: jobs.jobTitle,
                language: jobs.language,
                status: jobs.status,
            })
            .from(jobs)
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .innerJoin(accounts, eq(customers.accountId, accounts.id));

        const jobsPageTableData: TableJobData[] = res.map((job) => ({
            id: job.id,
            companyName: job.companyName,
            accountName: job.accountName,
            accountId: job.accountId,
            createdAt: job.createdAt.toISOString(),
            jobTitle: job.jobTitle,
            language: job.language,
            status: job.status,
        }));

        this.logger.info("Jobs page table data", { jobsPageTableData });

        return { jobs: jobsPageTableData };
    }

    async getJob(jobId: string): Promise<JobDetailsDataResponse> {
        this.logger.info("Getting job", { jobId });

        const res = await this.db
            .select({
                accountId: customers.accountId,
                jobId: jobs.id,
                title: jobs.jobTitle,
                language: jobs.language,
                description: jobs.description,
                customerId: jobs.customerId,
                jobQuestionsData: jobs.questions,
                status: jobs.status,
            })
            .from(jobs)
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .where(eq(jobs.id, jobId));

        const { jobQuestionsData, ...rest } = res[0];

        return {
            ...rest,
            openQuestions: jobQuestionsData.openQuestions,
            questionIds: jobQuestionsData.questionIds,
            computedTraits: jobQuestionsData.computedTraits,
            warmupQuestions: jobQuestionsData.warmupQuestions,
            simulations: jobQuestionsData.simulations,
        };
    }

    async checkIfPilatJob(jobId: string): Promise<boolean> {
        const res = await this.db
            .select({
                accountName: accounts.name,
            })
            .from(jobs)
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .innerJoin(accounts, eq(customers.accountId, accounts.id))
            .where(eq(jobs.id, jobId));

        return res[0].accountName === "Pilat";
    }

    async notifyPilatJobReady(jobId: string): Promise<void> {
        try {
            const pilatApiUrl = this.pilatSecrets.PILAT_JOB_DEFINITION_NOTIFICATION_URL;
            const pilatApiKey = this.pilatSecrets.PILAT_JOB_DEFINITION_NOTIFICATION_URL_API_KEY;

            const body = { jobId };

            await axios.post(pilatApiUrl, body, {
                headers: {
                    "x-api-key": pilatApiKey,
                },
            });
            this.logger.info("Pilat job ready notified", { jobId });
        } catch (error) {
            this.logger.error("Failed to notify Pilat job ready", { error });
            throw error;
        }
    }

    async updateJob(updateJobRequest: UpdateJobRequest): Promise<LambdaResponseToClientAction> {
        await this.db
            .update(jobs)
            .set({
                jobTitle: updateJobRequest.title,
                description: updateJobRequest.description,
                customerId: updateJobRequest.customerId,
                language: updateJobRequest.language,
                status: "ready",
                questions: {
                    openQuestions: updateJobRequest.openQuestions,
                    questionIds: [
                        ...updateJobRequest.A46,
                        ...updateJobRequest.A47,
                        ...updateJobRequest.A48,
                        ...updateJobRequest.A49,
                    ],
                    computedTraits: updateJobRequest.computedTraits,
                    warmupQuestions: updateJobRequest.warmupQuestions,
                    simulations: updateJobRequest.simulations,
                },
            })
            .where(eq(jobs.id, updateJobRequest.jobId));

        if (await this.checkIfPilatJob(updateJobRequest.jobId)) {
            await this.notifyPilatJobReady(updateJobRequest.jobId);
        }

        return createLambdaResponseToClientAction(200, {
            message: "Job updated successfully",
            type: "success",
        });
    }

    async getCustomerLogo(customerId: string): Promise<string | null> {
        const res = await this.db
            .select({
                logo: customers.logoLink,
            })
            .from(customers)
            .where(eq(customers.id, customerId));

        return res.length && res[0].logo ? res[0].logo : null;
    }

    async getCustomerExternalId(customerId: string): Promise<string> {
        try {
            const res = await this.db
                .select({
                    externalCustomerId: customerIdMappings.externalId,
                })
                .from(customers)
                .innerJoin(customerIdMappings, eq(customerIdMappings.internalId, customerId))
                .where(eq(customers.id, customerId));

            if (res.length === 0) throw new Error("Customer not found");

            return res[0].externalCustomerId;
        } catch (error) {
            this.logger.error(`Failed to get customer name | ${error} | ${customerId}`);
            throw error;
        }
    }

    async getFamiliesWithTraitsAndQuestionsForBuilder(): Promise<FamiliesWithQuestionsForBuilderResponse> {
        try {
            const familiesWithTraitsAndQuestions = await this.db
                .select({
                    familyId: traitsFamilies.id,
                    family: traitsFamilies.family,
                    traitId: traits.id,
                    trait: traits.trait,
                    questionId: questions.id,
                    questionOptions: questions.options,
                })
                .from(traitsFamilies)
                .leftJoin(traits, eq(traitsFamilies.id, traits.familyId))
                .leftJoin(
                    questions,
                    and(eq(traits.id, questions.traitId), eq(sql<string>`${questions.options}->>'type'`, "general"))
                );

            const familiesMap: { [key: string]: FamilyForBuilder } = {};

            for (const record of familiesWithTraitsAndQuestions) {
                if (!record.familyId || !record.family) continue;

                if (!familiesMap[record.familyId]) {
                    familiesMap[record.familyId] = {
                        computedTraits: [],
                        family: record.family as LanguageVariation,
                        family_id: record.familyId as FamilyId,
                        questions: [],
                    };
                }

                if (record.questionId && record.questionOptions && record.traitId && record.trait) {
                    familiesMap[record.familyId].questions.push({
                        question_id: record.questionId,
                        question: record.questionOptions as QuestionOptions,
                        trait: record.trait as LanguageVariation,
                        trait_id: record.traitId,
                    });
                }

                if (record.traitId && record.trait && !record.questionId) {
                    familiesMap[record.familyId].computedTraits.push({
                        trait: record.trait as LanguageVariation,
                        id: record.traitId,
                    });
                }
            }

            const familiesData: FamilyForBuilder[] = Object.values(familiesMap)
                .map((family) => ({
                    ...family,
                    questions: family.questions.sort((a, b) => a.question.order - b.question.order),
                }))
                .sort((a, b) => a.family_id.localeCompare(b.family_id));

            return { families: familiesData };
        } catch (error) {
            this.logger.error("Failed to fetch families with traits and questions");
            throw error;
        }
    }

    async getJobQuestionsForExport(jobId: string): Promise<JobQuestionsForExportResponse> {
        const jobResult = await this.db
            .select({
                questions: jobs.questions,
                language: jobs.language,
                customerId: jobs.customerId,
            })
            .from(jobs)
            .where(eq(jobs.id, jobId))
            .limit(1);

        if (!jobResult.length) {
            throw new Error("Job not found");
        }

        const { questions: questionsJson, language, customerId } = jobResult[0];

        const jobQuestionsData = questionsJson as JobQuestionsData;

        const questionResults = await this.db
            .select({
                id: questions.id,
                options: questions.options,
                trait: traits.trait,
            })
            .from(questions)
            .innerJoin(traits, eq(questions.traitId, traits.id))
            .where(inArray(questions.id, jobQuestionsData.questionIds));

        const sortedQuestions = questionResults.sort((a, b) => {
            return a.options.order - b.options.order;
        });

        const questionsData = sortedQuestions.map((question) => {
            const options = (question.options as QuestionOptions).options;
            const questionLanguage = language as keyof LanguageVariation;
            const questionText =
                customerId in options
                    ? options[customerId][questionLanguage].male.text || ""
                    : options.default[questionLanguage].male.text || "";
            const traitText = (question.trait as LanguageVariation)[questionLanguage] || "";

            return {
                question: questionText,
                trait: traitText,
            };
        });

        return {
            questions: questionsData,
            isRtl: language === "he",
        };
    }

    async getSimulations(): Promise<BuilderSimulationQuestionsResponse> {
        const simulationsRes = await this.db
            .select({
                id: simulations.id,
                data: simulations.data,
            })
            .from(simulations);

        const questionsForSimulations: BuilderSimulation[] = await Promise.all(
            simulationsRes.map(async (simulation) => {
                const questionsRes = await this.db
                    .select({
                        id: questions.id,
                        options: questions.options,
                    })
                    .from(questions)
                    .where(inArray(questions.id, simulation.data.questionIds));

                return {
                    id: simulation.id,
                    story: simulation.data.story,
                    questions: questionsRes.map((question) => ({
                        en: question.options.options.default.en.male.text,
                        he: question.options.options.default.he.male.text,
                    })),
                };
            })
        );
        return { simulations: questionsForSimulations };
    }
}
