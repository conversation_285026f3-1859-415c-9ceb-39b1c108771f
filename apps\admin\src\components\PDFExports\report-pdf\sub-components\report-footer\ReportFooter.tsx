import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";
import { ReportFooterPage } from "./ReportFooterPage";
import { IverseLogo } from "../icons/IverseLogo";

interface ReportFooterProps {
    candidateName: string;
    page: number;
    isRtl: boolean;
    totalPages: number;
}

export const ReportFooter: FC<ReportFooterProps> = ({ totalPages, isRtl, candidateName, page }) => {
    return (
        <View style={styles.reportFooterContainer}>
            <View></View>
            <View style={[styles.flewDirectionRow, styles.alignItemsCenter, styles.flexGap4, styles.reportFooterPagingContainer]}>
                {Array.from({ length: totalPages }, (_, i) => (
                    <ReportFooterPage key={i} page={i + 1} currentPage={page} />
                ))}
            </View>
            <View style={[styles.flewDirectionRow, styles.displayFlex, styles.flexGap2, styles.alignItemsCenter]}>
                <IverseLogo />
            </View>
        </View>
    );
};
