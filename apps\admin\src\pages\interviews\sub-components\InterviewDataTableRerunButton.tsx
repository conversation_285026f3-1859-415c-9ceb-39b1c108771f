import { InterviewStatus } from "@eva/shared-common";
import { FC, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useRerunEvaluationMutation } from "../../../app-state/apis";
import { PrimaryButton } from "@eva/shared-ui";

interface InterviewDataTableRerunButtonProps {
    interviewId: string;
    evaluationStatus: InterviewStatus;
}

export const InterviewDataTableRerunButton: FC<InterviewDataTableRerunButtonProps> = ({
    interviewId,
    evaluationStatus,
}) => {
    const [rerunEvaluation, { isLoading }] = useRerunEvaluationMutation();
    const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
    const isDisabled =
        isLoading ||
        isWaitingForResponse ||
        evaluationStatus === "interviewing" ||
        evaluationStatus === "running" ||
        evaluationStatus === "pending" ||
        evaluationStatus === "transferring" ||
        evaluationStatus === "invited";

    const handleRerunClick = async () => {
        try {
            setIsWaitingForResponse(true);
            const res = await rerunEvaluation(interviewId);
            res.error ? toast.error("Failed to start evaluation process") : toast.success("Evaluation process started");
            setIsWaitingForResponse(false);
        } catch (error) {
            toast.error("Failed to start evaluation process");
            setIsWaitingForResponse(false);
        }
    };

    useEffect(() => {
        if (evaluationStatus === "completed" || evaluationStatus === "failed") {
            setIsWaitingForResponse(false);
        }
    }, [evaluationStatus]);
    return <PrimaryButton disabled={isDisabled} content="Rerun" onClick={handleRerunClick} isLoading={isLoading} />;
};
