export interface AnalyzerReportData {
    report_versions: ReportVersion[];
    report_edit_session?: ReportVersion;
}

export interface ReportVersion {
    pdf_url?: string;
    approved?: boolean;
    approved_by?: string;
    approved_date?: string;
    reviewed_by?: string;
    review_date?: string;
    traits: Trait[];
    families: Family[];
    interview_score: number;
    summary: string;
}

export interface Trait {
    score: number;
    judgeScore?: number;
    summary: string;
    video_link: string;
    code: string;
    grading_map: Record<string, number> | null;
}

export interface Family {
    score: number;
    summary: string;
    code: string;
}
