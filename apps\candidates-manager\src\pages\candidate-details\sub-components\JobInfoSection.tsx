import { FC } from "react";
import { Stack } from "@mui/material";
import { Work, CalendarMonth } from "@mui/icons-material";
import { H4, formatDate } from "@eva/shared-ui";
import { MetadataItem } from "./MetadataItem";

interface JobInfoSectionProps {
    jobTitle: string;
    createdAt: string;
}

export const JobInfoSection: FC<JobInfoSectionProps> = ({
    jobTitle,
    createdAt,
}) => {
    return (
        <Stack spacing={2}>
            <H4>Job Information</H4>
            <MetadataItem
                icon={<Work fontSize="small" />}
                label="Applied for"
                value={jobTitle}
            />
            <MetadataItem
                icon={<CalendarMonth fontSize="small" />}
                label="Created"
                value={formatDate(createdAt)}
            />
        </Stack>
    );
}; 