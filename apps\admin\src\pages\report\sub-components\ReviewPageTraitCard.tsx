import React from "react";
import { Box, Card, CardContent, Rating, styled } from "@mui/material";
import { Trait, LanguageVariation } from "@eva/shared-common";
import { H5, LightPaper } from "@eva/shared-ui";
import { SignedUrlVideoPlayer } from "../../../components/SignedUrlVideoPlayer";
import { TraitCardHeader } from "./TraitCardHeader";


const StyledCardContent = styled(CardContent)({
    padding: "8px 12px",
    "&:last-child": {
        paddingBottom: "8px",
    },
});

interface ReviewPageTraitCardProps {
    trait: Trait;
    traitName: LanguageVariation;
    currentLanguage: keyof LanguageVariation;
    isEditMode: boolean;
    handleTraitScoreChange: (traitCode: string, newScore: number) => void;
}

export const ReviewPageTraitCard: React.FC<ReviewPageTraitCardProps> = ({
    trait,
    traitName,
    currentLanguage,
    isEditMode,
    handleTraitScoreChange,
}) => {
    const diff =
        trait.judgeScore !== undefined && trait.judgeScore !== null ? Math.abs(trait.judgeScore - trait.score) : null;

    return (
        <Box maxWidth="300px" minWidth="300px">
            <Card>
                <LightPaper>
                    <StyledCardContent>
                        <TraitCardHeader
                            traitName={traitName}
                            currentLanguage={currentLanguage}
                            judgeScore={trait.judgeScore}
                            diff={diff}
                        />
                    </StyledCardContent>
                    {trait.video_link && (
                        <StyledCardContent>
                            <SignedUrlVideoPlayer url={trait.video_link} height="150px" />
                        </StyledCardContent>
                    )}
                    <StyledCardContent>
                        <Rating
                            onChange={(event, newValue) => {
                                if (newValue !== null) {
                                    handleTraitScoreChange(trait.code, newValue);
                                }
                            }}
                            value={trait.score}
                            readOnly={!isEditMode}
                            size="large"
                        />
                    </StyledCardContent>
                    <StyledCardContent>
                        <H5>Score: {trait.score}</H5>
                    </StyledCardContent>
                </LightPaper>
            </Card>
        </Box>
    );
};
