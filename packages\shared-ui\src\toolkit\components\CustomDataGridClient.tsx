import { Stack, styled } from "@mui/material";
import {
    DataGrid,
    GridColDef,
    GridRowSelectionModel,
    GridSortModel,
    GridValidRowModel
} from "@mui/x-data-grid";
import { FC, useLayoutEffect, useState } from "react";
import { useAppTheme } from "../theme/AppThemeProvider";

const StyledCDataGrid = styled(DataGrid)<{ isRtl: boolean }>(({ isRtl }) => ({
    ".MuiTablePagination-actions": {
        button: {
            transform: isRtl ? "rotate(180deg)" : "rotate(0deg)",
        },
    },
}));

interface CustomDataGridClientProps {
    rows: GridValidRowModel[];
    columns: GridColDef[];
    pageSizeOptions?: number[];
    initialPage?: number;
    initialPageSize?: number;
    defaultSortField?: string;
    tableName: string;
    loading?: boolean;
    height?: string;
    enableSelection?: boolean;
    handleSelect?: (selectedIds: GridRowSelectionModel) => void;
    selectionModel?: GridRowSelectionModel;
}

export const CustomDataGridClient: FC<CustomDataGridClientProps> = ({
    rows,
    columns,
    pageSizeOptions = [10, 20, 50],
    initialPage = 0,
    initialPageSize = 20,
    defaultSortField,
    loading = false,
    height = '75dvh',
    enableSelection = false,
    handleSelect,
    selectionModel,
}) => {
    const { currentDirection } = useAppTheme();
    const [renderTrigger, setRenderTrigger] = useState(false);

    useLayoutEffect(() => {
        const pageNumberElement = document.querySelector(".MuiTablePagination-selectLabel");
        const ofElement = document.querySelector(".MuiTablePagination-displayedRows");
        if (ofElement && ofElement.textContent && pageNumberElement && pageNumberElement.textContent) {
            if (currentDirection === "rtl") ofElement.innerHTML = ofElement.textContent.replace("of", "מתוך");
            if (currentDirection === "ltr") ofElement.innerHTML = ofElement.textContent.replace("מתוך", "of");
            if (currentDirection === "rtl")
                pageNumberElement.innerHTML = pageNumberElement.textContent.replace("Rows per page:", "שורות בעמוד:");
            if (currentDirection === "ltr")
                pageNumberElement.innerHTML = pageNumberElement.textContent.replace("שורות בעמוד:", "Rows per page:");
        }
    }, [currentDirection, renderTrigger]);

    const handleStateChange = () => {
        setRenderTrigger((prev) => !prev);
    };  

    const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
        if (handleSelect) {
            handleSelect(newSelectionModel);
        }
    };

    return (
        <Stack height={height}>
            <StyledCDataGrid
                isRtl={currentDirection === "rtl"}
                onStateChange={handleStateChange}
                loading={loading}
                initialState={{
                    pagination: {
                        paginationModel: {
                            page: initialPage,
                            pageSize: initialPageSize,
                        },
                    },
                    sorting: defaultSortField ? {
                        sortModel: [
                            {
                                field: defaultSortField,
                                sort: "desc",
                            },
                        ],
                    } : undefined
                }}
                rows={rows}
                columns={columns}
                pageSizeOptions={pageSizeOptions}
                disableRowSelectionOnClick={true}
                checkboxSelection={enableSelection}
                onRowSelectionModelChange={handleSelectionChange}
                rowSelectionModel={selectionModel}
            />
        </Stack>
    );
}; 