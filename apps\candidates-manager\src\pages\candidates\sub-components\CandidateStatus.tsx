import { InterviewStatus } from "@eva/shared-common";
import { CircularProgress } from "@mui/material";
import { FC } from "react";
import { useGetCandidateStatusQuery } from "../../../app-state/apis/candidatesManagerApi";

const startedStatuses: InterviewStatus[] = ["pending", "interviewing", "transferring", "running", "failed"];

const completedStatuses: InterviewStatus[] = ["completed"];

interface CandidateStatusProps {
    candidateId: string;
}

export const CandidateStatus: FC<CandidateStatusProps> = ({ candidateId }) => {
    const { data, isLoading, error } = useGetCandidateStatusQuery(candidateId);

    if (isLoading) {
        return <CircularProgress size={20} />;
    }

    if (error) {
        return <span>Error</span>;
    }

    if (!data) {
        return <span>N/A</span>;
    }

    if (startedStatuses.includes(data.status as InterviewStatus)) {
        return <span>Started</span>;
    }

    if (completedStatuses.includes(data.status as InterviewStatus)) {
        return <span>Completed</span>;
    }

    if (data.status === "invited") {
        return <span>Invited</span>;
    }

    return <span>N/A</span>;
};
