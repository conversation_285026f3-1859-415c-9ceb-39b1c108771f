import { Button } from "@mui/material";
import React, { useState } from "react";
import { PrimaryButtonProps } from "./PrimaryButton";
import styled from "@emotion/styled";

const SecondaryButtonContainer = styled(Button)({
    width: "fit-content",
});

export interface SecondaryButtonProps extends PrimaryButtonProps {}

export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
    variant = "contained",
    content,
    onClick,
    disabled,
    timeDisabled,
    "data-qa": dataQa,
    endIcon,
}) => {
    const [isTimeDisabled, setIsTimeDisabled] = useState(timeDisabled ? true : false);

    if (timeDisabled) {
        setTimeout(() => {
            setIsTimeDisabled(false);
        }, timeDisabled);
    }

    const isDisabled = disabled || isTimeDisabled;

    return (
        <SecondaryButtonContainer data-qa={dataQa} color='secondary' variant={variant} onClick={onClick} disabled={isDisabled} endIcon={endIcon}>
            {content}
        </SecondaryButtonContainer>
    );
};
