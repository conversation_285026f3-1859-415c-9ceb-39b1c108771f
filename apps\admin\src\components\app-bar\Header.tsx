import { Stack } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import Container from "@mui/material/Container";
import Toolbar from "@mui/material/Toolbar";
import { LanguageSelector, useAppText } from "@eva/shared-ui";
import { AppLogo } from "./sub-components/AppLogo";
import { NavMenu } from "./sub-components/NavMenu";
import { UserMenu } from "./sub-components/UserMenu";

const Header = () => {
    const { getContentForDisplay } = useAppText();
    const pages = [
        {
            name: getContentForDisplay("appBar.navBar.customers"),
            path: "/customers",
        },
        { name: getContentForDisplay("appBar.navBar.jobs"), path: "/jobs" },
        {
            name: getContentForDisplay("appBar.navBar.interviews"),
            path: "/interviews",
        },
        {
            name: getContentForDisplay("appBar.navBar.videos"),
            path: "/videos",
        },
        {
            name: getContentForDisplay("appBar.navBar.prompts"),
            path: "/prompts",
        },
    ];
    return (
        <AppBar position="static" color="inherit">
            <Container maxWidth="xl">
                <Toolbar disableGutters>
                    <AppLogo />
                    <NavMenu pages={pages} />
                    <Stack direction="row" gap={12} alignItems="center">
                        <LanguageSelector />
                        <UserMenu />
                    </Stack>
                </Toolbar>
            </Container>
        </AppBar>
    );
};

export default Header;
