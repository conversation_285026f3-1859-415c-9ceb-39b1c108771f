import { ReportVersion } from "@eva/shared-common";

export const deepEqual = (a: any, b: any): boolean => {
    if (a === b) return true;

    if (typeof a !== "object" || typeof b !== "object" || a === null || b === null) {
        return false;
    }

    if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false;
        return a.every((item, index) => deepEqual(item, b[index]));
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;
        if (!deepEqual(a[key], b[key])) return false;
    }

    return true;
};

export const compareReports = (
    originalReport: ReportVersion,
    savedEditSession: ReportVersion | undefined,
    editReportState: ReportVersion
): boolean => {
    if (savedEditSession) {
        return !deepEqual(savedEditSession, editReportState);
    } else {
        return !deepEqual(originalReport, editReportState);
    }
};
