{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "deploy": {"dependsOn": ["build", "test", "lint"], "cache": false}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "install": {"cache": false}}}