import { TextField } from "@mui/material";
import { useField } from "formik";
import { FC } from "react";
import { Body1 } from "../theme/Typography";

interface EditableFormikTextFieldProps {
    content: string;
    fieldName: string;
    isEditing: boolean;
}

export const EditableFormikTextField: FC<EditableFormikTextFieldProps> = ({ content, fieldName, isEditing }) => {
    const [field, , helpers] = useField(fieldName);

    const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        helpers.setValue(event.target.value);
    };

    const renderContent = () => {
        if (content.includes("\n")) {
            return content.split("\n").map((line, index) => <Body1 key={index}>{line}</Body1>);
        } else {
            return <Body1>{content}</Body1>;
        }
    };

    return isEditing ? <TextField multiline fullWidth value={field.value} onChange={handleFieldChange} /> : renderContent();
};
