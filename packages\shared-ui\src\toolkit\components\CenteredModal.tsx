import { Dialog, Paper, Stack } from "@mui/material";
import React, { MouseEvent } from "react";
import { AbsolutePositionWrapper } from "./wrappers/AbsolutePositionWrapper";
import { CloseModalButton } from "./CloseModalButton";

interface CenteredModalProps {
    isOpen: boolean;
    onClose: () => void;
    children: React.ReactNode;
    width?: number | string;
    height?: number | string;
}

export const CenteredModal: React.FC<CenteredModalProps> = (props: CenteredModalProps) => {
    const { width = 400, height = 400, isOpen, onClose, children } = props;

    const handleCloseButtonClick = (event: MouseEvent) => {
        event.stopPropagation(); 
        onClose();
    };

    return (
        <Dialog maxWidth='lg' open={isOpen} onClose={onClose} >
            <Paper>
                <Stack width={width} height={height} padding={10} gap={12} overflow='auto'>
                    {children}
                    <AbsolutePositionWrapper right='-20px' top='20px' zIndex={100}>
                        <CloseModalButton onClick={handleCloseButtonClick} />
                    </AbsolutePositionWrapper>
                </Stack>
            </Paper>
        </Dialog>
    );
};
