import React from 'react';
import { CenteredModal } from '@eva/shared-ui';
import { CreateJobForm } from './CreateJobForm'; 

interface AddJobModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export const AddJobModal: React.FC<AddJobModalProps> = ({ 
    isOpen, 
    onClose, 
}) => {
    const handleSuccess = () => {
        onClose();
    };

    return (
        <CenteredModal
            isOpen={isOpen}
            onClose={onClose}
            width={600} 
            height="auto"
        >
            <CreateJobForm 
                onSuccess={handleSuccess}
                onCancel={onClose}
            />
        </CenteredModal>
    );
}; 