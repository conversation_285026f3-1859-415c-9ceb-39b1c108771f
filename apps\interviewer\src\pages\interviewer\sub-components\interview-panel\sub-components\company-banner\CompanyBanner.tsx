import { Stack } from "@mui/material";
import { useMediaQueryContext, AbsolutePositionWrapper, H5, H6 } from "@eva/shared-ui";
import { useInterviewer } from "../../../../InterviewerProvider";
import { CompanyLogo } from "./sub-components/CompanyLogo";

export const CompanyBanner = () => {
    const { logoLink, data } = useInterviewer();
    const { isMobile } = useMediaQueryContext();

    return (
        <>
            <AbsolutePositionWrapper top="40px" left={isMobile ? "50%" : "60px"} zIndex={200}>
                <Stack
                    gap={8}
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    width={isMobile ? "90dvw" : "auto"}
                >
                    {logoLink && <CompanyLogo companyLogoAddress={logoLink} />}
                    {isMobile && (
                        <Stack gap={2} alignItems="end">
                            {data && (
                                <>
                                    <H5>{data.customerName}</H5>
                                    <H6>{data.jobTitle}</H6>
                                </>
                            )}
                        </Stack>
                    )}
                </Stack>
            </AbsolutePositionWrapper>
            {!isMobile && (
                <AbsolutePositionWrapper fromEdge top="25px" right={isMobile ? "50%" : "20px"} zIndex={200}>
                    <Stack gap={2} direction="row">
                        {data && (
                            <>
                                <H5>
                                    {data.customerName} - {data.jobTitle}
                                </H5>
                            </>
                        )}
                    </Stack>
                </AbsolutePositionWrapper>
            )}
        </>
    );
};
