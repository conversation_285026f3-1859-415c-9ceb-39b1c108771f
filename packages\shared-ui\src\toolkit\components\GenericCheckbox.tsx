import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";

interface GenericCheckboxProps {
    label: string;
    checked: boolean;
    onChange: () => void;
}

export const GenericCheckbox: React.FC<GenericCheckboxProps> = ({ label, checked, onChange }) => {
    return <FormControlLabel control={<Checkbox size='large' checked={checked} onChange={onChange} />} label={label} />;
};
