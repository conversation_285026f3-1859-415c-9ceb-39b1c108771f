/* eslint-disable */
// WARNING: DO NOT EDIT. This file is automatically generated by AWS Amplify. It will be overwritten.

const awsmobile = {
    "aws_project_region": "us-east-1",
    "aws_cloud_logic_custom": [
        {
            "name": "IverseAdminExpressApi",
            "endpoint": "https://dvr2i04ycc.execute-api.us-east-1.amazonaws.com/staging",
            "region": "us-east-1"
        }
    ],
    "aws_cognito_identity_pool_id": "us-east-1:b3b21898-65b1-4fde-abd3-024b2906fe2c",
    "aws_cognito_region": "us-east-1",
    "aws_user_pools_id": "us-east-1_szReER0h5",
    "aws_user_pools_web_client_id": "4a3fomj3u7f6vp6riaqavonkg3",
    "oauth": {},
    "aws_cognito_username_attributes": [],
    "aws_cognito_social_providers": [],
    "aws_cognito_signup_attributes": [
        "EMAIL"
    ],
    "aws_cognito_mfa_configuration": "OFF",
    "aws_cognito_mfa_types": [
        "SMS"
    ],
    "aws_cognito_password_protection_settings": {
        "passwordPolicyMinLength": 8,
        "passwordPolicyCharacters": []
    },
    "aws_cognito_verification_mechanisms": [
        "EMAIL"
    ],
    "aws_user_files_s3_bucket": "iverse-admin-uploadse62dc-staging",
    "aws_user_files_s3_bucket_region": "us-east-1"
};


export default awsmobile;
