import { Select<PERSON>hangeEvent, Stack } from "@mui/material";
import { useField, useFormikContext } from "formik";
import { FC, useEffect } from "react";
import { HexColorPicker } from "react-colorful";
import { toast } from "react-toastify";
import { useGetAllAccountsQuery } from "../../../app-state/apis";
import { AbsolutePositionWrapper, Body2, FileInput, H3, PrimaryButton, CustomSelect, CustomTextField } from "@eva/shared-ui";
import { CustomerLogo } from "../../../components/CustomerLogo";

interface CustomerCreateFormProps {
    formType: "create" | "edit";
}

export const CustomerCreateForm: FC<CustomerCreateFormProps> = ({ formType }) => {
    const { data: accountsData, isLoading } = useGetAllAccountsQuery();
    const [accountIdField, , { setValue: setAccountIdValue }] = useField("accountId");
    const [logoFileField, , { setValue: setFile }] = useField("logoFile");
    const [, , { setValue: setLogoFilePathValue }] = useField("logoFilePath");
    const [integrationField, , { setValue: setIntegration }] = useField("integration");
    const [colorField, , { setValue: setColor }] = useField("themeColor");
    const [customerNameField, , { setValue: setCustomerNameValue }] = useField("customerName");
    const [customerIdField] = useField("customerId");
    const { submitForm, errors, setErrors } = useFormikContext();
    const accounts = accountsData
        ? accountsData.accounts.map((account) => ({
              label: account.name,
              value: account.id,
          }))
        : [];

    const handleAccountChange = (event: SelectChangeEvent) => {
        setAccountIdValue(event.target.value);
    };

    const handleCustomerNameChange = (event: React.ChangeEvent<{ value: string }>) => {
        setCustomerNameValue(event.target.value);
    };

    const handleFileChange = (file: File | null) => {
        setFile(file);
        setLogoFilePathValue(file ? `public/uploads/logos/${customerNameField.value}-${file.name}` : null);
    };

    const handleIntegrationChange = (event: React.ChangeEvent<{ value: string }>) => {
        setIntegration(event.target.value);
    };

    const handleColorChange = (color: string) => {
        setColor(color);
    };

    useEffect(() => {
        if (errors) {
            for (const error in errors) {
                toast.error((errors as Record<string, string>)[error]);
            }
        }
        setErrors({});
    }, [errors]);

    const formTypeText = formType === "create" ? "Create" : "Update";
    const isAccountFieldDisabled = formType === "edit";

    return (
        <>
            <H3>{formTypeText} Customer</H3>
            <Stack direction='row' gap={2} position='relative'>
                <CustomSelect
                    disabled={isAccountFieldDisabled}
                    options={accounts}
                    onChange={handleAccountChange}
                    loading={isLoading}
                    label='Accounts'
                    value={accountIdField.value}
                />
                {customerIdField.value && (
                    <AbsolutePositionWrapper right='-60px' top='30px'>
                        <CustomerLogo size={150} customerId={customerIdField.value} />
                    </AbsolutePositionWrapper>
                )}
            </Stack>
            <CustomTextField value={integrationField.value} width={400} label='Integration' onChange={handleIntegrationChange} />
            <CustomTextField value={customerNameField.value} width={400} label='Company Name' onChange={handleCustomerNameChange} />
            <Stack height={200} width='100%' direction='row' gap={4}>
                <HexColorPicker color={colorField.value} onChange={handleColorChange} />
                <Stack width='50%' height={200} style={{ background: colorField.value }} borderRadius={2} />
            </Stack>
            <Stack direction='row' gap={3}>
                <FileInput onFileSelect={handleFileChange} buttonLabel='Choose logo' accept='.svg,.png' />
                {logoFileField.value && (
                    <Stack justifyContent='center' padding={2}>
                        <Body2>Selected file: {(logoFileField.value as File).name}</Body2>
                    </Stack>
                )}
            </Stack>
            <Stack alignItems='end' height='100%' justifyContent='end'>
                <PrimaryButton content={formTypeText} onClick={submitForm} />
            </Stack>
        </>
    );
};
