import { pdf } from "@react-pdf/renderer";
import React, { Suspense, useState } from "react";
import { toast } from "react-toastify";
import { useLazyGetSignedUrlQuery, useApproveReportMutation } from "../app-state/apis";
import type {
    AdditionalQuestionsResponse,
    FamiliesWithTraitsForDisplayMap,
    InterviewResponse,
    ReportVersion,
    Trait,
    WarmupQuestionsAnswersResponse,
} from "@eva/shared-common";
import { PrimaryButton, useAppText, ConfirmationDialog, blobToBase64, formatDate } from "@eva/shared-ui";
import { ReportPDF } from "./PDFExports/report-pdf/ReportPDF";
import SendIcon from "@mui/icons-material/Send";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import VerifiedIcon from "@mui/icons-material/Verified";
import { useAuthenticator } from "@aws-amplify/ui-react";

const getCandidateId = (interviewData: InterviewResponse): string => {
    const companyName = interviewData.interview.companyName.toLowerCase();
    if (companyName === "pilat" || companyName === "idf") {
        return interviewData.interview.socialId ? interviewData.interview.socialId : "*";
    }
    return interviewData.interview.email ? interviewData.interview.email : "*";
};

interface PDFActionButtonProps {
    interviewData: InterviewResponse;
    additionalQuestionsResponse?: AdditionalQuestionsResponse;
    warmupQuestionsResponse?: WarmupQuestionsAnswersResponse;
    familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap;
    actionType: "export" | "sendReport" | "approveReport";
    disabled?: boolean;
    validateSubmit?: boolean;
}

export const PDFActionButton: React.FC<PDFActionButtonProps> = ({
    interviewData,
    additionalQuestionsResponse,
    warmupQuestionsResponse,
    familiesWithTraitsMap,
    actionType,
}) => {
    const { getContentForDisplay } = useAppText();
    const { user } = useAuthenticator();
    const [loading, setLoading] = useState<boolean>(false);
    const [getSignedUrl] = useLazyGetSignedUrlQuery();
    const [approveReport] = useApproveReportMutation();
    const [isValidationModalOpen, setIsValidationModalOpen] = useState<boolean>(false);
    const exportText = getContentForDisplay("reviewPage.export");
    const sendReportText = getContentForDisplay("reviewPage.sendReport");
    const approveReportText = "Approve Report";

    const getButtonCaption = () => {
        switch (actionType) {
            case "export":
                return exportText;
            case "sendReport":
                return sendReportText;
            case "approveReport":
                return approveReportText;
            default:
                return exportText;
        }
    };

    const getButtonIcon = () => {
        switch (actionType) {
            case "export":
                return <FileDownloadIcon />;
            case "sendReport":
                return <SendIcon />;
            case "approveReport":
                return <VerifiedIcon />;
            default:
                return <FileDownloadIcon />;
        }
    };

    const getValidationMessage = () => {
        switch (actionType) {
            case "sendReport":
                return "Are you sure you want to send the report?";
            case "approveReport":
                return "Are you sure you want to approve this report? This will mark it as the official approved report for this candidate and job.";
            default:
                return "";
        }
    };

    const generateSignedUrl = async (url: string) => {
        const { url: signedUrl } = await getSignedUrl({ url, expiration: 10080 }).unwrap();
        return signedUrl;
    };

    const generateSignedUrlsForTraits = async (traits: Trait[]) => {
        return await Promise.all(
            traits.map(async (trait) => {
                return {
                    ...trait,
                    video_link: trait.video_link ? await generateSignedUrl(trait.video_link) : "",
                };
            })
        );
    };

    const handleActionClick = async () => {
        if (actionType === "sendReport" || actionType === "approveReport") {
            setIsValidationModalOpen(true);
        } else {
            await handleGetPDF();
        }
    };

    const handleGetPDF = async () => {
        setIsValidationModalOpen(false);
        setLoading(true);

        try {
            const traitsWithSignedUrls: Trait[] = await generateSignedUrlsForTraits(interviewData.report.traits);

            const reportWithSignedUrls: ReportVersion = {
                ...interviewData.report,
                traits: traitsWithSignedUrls,
            };

            const candidateId: string = getCandidateId(interviewData);

            const doc = (
                <ReportPDF
                    email={interviewData.interview.email}
                    language={interviewData.interview.language}
                    candidateId={candidateId}
                    candidateImage={interviewData.interview.candidateImage}
                    interviewDate={formatDate(interviewData.interview.interviewDate, false)}
                    customerName={interviewData.interview.companyName}
                    reportData={reportWithSignedUrls}
                    familiesWithTraitsMap={familiesWithTraitsMap}
                    additionalQuestions={additionalQuestionsResponse?.additionalQuestionsAnswers || []}
                    warmupQuestions={warmupQuestionsResponse?.warmupQuestionsAnswers || []}
                    candidateName={interviewData.interview.candidateName}
                    jobTitle={interviewData.interview.jobTitle}
                />
            );

            const asPdf = pdf();
            asPdf.updateContainer(doc);

            const blob = await asPdf.toBlob();

            if (actionType === "export") {
                const pdfUrl = URL.createObjectURL(blob);
                const link = document.createElement("a");
                link.href = pdfUrl;
                link.download = `report-${interviewData.interview.candidateName}.pdf`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                const base64PDF = await blobToBase64(blob);

                if (actionType === "approveReport") {
                    await approveReport({
                        interviewId: interviewData.interview.interviewId,
                        body: {
                            approvedBy: user?.username || "Unknown",
                            base64PDF: base64PDF,
                        },
                    }).unwrap();

                    toast.success("Report approved and sent successfully");
                } else {
                    // await sendPFDReport({
                    //     interviewId: interviewData.interview.interviewId,
                    //     base64PDF: base64PDF,
                    //     globalScore: interviewData.report.interview_score,
                    // }).unwrap();

                    toast.success("Report sent successfully");
                }
            }
        } catch (e) {
            toast.error(actionType === "approveReport" ? "Error approving report" : "Error sending report");
            console.error(e);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <PrimaryButton
                isLoading={loading}
                onClick={handleActionClick}
                content={getButtonCaption()}
                endIcon={getButtonIcon()}
            />
            <ConfirmationDialog
                onSubmit={handleGetPDF}
                isOpen={isValidationModalOpen}
                message={getValidationMessage()}
                onClose={() => setIsValidationModalOpen(false)}
            />
        </>
    );
};
