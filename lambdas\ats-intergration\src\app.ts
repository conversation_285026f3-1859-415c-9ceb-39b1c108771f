import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { Logger } from '@eva/logger';
import { CustomerService } from './services/CustomerService';
import { DB, initDB } from '@eva/drizzle';
import { JobService } from './services/JobService';
import { setupLogging } from '@eva/logger';
import { CreateCustomerRequest, CreateJobRequest } from './types';

let db: DB | null = null;
let logger: Logger | null = null;
let customerService: CustomerService | null = null;
let jobService: JobService | null = null;

const initializeServices = async () => {
    if (!logger) {
        logger = setupLogging();
    }
    if (!db) {
        db = await initDB();
        logger.info('Database initialized');
    }
    if (!customerService) {
        customerService = new CustomerService(db, logger);
        logger.info('CustomerService initialized');
    }
    if (!jobService) {
        jobService = new JobService(db, logger);
        logger.info('JobService initialized');
    }
};

const handleCreateCustomer = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const accountId = event.pathParameters?.accountId;
    if (!accountId) {
        throw Object.assign(new Error('accountId is required'), { statusCode: 400 });
    }
    
    const customerId = await customerService!.createCustomer(
        JSON.parse(event.body!) as CreateCustomerRequest,
        accountId,
    );
    return {
        statusCode: 201,
        headers: { Location: `/customers/${customerId}` },
        body: JSON.stringify({ customerId }),
    };
};

const handleCreateJob = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const customerId = event.pathParameters?.customerId;
    if (!customerId) {
        throw Object.assign(new Error('customerId is required'), { statusCode: 400 });
    }
    const requestBody = JSON.parse(event.body!) as CreateJobRequest;
    const result = await jobService!.createJobForCustomer(customerId, requestBody);

    return {
        statusCode: 201,
        headers: { Location: `/jobs/${result.jobId}` },
        body: JSON.stringify(result),
    };
};

export const lambdaHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    await initializeServices();

    try {
        const method = event.httpMethod;
        const path = event.path;

        logger!.info(`Received ${method} request for path: ${path}`);

        if (method === 'POST' && path.match(/^\/accounts\/[^\/]+\/customers$/)) {
            return await handleCreateCustomer(event);
        } else if (method === 'POST' && path.match(/^\/customers\/[^\/]+\/jobs$/)) {
            return await handleCreateJob(event);
        }

        logger!.warn(`Route not found: ${method} ${path}`);
        throw Object.assign(new Error('Not Found'), { statusCode: 404 });
    } catch (err) {
        const error = err as Error & { statusCode?: number };
        logger!.error('Error processing request', {
            error: error.message,
            stack: error.stack,
            statusCode: error.statusCode,
        });

        return {
            statusCode: error.statusCode || 500,
            body: '',
        };
    }
};
