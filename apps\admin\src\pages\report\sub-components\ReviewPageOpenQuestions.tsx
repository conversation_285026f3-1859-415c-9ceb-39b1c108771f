import { Card } from "@aws-amplify/ui-react";
import { Body1, H3, LightPaper, useAppText } from "@eva/shared-ui";
import { Box, CardContent, CardMedia, Paper, Stack } from "@mui/material";
import { useReviewPageContext } from "../context/ReviewPageProvider";

export const ReviewPageOpenQuestions = () => {
    const { additionalQuestionsResponse } = useReviewPageContext();
    const { currentLanguage } = useAppText();
    const headerText = currentLanguage === "en" ? "Open Questions" : "שאלות פתוחות";
    return additionalQuestionsResponse && additionalQuestionsResponse.additionalQuestionsAnswers.length ? (
        <Stack padding={3}>
            <Paper>
                <Stack padding={3} gap={6}>
                    <H3 color="secondary">{headerText}</H3>
                    <Stack direction="row" gap={3}>
                        {additionalQuestionsResponse?.additionalQuestionsAnswers.map((item) => (
                            <Box maxWidth="300px" minWidth="300px">
                                <Card>
                                    <LightPaper>
                                        <CardMedia controls component="video" src={item.video_link} />
                                        <CardContent>
                                            <Body1>{item.question}</Body1>
                                        </CardContent>
                                    </LightPaper>
                                </Card>
                            </Box>
                        ))}
                    </Stack>
                </Stack>
            </Paper>
        </Stack>
    ) : (
        <></>
    );
};
