import { FC, useState } from "react";
import { SecondaryButton } from "@eva/shared-ui";
import { InterviewLogsModal } from "./InterviewLogsModal";

interface InterviewLogsButtonProps {
    runId: string;
    interviewDate: string;
    disabled: boolean;
}

export const InterviewLogsButton: FC<InterviewLogsButtonProps> = ({ interviewDate, runId, disabled }) => {
    const [isOpen, setIsOpen] = useState(false);

    const handleButtonClick = () => {
        setIsOpen((prev) => !prev);
    };
    return (
        <>
            <SecondaryButton
                disabled={disabled}
                onClick={handleButtonClick}
                content='Logs'
                data-qa={`logs-button-${runId}`}
            />
            {isOpen && (
                <InterviewLogsModal
                    isOpen={isOpen}
                    onClose={handleButtonClick}
                    runId={runId}
                    interviewDate={interviewDate}
                />
            )}
        </>
    );
};
