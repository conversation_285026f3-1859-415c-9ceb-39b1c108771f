import { InterviewsFilterParams } from "@eva/shared-common";
import { CustomSelect, CustomSelectOption, CustomTextField, DataTableFilterWrapper } from "@eva/shared-ui";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { IconButton, SelectChangeEvent, Skeleton } from "@mui/material";
import { debounce } from "lodash";
import { FC, useState } from "react";
import { useGetInterviewsPageFilterOptionsQuery } from "../../../app-state/apis";


const evaluationStatusOptions: CustomSelectOption[] = [
    {
        value: "",
        label: "All",
    },
    {
        value: "not_started",
        label: "Not Started",
    },
    {
        value: "not_available",
        label: "N/A",
    },
    {
        value: "in_progress",
        label: "In Progress",
    },
    {
        value: "complete",
        label: "Complete",
    },
];

interface InterviewsDataTableFilterProps {
    filerFields: InterviewsFilterParams;
    handleFilterChange: (filterFields: InterviewsFilterParams) => void;
    resultsCount: number;
}

export const InterviewsDataTableFilter: FC<InterviewsDataTableFilterProps> = ({
    filerFields,
    handleFilterChange,
    resultsCount,
}) => {
    const { data } = useGetInterviewsPageFilterOptionsQuery();
    const [searchText, setSearchText] = useState(filerFields.searchText || "");

    const debouncedHandleSearchChange = debounce((event: React.ChangeEvent<{ value: string }>) => {
        handleFilterChange({ ...filerFields, searchText: event.target.value });
    }, 1200);
    
    const handleSearchChange = (event: React.ChangeEvent<{ value: string }>) => {
        setSearchText(event.target.value);
        debouncedHandleSearchChange(event);
    };

    const handleJobTitleChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filerFields, jobTitle: event.target.value });
    };

    const handleCompanyChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filerFields, company: event.target.value });
    };

    const handleAccountChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filerFields, account: event.target.value });
    };

    const handleReviewStatusChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filerFields, reviewStatus: event.target.value });
    };

    const handleResetFilters = () => {
        handleFilterChange({
            searchText: "",
            jobTitle: "",
            company: "",
            account: "",
            reviewStatus: "",
        });
    };

    const jobTitleOptions: CustomSelectOption[] = data
        ? [
              {
                  value: "",
                  label: "All",
              },
              ...data.jobTitles.map((jobTitle) => ({
                  value: jobTitle,
                  label: jobTitle,
              })),
          ]
        : [];

    const companyOptions: CustomSelectOption[] = data
        ? [
              {
                  value: "",
                  label: "All",
              },
              ...data.companies.map((company) => ({
                  value: company,
                  label: company,
              })),
          ]
        : [];

    const accountOptions: CustomSelectOption[] = data
        ? [
              {
                  value: "",
                  label: "All",
              },
              ...data.accounts.map((account) => ({
                  value: account,
                  label: account,
              })),
          ]
        : [];

    return data ? (
        <DataTableFilterWrapper resultsCount={resultsCount}>
            <CustomTextField
                width={300}
                value={searchText}
                onChange={handleSearchChange}
                label="Search"
            />
            <CustomSelect
                value={filerFields.jobTitle || ""}
                label="Job Title"
                options={jobTitleOptions}
                onChange={handleJobTitleChange}
            />
            <CustomSelect
                value={filerFields.company || ""}
                label="Company"
                options={companyOptions}
                onChange={handleCompanyChange}
            />
            <CustomSelect
                value={filerFields.account || ""}
                label="Account"
                options={accountOptions}
                onChange={handleAccountChange}
            />
            <CustomSelect
                value={filerFields.reviewStatus || ""}
                label="Review Status"
                options={evaluationStatusOptions}
                onChange={handleReviewStatusChange}
            />
            <IconButton onClick={handleResetFilters}>
                <RestartAltIcon />
            </IconButton>
        </DataTableFilterWrapper>
    ) : (
        <Skeleton variant="rectangular" height={50} />
    );
};
