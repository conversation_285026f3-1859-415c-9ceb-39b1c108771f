import { CssBaseline } from "@mui/material";
import { Provider as StoreProvider } from "react-redux";
import { store } from "./store/store";
import { Amplify } from "aws-amplify";
import awsExports from "../aws-exports";
import { MediaQueryProvider, AppThemeProvider, TextProvider } from "@eva/shared-ui";
import { appLanguagesTextMap } from "./appText";

Amplify.configure(awsExports);

interface ShellProps {
    children: React.ReactNode;
}

export const Shell = ({ children }: ShellProps) => (
    <StoreProvider store={store}>
        <MediaQueryProvider>
            <AppThemeProvider>
                <TextProvider appLanguagesTextMap={appLanguagesTextMap}>
                    <CssBaseline />
                    {children}
                </TextProvider>
            </AppThemeProvider>
        </MediaQueryProvider>
    </StoreProvider>
);
