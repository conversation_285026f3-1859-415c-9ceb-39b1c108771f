import { C<PERSON>Job } from "@eva/shared-common";
import { GridColDef } from "@mui/x-data-grid";
import { FC } from "react";
import { Link } from "react-router-dom";
import { CustomDataGridClient } from "@eva/shared-ui";
import { JobStatusChip } from "../../../components/JobStatusChip";

const columns: GridColDef[] = [
    {
        field: "title",
        headerName: "Job Title",
        type: "string",
        width: 250,
        disableColumnMenu: true,
        renderCell: (params) => <Link to={`/jobs/${params.row.id}`}>{params.row.title}</Link>,
    },
    {
        field: "description",
        headerName: "Description",
        type: "string",
        disableColumnMenu: true,
        width: 350,
        renderCell: (params) => (
            <div style={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
                {params.row.description}
            </div>
        ),
    },
    {
        field: "status",
        headerName: "Status",
        type: "string",
        disableColumnMenu: true,
        width: 200,
        renderCell: (params) => <JobStatusChip status={params.row.status} />,
    },
    {
        field: "createdAt",
        headerName: "Created At",
        type: "string",
        disableColumnMenu: true,
        renderCell: (params) => <div>{new Date(params.row.createdAt).toLocaleDateString()}</div>,
    },
];

interface JobsDataTableProps {
    jobs: CMJob[];
}

export const JobsDataTable: FC<JobsDataTableProps> = ({ jobs }) => {
    return (
        <CustomDataGridClient
            tableName="jobs-table"
            defaultSortField="createdAt"
            rows={jobs}
            columns={columns}
            height="75dvh"
        />
    );
};
