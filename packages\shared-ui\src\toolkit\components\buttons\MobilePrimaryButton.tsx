import { Button, CircularProgress, styled } from "@mui/material";
import { useState } from "react";
import { PrimaryButtonProps } from "./PrimaryButton";

const MobilePrimaryButtonContainer = styled(Button)({
    width: "100%", // Full width for easy tapping
    padding: "12px 16px", // Increased padding for better touch interaction
    fontSize: "24px", // Larger text size for readability
    borderRadius: "8px", // Rounded corners for a modern look
    textTransform: "none", // Maintain text case as is
});

interface MobilePrimaryButtonProps extends PrimaryButtonProps {}

export const MobilePrimaryButton = ({
    variant = "contained",
    content,
    onClick,
    disabled,
    timeDisabled,
    "data-qa": dataQa,
    isLoading,
    endIcon,
}: MobilePrimaryButtonProps) => {
    const [isTimeDisabled, setIsTimeDisabled] = useState(timeDisabled ? true : false);

    if (timeDisabled) {
        setTimeout(() => {
            setIsTimeDisabled(false);
        }, timeDisabled);
    }

    const isDisabled = disabled || isTimeDisabled;

    return (
        <MobilePrimaryButtonContainer
            data-qa={dataQa}
            color='primary'
            variant={variant}
            onClick={onClick}
            disabled={isDisabled}
            endIcon={endIcon}
        >
            {isLoading ? <CircularProgress color='inherit' size='24px' /> : content}
        </MobilePrimaryButtonContainer>
    );
};
