import React from "react";
import { Box, Stack, Tooltip, styled, Theme } from "@mui/material";
import { LanguageVariation } from "@eva/shared-common";
import { H5 } from "@eva/shared-ui";


const TraitCardHeaderContainer = styled(Stack)(({ theme }) => ({
    alignItems: "center", 
}));

const getDiffColorStyle = (diff: number) => (theme: Theme): string => {
    if (diff <= 1) {
        return theme.palette.success.main;
    }
    if (diff <= 2) {
        return theme.palette.warning.main;
    }
    return theme.palette.error.main;
};

const DiffIndicator = styled(Box)<{ diff: number }>(({ theme, diff }) => ({
    width: 12,
    height: 12,
    borderRadius: '50%',
    backgroundColor: getDiffColorStyle(diff)(theme),
}));

interface TraitCardHeaderProps {
    traitName: LanguageVariation;
    currentLanguage: keyof LanguageVariation;
    judgeScore?: number | null;
    diff?: number | null;
}

export const TraitCardHeader: React.FC<TraitCardHeaderProps> = ({ 
    traitName, 
    currentLanguage, 
    judgeScore, 
    diff 
}) => {
    return (
        <TraitCardHeaderContainer direction="row" spacing={1} alignItems="center">
            <H5 color='textPrimary'>{traitName[currentLanguage]}</H5>
            {diff !== null && diff !== undefined && judgeScore !== undefined && judgeScore !== null && (
                <Tooltip title={`Judge Score: ${judgeScore}`}>
                    <DiffIndicator diff={diff} />
                </Tooltip>
            )}
        </TraitCardHeaderContainer>
    );
}; 