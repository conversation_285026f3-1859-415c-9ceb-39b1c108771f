import { NextFunction, Request, Response, Router } from "express";
import { InterviewerJobData, InterviewProgressStatus } from "@eva/shared-common";
import { getInterviewService, getLogger } from "../config/initialize";

const router = Router();
const logger = getLogger();

router.get(
    "/setup/:jobId/:pilatInterviewId",
    async (req: Request, res: Response<{ interviewId: string }>, next: NextFunction) => {
        try {
            const { jobId, pilatInterviewId } = req.params;
            const interviewService = getInterviewService();
            const jobInterviewData = await interviewService.setupPilatInterview(jobId, pilatInterviewId);
            logger.info(jobInterviewData);
            res.json(jobInterviewData);
        } catch (error) {
            logger.error("Error setting up interview for pilat");
            next(error);
        }
    }
);

router.get("/setup/:interviewId", async (req: Request, res: Response<InterviewerJobData>, next: NextFunction) => {
    try {
        const { interviewId } = req.params;
        const interviewService = getInterviewService();
        const jobInterviewData = await interviewService.setupInterview(interviewId);
        logger.info(jobInterviewData);
        res.json(jobInterviewData);
    } catch (error) {
        logger.error("Error setting up interview");
        next(error);
    }
});
router.get("/current-step/:interviewId", async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { interviewId } = req.params;

        if (!interviewId) {
            return res.status(400).json({
                message: "Interview ID is required",
            });
        }

        const interviewService = getInterviewService();
        const currentStep = await interviewService.getCurrentStep(interviewId);

        return res.json(currentStep);
    } catch (error) {
        next(error);
    }
});

router.post("/submit-answer", async (req: Request, res: Response, next: NextFunction) => {
    try {
        const interviewService = getInterviewService();
        await interviewService.submitAnswer(req.body);
        res.json({});
    } catch (error) {
        logger.error("Error submitting answer");
        next(error);
    }
});

router.post("/start-interview/:interviewId", async (req: Request, res: Response, next: NextFunction) => {
    try {
        const interviewService = getInterviewService();
        await interviewService.startInterview(req.params.interviewId);
        res.json({});
    } catch (error) {
        logger.error("Error starting interview");
        next(error);
    }
});

router.get(
    "/interview-progress-status/:interviewId",
    async (req: Request, res: Response<InterviewProgressStatus>, next: NextFunction) => {
        try {
            const interviewService = getInterviewService();
            const interviewProgressStatus = await interviewService.getInterviewProgressStatus(req.params.interviewId);
            res.json(interviewProgressStatus);
        } catch (error) {
            logger.error("Error getting interview progress status");
            next(error);
        }
    }
);

router.get("/terms-url", async (req: Request, res: Response<{ termsUrl: string }>, next: NextFunction) => {
    try {
        const interviewService = getInterviewService();
        const termsUrl = await interviewService.getTermsUrl();
        res.json({ termsUrl });
    } catch (error) {
        logger.error("Error getting terms url");
        next(error);
    }
});



export default router;
