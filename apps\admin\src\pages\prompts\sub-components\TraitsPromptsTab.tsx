import { useGetPromptsPageTraitsMetadataQuery } from "../../../app-state/apis";
import { LoadingScreen } from "@eva/shared-ui";
import { TraitPromptsCard } from "./TraitPromptsCard";

export const TraitsPromptsTab = () => {
    const { data, isLoading } = useGetPromptsPageTraitsMetadataQuery();

    return (
        <>
            {!isLoading && data ? (
                <>
                    {data.traits.map((trait) => (
                        <TraitPromptsCard key={trait.id} trait={trait} />
                    ))}
                </>
            ) : (
                <LoadingScreen />
            )}
        </>
    );
};
