import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Accordion, AccordionSummary, Stack } from "@mui/material";
import { useField } from "formik";
import { FC } from "react";
import { H2, H3, Language, useAppText, GenericCheckbox } from "@eva/shared-ui";
import { FamilyFieldQuestionRow } from "./FamilyFieldQuestionRow";
import { FamilyForBuilder, QuestionOptions } from "@eva/shared-common";

const questionTextForPreview = (customer: string, question: QuestionOptions, currentLanguage: Language) => {
    if (customer in question.options) return question.options[customer][currentLanguage].male.text;
    else return question.options.default[currentLanguage].male.text;
};
interface FamilyFieldProps {
    index: number;
    family: FamilyForBuilder;
    disabled: boolean;
}

export const FamilyField: FC<FamilyFieldProps> = ({ family, disabled }) => {
    const { currentLanguage, getContentForDisplay } = useAppText();
    const [familyField, , helpers] = useField(family.family_id);
    const [customerField] = useField("customerId");
    const [computedTraits, , calculatedTraitsHelpers] = useField("computedTraits");
    const values = familyField.value as string[];

    const questionsText = getContentForDisplay("jobBuilderForm.questions");
    const computedTraitsText = getContentForDisplay("jobBuilderForm.computedTraits");

    const handleSelect = (id: string) => {
        if (values.includes(id)) {
            helpers.setValue(values.filter((value) => value !== id));
        } else {
            helpers.setValue([...values, id]);
        }
    };

    const handleCalculatedTraitSelect = (id: string) => {
        if (computedTraits.value.includes(id)) {
            calculatedTraitsHelpers.setValue(computedTraits.value.filter((value: string) => value !== id));
        } else {
            calculatedTraitsHelpers.setValue([...computedTraits.value, id]);
        }
    };

    return (
        <Accordion defaultExpanded elevation={0}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Stack paddingLeft={3}>
                    <H2>{family.family[currentLanguage]}</H2>
                </Stack>
            </AccordionSummary>
            <Stack padding={5} gap={4}>
                <Stack paddingLeft={3}>
                    <H3>{questionsText}</H3>
                </Stack>
                <Stack paddingLeft={3} gap={4}>
                    {family.questions.map((question) => (
                        <FamilyFieldQuestionRow
                            disabled={disabled}
                            key={question.question_id}
                            questionId={question.question_id}
                            trait={question.trait[currentLanguage]}
                            checked={values.includes(question.question_id)}
                            handleSelect={handleSelect}
                            questionText={questionTextForPreview(
                                customerField.value,
                                question.question,
                                currentLanguage
                            )}
                        />
                    ))}
                </Stack>
                {family.computedTraits.length > 0 && (
                    <>
                        <Stack paddingLeft={3}>
                            <H3>{computedTraitsText}</H3>
                        </Stack>
                        <Stack paddingLeft={6} gap={4} direction="row">
                            {family.computedTraits.map((trait) => (
                                <GenericCheckbox
                                    key={trait.id}
                                    label={trait.trait[currentLanguage]}
                                    checked={computedTraits.value.includes(trait.id)}
                                    onChange={() => handleCalculatedTraitSelect(trait.id)}
                                />
                            ))}
                        </Stack>
                    </>
                )}
            </Stack>
        </Accordion>
    );
};
