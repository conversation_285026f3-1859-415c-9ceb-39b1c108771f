import { useAuthenticator } from "@aws-amplify/ui-react";
import { Skeleton } from "@mui/material";
import React, { Suspense, useEffect } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import { LoadingScreen, BackdropLoading, AuthenticatorPage } from "@eva/shared-ui";
import { PromptsPage } from "../pages/prompts/PromptsPage";

const VideosPage = React.lazy(() => import("../pages/video-uploader/VideosPage"));
const ReviewPage = React.lazy(() => import("../pages/report/ReviewPage"));
const InterviewsPage = React.lazy(() => import("../pages/interviews/InterviewsPage"));
const JobBuilderPage = React.lazy(() => import("../pages/job-builder/JobBuilderPage"));
const JobsPage = React.lazy(() => import("../pages/jobs/JobsPage"));
const Header = React.lazy(() => import("../components/app-bar/Header"));
const CustomersPage = React.lazy(() => import("../pages/customers/CustomersPage"));

export const Core = () => {
    const { route, isPending, authStatus } = useAuthenticator((context) => [
        context.route,
        context.isPending,
        context.authStatus,
        context.user,
    ]);

    useEffect(() => {
        if (authStatus === "unauthenticated") {
            window.localStorage.clear();
        }
    }, [authStatus]);

    if (route === "transition" || isPending) return <BackdropLoading isLoading={route === "transition"} />;

    return route === "authenticated" ? (
        <Router>
            <>
                <Suspense fallback={<Skeleton variant="rectangular" width="100%" height="64px" animation="wave" />}>
                    <Header />
                </Suspense>
                <Suspense fallback={<LoadingScreen />}>
                    <Routes>
                        <Route path="/" element={<JobsPage />} />
                        <Route path="/jobs" element={<JobsPage />} />
                        <Route path="/jobs/builder" element={<JobBuilderPage />} />
                        <Route path="/jobs/builder/:id" element={<JobBuilderPage />} />
                        <Route path="/interviews" element={<InterviewsPage />} />
                        <Route path="/interviews/:id" element={<ReviewPage />} />
                        <Route path="/videos" element={<VideosPage />} />
                        <Route path="/customers" element={<CustomersPage />} />
                        <Route path="/prompts" element={<PromptsPage />} />
                        <Route path="*" element={<JobsPage />} />
                    </Routes>
                </Suspense>
            </>
        </Router>
    ) : (
        <AuthenticatorPage />
    );
};
