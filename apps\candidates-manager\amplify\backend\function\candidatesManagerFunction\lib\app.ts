import awsServerlessExpressMiddleware from "aws-serverless-express/middleware";
import bodyParser from "body-parser";
import express from "express";
import { corsMiddleware } from "./middleware/cors";
import { errorHandler } from "./middleware/errorHandler";
import { initializeMiddleware, getLogger } from "./config/initialize";
import routes from "./routes";

const app = express();
const logger = getLogger();

app.use(bodyParser.json({ limit: "10mb" }));
app.use(awsServerlessExpressMiddleware.eventContext());
app.use(corsMiddleware);
app.use(initializeMiddleware);

app.use(routes);

app.use(errorHandler(logger));

app.listen(3000, () => {
    console.log("App started");
});

export default app;
