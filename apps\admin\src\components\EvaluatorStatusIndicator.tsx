import React from "react";
import { Chip } from "@mui/material";
import { InterviewStatus } from "@eva/shared-common";

const interviewStatusColors: Record<
    InterviewStatus,
    { label: string; color: "default" | "primary" | "secondary" | "error" | "success" | "warning" }
> = {
    interviewing: { label: "Interviewing", color: "default" },
    pending: { label: "Pending", color: "warning" },
    completed: { label: "Completed", color: "success" },
    running: { label: "Running", color: "primary" },
    failed: { label: "Failed", color: "error" },
    transferring: { label: "Transferring", color: "secondary" },
    invited: { label: "Invited", color: "default" },
};

interface InterviewStatusIndicatorProps {
    status: InterviewStatus;
}

export const InterviewStatusIndicator: React.FC<InterviewStatusIndicatorProps> = ({ status }) => {
    const { label, color } = interviewStatusColors[status];

    return <Chip label={label} color={color} />;
};
