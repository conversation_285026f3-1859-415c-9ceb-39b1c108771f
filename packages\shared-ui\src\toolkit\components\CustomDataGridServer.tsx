import { Stack, styled } from "@mui/material";
import {
    DataGrid,
    GridColDef,
    GridPaginationModel,
    GridRowSelectionModel,
    GridSortModel,
    GridValidRowModel
} from "@mui/x-data-grid";
import { FC, useLayoutEffect, useState } from "react";
import { useAppTheme } from "../theme/AppThemeProvider";

const StyledCDataGrid = styled(DataGrid)<{ isRtl: boolean }>(({ isRtl }) => ({
    ".MuiTablePagination-actions": {
        button: {
            transform: isRtl ? "rotate(180deg)" : "rotate(0deg)",
        },
    },
}));

interface CustomDataGridServerProps {
    rows: GridValidRowModel[];
    columns: GridColDef[];
    pageSizeOptions?: number[];
    tableName: string;
    paginationModel: GridPaginationModel;
    onPaginationModelChange: (model: GridPaginationModel) => void;
    sortModel: GridSortModel;
    onSortModelChange: (model: GridSortModel) => void;
    rowCount: number;
    loading?: boolean;
    height?: string;
    enableSelection?: boolean;
    handleSelect?: (selectedIds: GridRowSelectionModel) => void;
    selectionModel?: GridRowSelectionModel;
}

export const CustomDataGridServer: FC<CustomDataGridServerProps> = ({
    rows,
    columns,
    pageSizeOptions = [10, 20, 50],
    paginationModel,
    onPaginationModelChange,
    sortModel,
    onSortModelChange,
    rowCount,
    loading = false,
    height = '75dvh',
    enableSelection = false,
    handleSelect,
    selectionModel,
}) => {
    const { currentDirection } = useAppTheme();
    const [renderTrigger, setRenderTrigger] = useState(false);

    useLayoutEffect(() => {
        const pageNumberElement = document.querySelector(".MuiTablePagination-selectLabel");
        const ofElement = document.querySelector(".MuiTablePagination-displayedRows");
        if (ofElement && ofElement.textContent && pageNumberElement && pageNumberElement.textContent) {
            if (currentDirection === "rtl") ofElement.innerHTML = ofElement.textContent.replace("of", "מתוך");
            if (currentDirection === "ltr") ofElement.innerHTML = ofElement.textContent.replace("מתוך", "of");
            if (currentDirection === "rtl")
                pageNumberElement.innerHTML = pageNumberElement.textContent.replace("Rows per page:", "שורות בעמוד:");
            if (currentDirection === "ltr")
                pageNumberElement.innerHTML = pageNumberElement.textContent.replace("שורות בעמוד:", "Rows per page:");
        }
    }, [currentDirection, renderTrigger]);

    const handleStateChange = () => {
        setRenderTrigger((prev) => !prev);
    };  

    const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
        if (handleSelect) {
            handleSelect(newSelectionModel);
        }
    };

    return (
        <Stack height={height}>
            <StyledCDataGrid
                isRtl={currentDirection === "rtl"}
                onStateChange={handleStateChange}
                onPaginationModelChange={onPaginationModelChange}
                paginationModel={paginationModel}
                paginationMode="server"
                sortingMode="server"
                onSortModelChange={onSortModelChange}
                sortModel={sortModel}
                rowCount={rowCount}
                loading={loading}
                rows={rows}
                columns={columns}
                pageSizeOptions={pageSizeOptions}
                disableRowSelectionOnClick={!enableSelection}
                checkboxSelection={enableSelection}
                onRowSelectionModelChange={handleSelectionChange}
                rowSelectionModel={selectionModel}
            />
        </Stack>
    );
}; 