import React, { Suspense } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import { BackdropLoading } from "@eva/shared-ui";

const BadRequestPage = React.lazy(() => import("../pages/BadRequestPage"));
const InterviewPage = React.lazy(() => import("../pages/interviewer/InterviewPage"));
const PilatSetupPage = React.lazy(() => import("../pages/setup-pages/PilatSetupPage"));
export const Core = () => {
    return (
        <Router>
            <Suspense fallback={<BackdropLoading isLoading />}>
                <Routes>
                    <>
                        <Route path="/:jobId/:pilatInterviewId" element={<PilatSetupPage />} />
                        <Route path="/404" element={<BadRequestPage />} />
                        <Route path="/interviewer/:interviewId" element={<InterviewPage />} />
                    </>
                </Routes>
            </Suspense>
        </Router>
    );
};
