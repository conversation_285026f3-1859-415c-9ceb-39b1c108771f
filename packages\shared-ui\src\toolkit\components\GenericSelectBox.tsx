import React from "react";
import { MenuItem, Select, FormControl, SelectChangeEvent } from "@mui/material";

interface GenericSelectBoxProps {
    value: string; 
    onChange: (event: SelectChangeEvent<string>) => void; 
    options: string[]; 
    label?: string; 
}

export const GenericSelectBox: React.FC<GenericSelectBoxProps> = ({ value, onChange, options, label }) => {
    return (
        <FormControl fullWidth>
            <Select value={value} onChange={onChange} displayEmpty inputProps={{ 'aria-label': label || 'select-box' }}>
                {options.map((option, index) => (
                    <MenuItem key={index} value={option}>
                        {option}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    );
};

