import { H3, H4 } from "@eva/shared-ui";
import { KeyboardArrowRight } from "@mui/icons-material";
import { AppBar, Stack, Toolbar, styled } from "@mui/material";
import { useLocation } from "react-router-dom";
import { usePageTitle } from "@eva/shared-ui";
import { UserMenu } from "./sub-components/UserMenu";

const StyledAppBar = styled(AppBar)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    color: theme.palette.text.primary,
    boxShadow: "none",
    borderBottom: `1px solid ${theme.palette.divider}`,
    position: "static",
}));

const Header = () => {
    const location = useLocation();
    const { subtitle } = usePageTitle();

    const getPageTitle = (pathname: string): string => {
        const basePath = pathname.split("/")[1];

        switch (basePath) {
            case "":
                return "Candidates";
            case "jobs":
                return "Jobs";
            case "candidates":
                return "Candidates";
            default:
                return "";
        }
    };

    const pageTitle = getPageTitle(location.pathname);

    return (
        <StyledAppBar>
            <Toolbar>
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    gap={2}
                    width="100%"
                    paddingInline={4}
                >
                    <Stack direction="row" alignItems="center" spacing={1}>
                        <H3>{pageTitle}</H3>
                        {subtitle && (
                            <>
                                <KeyboardArrowRight color="action" />
                                <H4>{subtitle}</H4>
                            </>
                        )}
                    </Stack>
                    <UserMenu />
                </Stack>
            </Toolbar>
        </StyledAppBar>
    );
};

export default Header;
