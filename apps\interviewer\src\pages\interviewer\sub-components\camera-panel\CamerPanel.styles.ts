import styled from "@emotion/styled";
import { Stack } from "@mui/material";
import { basicTheme } from "@eva/shared-ui";
import { motion } from "framer-motion";

export const VideoPreviewContainer = styled(Stack)({
    position: "relative",
    borderRadius: "16px",
    height: "50dvh",
    width: "800px",
    overflow: "hidden",
    boxShadow: "0 10px 30px rgba(0, 0, 0, 0.1)",
    background: "linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))",
    backdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.18)",
    [basicTheme.breakpoints.down("sm")]: {
        maxHeight: "50dvh",
        width: "100%",
        borderRadius: 0,
    },
});

export const AnimatedVideoContainer = styled(motion.div)({
    position: "relative",
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
});

export const AnimatedComponentWrapper = styled(motion.div)({
    width: "100%",
});
