import { Search } from "@mui/icons-material";
import { InputAdornment, Stack, TextField } from "@mui/material";
import { FC } from "react";

export interface JobsFilterFields {
    searchText: string;
}

interface JobsDataTableFilterProps {
    filterFields: JobsFilterFields;
    handleFilterChange: (filterFields: JobsFilterFields) => void;
    resultsCount: number;
}

export const JobsDataTableFilter: FC<JobsDataTableFilterProps> = ({
    filterFields,
    handleFilterChange,
    resultsCount,
}) => {
    return (
        <Stack direction="row" spacing={2} alignItems="center">
            <TextField
                placeholder="Search jobs..."
                value={filterFields.searchText}
                onChange={(e) => handleFilterChange({ ...filterFields, searchText: e.target.value })}
                size="small"
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                    ),
                }}
            />
            <div>{resultsCount} results</div>
        </Stack>
    );
}; 