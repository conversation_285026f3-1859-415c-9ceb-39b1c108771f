{"name": "@eva/shared-ui", "version": "0.0.1", "private": true, "main": "index.ts", "types": "index.d.ts", "sideEffects": false, "scripts": {"build": "tsc", "lint": "eslint .", "test": "jest", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/stylis": "^4.2.5", "typescript": "^5.3.3", "eslint": "^8.56.0", "jest": "^29.7.0"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.7", "@mui/icons-material": "^5.15.14", "@mui/x-data-grid": "^7.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "react-toastify": "^10.0.5", "axios": "^1.7.7", "@aws-amplify/ui-react": "^6.1.6", "@fontsource/poppins": "^5.0.12", "formik": "^2.4.6", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}