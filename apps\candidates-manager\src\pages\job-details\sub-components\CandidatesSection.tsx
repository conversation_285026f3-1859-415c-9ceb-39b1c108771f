import { <PERSON><PERSON>, Toolt<PERSON> } from "@mui/material";
import { GridRowSelectionModel } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import { LoadingScreen, PrimaryButton, PageLayout } from "@eva/shared-ui";
import { CandidatesDataTable } from "../../candidates/sub-components/CandidatesDataTable";
import {
    CandidatesDataTableFilter,
    CandidatesFilterFields,
} from "../../candidates/sub-components/CandidatesDataTableFilter";
import { useValidateCandidatesForInvitationMutation, useInviteCandidatesMutation } from "../../../app-state/apis";
import { useState } from "react";
import { CMCandidate } from "@eva/shared-common";
interface CandidatesSectionProps {
    jobId: string;
    candidates: CMCandidate[];
    isLoading: boolean;
    inviteDisabled: boolean;
}

export const CandidatesSection = ({ jobId, candidates, isLoading, inviteDisabled }: CandidatesSectionProps) => {
    const [filterFields, setFilterFields] = useState<CandidatesFilterFields>({
        searchText: "",
    });
    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>([]);
    const [validateCandidates] = useValidateCandidatesForInvitationMutation();
    const [inviteCandidates, { isLoading: isInviting }] = useInviteCandidatesMutation();

    const handleFilterChange = (filterFields: CandidatesFilterFields) => {
        setFilterFields(filterFields);
    };

    const handleSelect = (selectedIds: GridRowSelectionModel) => {
        setSelectionModel(selectedIds);
    };

    const filteredCandidates = candidates.filter((candidate) => {
        return (
            candidate.name.toLowerCase().includes(filterFields.searchText.toLowerCase()) ||
            candidate.jobTitle.toLowerCase().includes(filterFields.searchText.toLowerCase())
        );
    });

    const handleInviteCandidates = async () => {
        if (selectionModel.length > 0 && jobId) {
            try {
                const validationResult = await validateCandidates({
                    candidateIds: selectionModel.map((id) => id.toString()),
                }).unwrap();

                if (validationResult.invalidCandidates.length > 0) {
                    const invalidCandidatesList = validationResult.invalidCandidates
                        .map((c: { candidateId: string; reason: string }) => {
                            const candidate = filteredCandidates.find(
                                (candidate) => candidate.id.toString() === c.candidateId
                            );
                            return `${candidate?.name || "Unknown candidate"}: ${c.reason}`;
                        })
                        .join(", ");
                    toast.error(`Cannot invite some candidates: ${invalidCandidatesList}`);
                    return;
                }

                await inviteCandidates({
                    jobId,
                    candidateIds: validationResult.validCandidates,
                }).unwrap();

                toast.success(`Successfully invited ${validationResult.validCandidates.length} candidates`);
                setSelectionModel([]);
            } catch (error) {
                console.error("Error inviting candidates:", error);
                toast.error("Failed to invite candidates");
            }
        }
    };

    return (
        <PageLayout>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                <CandidatesDataTableFilter
                    filterFields={filterFields}
                    handleFilterChange={handleFilterChange}
                    resultsCount={filteredCandidates.length}
                />
                <Tooltip title={inviteDisabled ? "Job is not active" : ""}>
                    <div>
                        <PrimaryButton
                            width="auto"
                            disabled={selectionModel.length === 0 || isInviting || inviteDisabled}
                            onClick={handleInviteCandidates}
                            content={isInviting ? "Sending..." : `Invite Selected (${selectionModel.length})`}
                        />
                    </div>
                </Tooltip>
            </Stack>
            <Stack>
                {isLoading ? (
                    <LoadingScreen height="45dvh" />
                ) : (
                    <CandidatesDataTable
                        height="50dvh"
                        candidates={filteredCandidates}
                        handleSelect={handleSelect}
                        selectionModel={selectionModel}
                    />
                )}
            </Stack>
        </PageLayout>
    );
};
