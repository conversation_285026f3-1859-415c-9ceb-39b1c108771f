{"compilerOptions": {"noEmitOnError": true, "incremental": true, "allowSyntheticDefaultImports": true, "target": "ES2015", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "Node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}