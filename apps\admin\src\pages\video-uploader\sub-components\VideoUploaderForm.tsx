import { Stack, Tabs, Tab } from "@mui/material";
import { useState } from "react";
import {
    useGetCustomersPageTableDataQuery,
    useGetFamiliesWithTraitsAndQuestionsQuery
} from "../../../app-state/apis";
import { H3, ModalLoadingSpinner } from "@eva/shared-ui";
import { WelcomeVideoForm } from "./WelcomeVideoForm";
import { QuestionVideosForm } from "./QuestionVideosForm";

export const VideoUploaderForm = () => {
    const { data: customersData } = useGetCustomersPageTableDataQuery();
    const { data: familiesWithQuestionsData } = useGetFamiliesWithTraitsAndQuestionsQuery();
    const [tabValue, setTabValue] = useState(0);

    const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    return (
        <Stack gap={4} minHeight={200} justifyContent='center'>
            {customersData && familiesWithQuestionsData ? (
                <>
                    <H3>Upload Videos</H3>
                    
                    <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <Tab label="Welcome Videos" />
                        <Tab label="Question Videos" />
                    </Tabs>
                    
                    {tabValue === 0 && (
                        <WelcomeVideoForm 
                            questionsData={familiesWithQuestionsData} 
                            customersData={customersData} 
                        />
                    )}
                    
                    {tabValue === 1 && (
                        <QuestionVideosForm 
                            questionsData={familiesWithQuestionsData} 
                            customersData={customersData} 
                            isLoading={false} 
                        />
                    )}
                </>
            ) : (
                <ModalLoadingSpinner />
            )}
        </Stack>
    );
};
