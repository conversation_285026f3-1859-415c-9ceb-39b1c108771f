# Common Library

This folder contains types and utilities that are shared between the client and the backend of the Iverse Admin application.

## Installation

To use this module, you need to install the following package:

```bash
npm install @aws-sdk/client-secrets-manager
```

## Usage

Import the necessary types and utilities from this module in your client or backend code to ensure consistency and reduce duplication.

```javascript
// Example import
import { someUtility, someType } from './common';
```

## Contributing

If you want to contribute to this module, please follow the standard contribution guidelines and ensure that your code is well-documented and tested.

## License

This project is licensed under the MIT License.