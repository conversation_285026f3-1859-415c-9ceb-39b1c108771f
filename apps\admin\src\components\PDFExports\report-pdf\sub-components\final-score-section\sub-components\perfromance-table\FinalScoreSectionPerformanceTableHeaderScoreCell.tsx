import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../../../PDF.styles";

interface FinalScoreSectionPerformanceTableHeaderScoreCellProps {
    isRtl: boolean;
    text: string;
    width: string;
    score: number;
    radius?: "topLeft" | "topRight";
}

export const FinalScoreSectionPerformanceTableHeaderScoreCell: FC<
    FinalScoreSectionPerformanceTableHeaderScoreCellProps
> = ({ isRtl, text, width, score, radius = "none" }) => {
    const borderRadius = radius === "topLeft" ? styles.leftTopRounded : radius === "topRight" ? styles.rightTopRounded : {};

    return (
        <View
            style={[
                styles.border,
                styles.displayFlex,
                styles.alignItemsCenter,
                styles.flexGap1,
                styles.justifyCenter,
                borderRadius,
                { width, height: 30 },
            ]}
        >
            <Text style={[styles.textBoldScore]}>{score}</Text>
            <Text style={[isRtl ? styles.hebrewTextSubTitle : styles.textSubTitle]}>{text}</Text>
        </View>
    );
};
