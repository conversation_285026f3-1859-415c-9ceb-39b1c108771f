{"staging": {"awscloudformation": {"AuthRoleName": "amplify-candidatesmanager-staging-9a95a-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-candidatesmanager-staging-9a95a-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-candidatesmanager-staging-9a95a-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-candidatesmanager-staging-9a95a-deployment", "UnauthRoleName": "amplify-candidatesmanager-staging-9a95a-unauthRole", "StackName": "amplify-candidatesmanager-staging-9a95a", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-candidatesmanager-staging-9a95a/16a56ff0-0277-11f0-b905-0ed90967629b", "AmplifyAppId": "d2q6sevltntydn", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-candidatesmanager-staging-9a95a-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"function": {"candidatesManagerFunction": {"deploymentBucketName": "amplify-candidatesmanager-staging-9a95a-deployment", "s3Key": "amplify-builds/candidatesManagerFunction-71325148644c71426361-build.zip"}}, "auth": {"candidatesmanager": {}}, "api": {"candidatesManagerApi": {}}, "hosting": {"amplifyhosting": {"appId": "d2q6sevltntydn", "type": "manual"}}}}}