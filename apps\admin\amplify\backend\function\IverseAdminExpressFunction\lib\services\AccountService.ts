import { DB } from "@eva/drizzle";
import { AccountsResponse } from "@eva/shared-common";
import { accounts } from "@eva/drizzle";
import { Logger } from "@eva/logger";
export class AccountService {
    db: DB;
    logger: Logger;
    constructor(db: <PERSON>, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    async getAccounts(): Promise<AccountsResponse> {
        this.logger.info("Getting all accounts");
        const res = await this.db
            .select({
                id: accounts.id,
                name: accounts.name,
            })
            .from(accounts);

        const allAccounts = res.map((item) => ({
            id: item.id,
            name: item.name,
        }));
        this.logger.info(`Found ${allAccounts.length} accounts`);
        return { accounts: allAccounts };
    }
}
