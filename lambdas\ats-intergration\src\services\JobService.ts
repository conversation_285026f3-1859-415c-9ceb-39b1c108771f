import { jobs, customers, DB, InferInsertModel, eq } from '@eva/drizzle';
import { CreateJobRequest, CreateJobResponse } from '../types';
import { Logger } from '@eva/logger';
import { createDefaultInvitationTemplates } from '@eva/shared-common/utils/createDefaultInvitationTemplates';

type NewJob = InferInsertModel<typeof jobs>;

export class JobService {
    supportedLanguages = ['en', 'he'];
    constructor(private db: DB, private logger: Logger) {}

    validateLanguage(language: string): boolean {
        return this.supportedLanguages.includes(language);
    }

    async createJobForCustomer(customerId: string, createJobRequest: CreateJobRequest): Promise<CreateJobResponse> {
        this.logger.info(
            `Attempting to create job for customerId: ${customerId} with data: ${JSON.stringify(createJobRequest)}`,
        );

        if (!this.validateLanguage(createJobRequest.language)) {
            this.logger.warn(`Invalid language: ${createJobRequest.language}`);
            throw Object.assign(new Error('Invalid language'), { statusCode: 400 });
        }

        const existingCustomer = await this.db
            .select({ id: customers.id })
            .from(customers)
            .where(eq(customers.id, customerId))
            .limit(1);

        if (existingCustomer.length === 0) {
            this.logger.warn(`Customer not found: ${customerId}`);
            throw Object.assign(new Error('Customer not found'), { statusCode: 404 });
        }

        const jobData: NewJob = {
            jobTitle: createJobRequest.name,
            description: createJobRequest.description,
            language: createJobRequest.language,
            customerId: customerId,
            invitation: {
                emailTag: "[email]",
                nameTag: "[name]",
                phoneTag: "[phone]",
                mailTemplateOption: createDefaultInvitationTemplates().mail,
                smsTemplateOption: createDefaultInvitationTemplates().sms,
            },
            status: 'pending-definition',
            questions: {
                questionIds: [],
                openQuestions: [],
                computedTraits: [],
                warmupQuestions: [],
                simulations: [],
            },
        };

        const newJob = await this.db.insert(jobs).values(jobData).returning({ jobId: jobs.id });
        const jobId = newJob[0].jobId;

        this.logger.info(`Successfully created job with id ${jobId} for customer ${customerId}`);
        return { jobId };
    }
}
