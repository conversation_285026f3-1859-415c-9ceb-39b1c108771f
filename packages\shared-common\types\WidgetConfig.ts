import { Gender } from "./Gender";

export interface WidgetSubmitRequest {
    jobId: string;

    videoData: {
        videoUrl?: string;
        interviewCompleted: boolean;
    };
}

export interface WidgetConfigRequest {
    jobId: string;
    externalId: string;
}

export interface WidgetQuestion {
    question: string;
    attempts: number;
    partDuration: number;
    thinkingTime: number;
    timeLimit?: number;
    videoQuestion?: string;
    description?: string;
}

export interface PilatWidgetConfigResponse {
    gender: Gender;
    logoLink: string | null;
    apiKey: string;
    themeColor: string | null;
    introVideo: string | null;
    welcomeTitle: string;
    candidateEmail: string;
    candidateName: string;
    myInterviewJobId: string;
    interviewId: string;
    questions: Array<WidgetQuestion>;
}

export interface ExternalWidgetConfigResponse extends Omit<PilatWidgetConfigResponse, "gender"> {
    email: string;
}

export interface InterviewWidgetConfigResponse {
    gender: Gender;
    logoLink: string | null;
    apiKey: string;
    themeColor: string | null;
    introVideo: string | null;
    welcomeTitle: string;
    candidateEmail: string;
    candidateName: string;
    myInterviewJobId: string;
    interviewId: string;
    questions: Array<WidgetQuestion>;
    email: string;
    language: string;
}
