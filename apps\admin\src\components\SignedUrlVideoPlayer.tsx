import { Skeleton, styled } from "@mui/material";
import { FC } from "react";
import { useGetSignedUrlQuery } from "../app-state/apis";

export const StyledVideo = styled("video")<{ height: string; width: string }>(({ height, width }) => ({
    width,
    height,
    borderRadius: "5px",
}));

interface SignedUrlVideoPlayerProps {
    url: string;
    expiration?: number;
    width?: string;
    height?: string;
}

export const SignedUrlVideoPlayer: FC<SignedUrlVideoPlayerProps> = ({ url, expiration = 120, width = "100%", height = "200px" }) => {
    const { data, isLoading } = useGetSignedUrlQuery({ url, expiration });

    return isLoading && !data ? (
        <Skeleton variant='rectangular' width='100%' height='150px' animation='wave' />
    ) : (
        <StyledVideo height={height} width={width} controls src={data?.url} autoPlay={false} />
    );
};
