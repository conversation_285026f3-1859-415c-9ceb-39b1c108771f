import { Stack, Box } from "@mui/material";
import <PERSON><PERSON> from "lottie-react";
import { useLayoutEffect, useRef, useState, useEffect } from "react";
import talkingAvatar from "../../../../../../../assets/animations/talking-avatar.json";
import { H4, <PERSON>C<PERSON>Wrapper, useMediaQueryContext } from "@eva/shared-ui";
import { VideoPlayer } from "../../../../../../../components/video-player/VideoPlayer";
import { useInterviewer } from "../../../../../InterviewerProvider";

interface QuestionPreviewContentProps {
    videoUrl?: string;
    content: string;
    hideContent?: boolean;
}

export const QuestionPreviewContent = ({ content, videoUrl, hideContent }: QuestionPreviewContentProps) => {
    const { isMobile } = useMediaQueryContext();
    const { handleCountDownChange } = useInterviewer();
    const videoPlayerRef = useRef<HTMLDivElement>(null);
    const [videoWidth, setVideoWidth] = useState(450);
    
    const onVideoEnd = () => {
        handleCountDownChange(true);
    };

    useEffect(() => {
        if (videoPlayerRef.current) {
            setVideoWidth(videoPlayerRef.current.offsetWidth);
        }
    }, [videoPlayerRef.current]);

    useLayoutEffect(() => {
        if (!videoUrl) {
            handleCountDownChange(true);
        }
    }, [videoUrl]);

    return (
        <>
            <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
                {!isMobile && (
                    <Box ref={videoPlayerRef}>
                        {videoUrl ? (
                            <VideoPlayer
                                onVideoEnd={onVideoEnd}
                                data-qa="question-video-preview"
                                width="100%"
                                height="50dvh"
                                videoUrl={videoUrl}
                            />
                        ) : (
                            <Lottie width={450} animationData={talkingAvatar} loop />
                        )}
                    </Box>
                )}
                {!hideContent && (
                    <NoCopyWrapper>
                        <div style={{ width: isMobile ? '100%' : `${videoWidth}px`, maxWidth: '100%' }}>
                            <H4 data-qa="question-text-preview">{content}</H4>
                        </div>
                    </NoCopyWrapper>
                )}
            </Stack>
        </>
    );
};
