import React from "react";
import { Formik, Form, FormikHelpers } from "formik";
import * as Yup from "yup";
import { CreateCandidateRequest } from "@eva/shared-common";
import { useCreateCandidateMutation, useValidateEmailMutation } from "../../../app-state/apis";
import { toast } from "react-toastify";
import { CandidateFormBody } from "./CandidateFormBody";

interface CreateCandidateFormProps {
    jobId: string;
    isHebrew: boolean;
    onSuccess: () => void;
    onCancel: () => void;
}

export const CreateCandidateForm: React.FC<CreateCandidateFormProps> = ({ jobId, isHebrew, onSuccess, onCancel }) => {
    const [createCandidate, { isLoading: isCreating }] = useCreateCandidateMutation();
    const [validateEmail, { isLoading: isValidating }] = useValidateEmailMutation();

    const validationSchema = Yup.object({
        firstName: Yup.string().required("First name is required"),
        lastName: Yup.string().required("Last name is required"),
        email: Yup.string().email("Invalid email format").required("Email is required"),
        ...(isHebrew && {
            socialId: Yup.string().matches(/^[0-9]+$/, "Social ID must contain only numbers").length(9, "Social ID must be 9 digits"),
            phoneNumber: Yup.string()
                .matches(/^[0-9]*$/, "Phone number must contain only numbers")
                .test(
                    'is-israeli-number',
                    'Must be a valid Israeli phone number',
                    function(value) {
                        if (!value) return true; 
                        return /^(05\d{8})$/.test(value);
                    }
                )
        })
    });

    const initialValues: CreateCandidateRequest = {
        firstName: "",
        lastName: "",
        email: "",
        jobId,
        ...(isHebrew ? {
            socialId: "",
            phoneNumber: ""
        } : {})
    };

    const handleSubmit = async (values: CreateCandidateRequest, { setSubmitting }: FormikHelpers<CreateCandidateRequest>) => {
        try {
            const validationResult = await validateEmail({
                email: values.email,
                jobId
            }).unwrap();

            if (!validationResult.isValid) {
                toast.error(validationResult.message || "Email validation failed");
                setSubmitting(false);
                return;
            }

            await createCandidate(values).unwrap();
            toast.success("Candidate created successfully");
            onSuccess();
        } catch (error) {
            toast.error("Failed to create candidate");
            console.error("Error creating candidate:", error);
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            validateOnBlur={false}
            validateOnChange={false}
            validateOnMount={false}
            onSubmit={handleSubmit}
        >
            <Form>
                <CandidateFormBody 
                    isHebrew={isHebrew} 
                    onCancel={onCancel} 
                    isLoading={isCreating || isValidating}
                />
            </Form>
        </Formik>
    );
}; 