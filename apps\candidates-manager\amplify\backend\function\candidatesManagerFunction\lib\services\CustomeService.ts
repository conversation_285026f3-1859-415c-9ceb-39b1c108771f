import { Logger } from "@eva/logger";
import { customers } from "@eva/drizzle";
import { DB } from "@eva/drizzle";
import { CMCustomerThemeResponse } from "@eva/shared-common";
import { eq } from "@eva/drizzle";

export class CustomerService {
    constructor(
        private db: DB,
        private logger: Logger
    ) {}

    async getCustomerLogo(customerId: string): Promise<{ logoLink: string } | null> {
        try {
            const customer = await this.db
                .select({
                    logoLink: customers.logoLink,
                })
                .from(customers)
                .where(eq(customers.id, customerId));
            return customer[0].logoLink ? { logoLink: customer[0].logoLink } : null;
        } catch (error) {
            this.logger.error(`Error getting customer ${customerId}`, error);
            throw error;
        }
    }

    async getCustomerTheme(customerId: string): Promise<CMCustomerThemeResponse> {
        try {
            this.logger.info(`Getting theme color for customer ${customerId}`);

            const result = await this.db
                .select({
                    themeColor: customers.themeColor,
                })
                .from(customers)
                .where(eq(customers.id, customerId))
                .limit(1);

            if (result.length === 0) {
                this.logger.warn(`Customer with ID ${customerId} not found`);
                return { themeColor: null };
            }

            return { themeColor: result[0].themeColor || null };
        } catch (error) {
            this.logger.error(`Error getting theme color for customer ${customerId}: ${error}`);
            throw error;
        }
    }
}
