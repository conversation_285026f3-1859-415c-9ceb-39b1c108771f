import { useInterviewer } from "../../../../InterviewerProvider";
import { QuestionStep } from "./sub-components/question-step/QuestionStep";
import { SimulationStep } from "./sub-components/simulation-step/SimulationStep";
import { motion, AnimatePresence } from "framer-motion";

const fadeAnimation = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.5 }
};

export const InterviewStepper = () => {
    const { isInterviewOn, currentStep } = useInterviewer();
    const stepType = currentStep?.type;


    return isInterviewOn ? (
        <AnimatePresence mode="wait">
            {stepType === "simulation" && (
                <motion.div key="simulation" {...fadeAnimation}>
                    <SimulationStep />
                </motion.div>
            )}
            {stepType === "general" && (
                <motion.div key="general" {...fadeAnimation}>
                    <QuestionStep />
                </motion.div>
            )}
            {stepType === "warmup" && (
                <motion.div key="warmup" {...fadeAnimation}>
                    <QuestionStep />
                </motion.div>
            )}
            {stepType === "open" && (
                <motion.div key="open" {...fadeAnimation}>
                    <QuestionStep />
                </motion.div>
            )}
        </AnimatePresence>
    ) : (
        <></>
    );
};
