import React from "react";
import { Formik, Form, FormikHelpers } from "formik";
import * as Yup from "yup";
import { CreateCandidateRequest } from "@eva/shared-common";
import {
    useBulkCreateCandidatesMutation,
    useLazyCheckDuplicateCandidateQuery,
    useValidateEmailMutation
} from "../../../app-state/apis/candidatesManagerApi";
import { toast } from "react-toastify";
import { ParsedCandidate } from "./types/BulkImportTypes";
import { BulkImportFormBody } from "./BulkImportFormBody";

interface FormikBulkImportFormProps {
    jobId: string;
    isHebrew: boolean;
    onSuccess: () => void;
    onCancel: () => void;
}

interface BulkImportFormValues {
    candidates: ParsedCandidate[];
    file: File | null;
}

export const FormikBulkImportForm: React.FC<FormikBulkImportFormProps> = ({ jobId, isHebrew, onSuccess, onCancel }) => {
    const [bulkCreateCandidates, { isLoading: isCreating }] = useBulkCreateCandidatesMutation();
    const [checkDuplicateCandidate] = useLazyCheckDuplicateCandidateQuery();
    const [validateEmail, { isLoading: isValidating }] = useValidateEmailMutation();

    const validationSchema = Yup.object({
        candidates: Yup.array().of(
            Yup.object({
                firstName: Yup.string().required("First name is required"),
                lastName: Yup.string().required("Last name is required"),
                email: Yup.string().email("Invalid email format").required("Email is required"),

                ...(isHebrew && {
                    socialId: Yup.string()
                        .matches(/^[0-9]+$/, "Social ID must contain only numbers")
                        .length(9, "Social ID must be 9 digits"),
                    phoneNumber: Yup.string()
                        .matches(/^[0-9]*$/, "Phone number must contain only numbers")
                        .test("is-israeli-number", "Must be a valid Israeli phone number", function (value) {
                            if (!value) return true;
                            return /^(05\d{8})$/.test(value);
                        }),
                }),
                isValid: Yup.boolean(),
                errors: Yup.array().of(Yup.string()),
                isDuplicate: Yup.boolean(),
            })
        ),
        file: Yup.mixed().required("Please upload a file"),
    });

    const initialValues: BulkImportFormValues = {
        candidates: [],
        file: null,
    };

    const checkIfDuplicate = async (email: string): Promise<boolean> => {
        try {
            const response = await checkDuplicateCandidate({
                email,
                jobId,
            }).unwrap();
            return response.exists;
        } catch (error) {
            console.error("Error checking for duplicate candidate:", error);
            return false;
        }
    };

    const validateCandidateEmail = async (email: string): Promise<{ isValid: boolean; message?: string }> => {
        try {
            const result = await validateEmail({
                email,
                jobId
            }).unwrap();
            return result;
        } catch (error) {
            console.error("Error validating email:", error);
            return { isValid: false, message: "Error validating email" };
        }
    };

    const handleSubmit = async (
        values: BulkImportFormValues,
        { setSubmitting, resetForm }: FormikHelpers<BulkImportFormValues>
    ) => {
        try {
            const validCandidates = values.candidates.filter((c) => c.isValid && !c.isDuplicate);

            if (validCandidates.length === 0) {
                toast.error("No valid candidates to import");
                return;
            }

            const candidatesToCreate: CreateCandidateRequest[] = validCandidates.map((c) => ({
                firstName: c.firstName,
                lastName: c.lastName,
                email: c.email,
                jobId,
                ...(isHebrew
                    ? {
                          socialId: c.socialId,
                          phoneNumber: c.phoneNumber,
                      }
                    : {}),
            }));

            await bulkCreateCandidates({ candidates: candidatesToCreate }).unwrap();
            toast.success(`Successfully imported ${validCandidates.length} candidates`);
            resetForm();
            onSuccess();
        } catch (error) {
            console.error("Error importing candidates:", error);
            toast.error("Failed to import candidates");
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            validateOnChange={false}
            validateOnBlur={false}
            onSubmit={handleSubmit}
        >
            {(formikProps) => (
                <Form>
                    <BulkImportFormBody
                        isHebrew={isHebrew}
                        onCancel={onCancel}
                        isCreating={isCreating || isValidating}
                        checkDuplicateCandidate={checkIfDuplicate}
                        validateEmail={validateCandidateEmail}
                        jobId={jobId}
                        {...formikProps}
                    />
                </Form>
            )}
        </Formik>
    );
};
