import React, { <PERSON> } from "react";
import { CustomDataGridClient, formatDate } from "@eva/shared-ui";
import { GridColDef } from "@mui/x-data-grid";  
import { IconButton } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import { CustomersTableData } from "@eva/shared-common";

interface CustomersDataTableProps {
    customers: CustomersTableData[];
    handleEditCustomerClicked: (customerId: string) => void;
}

const CustomersDataTable: FC<CustomersDataTableProps> = ({ customers, handleEditCustomerClicked }) => {
    const columns: GridColDef<CustomersTableData>[] = [
        {
            field: "companyName",
            headerName: "Customer",
            type: "string",
            width: 400,
            disableColumnMenu: true,
        },
        {
            field: "accountName",
            headerName: "Account",
            type: "string",
            disableColumnMenu: true,
            width: 300,
        },
        {
            field: "integration",
            headerName: "Integration",
            type: "string",
            disableColumnMenu: true,
            width: 200,
        },
        {
            field: "createdAt",
            headerName: "Created At",
            type: "string",
            disableColumnMenu: true,
            width: 300,
            renderCell: (params) => (params.value ? formatDate(params.value) : ""),
        },
        {
            field: "id",
            headerName: "",
            sortable: false,
            width: 100,
            disableColumnMenu: true,
            renderCell: (params) => (
                <IconButton onClick={() => handleEditCustomerClicked(params.row.id)}>
                    <EditIcon />
                </IconButton>
            ),
        },
    ];

    return <CustomDataGridClient tableName='customers-table' defaultSortField='createdAt' rows={customers} columns={columns} />;
};


export default CustomersDataTable;