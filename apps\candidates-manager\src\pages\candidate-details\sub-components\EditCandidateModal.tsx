import { Stack } from "@mui/material";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import { FormikTextField } from "../../../components/FormikFields";
import { PrimaryButton, CenteredModal, H3 } from "@eva/shared-ui";
import { useUpdateCandidateMutation, useValidateEmailMutation } from "../../../app-state/apis/candidatesManagerApi";
import { toast } from "react-toastify";
import { CMCandidateDetailsResponse } from "@eva/shared-common";

interface EditCandidateModalProps {
    isOpen: boolean;
    onClose: () => void;
    candidate: CMCandidateDetailsResponse;
}

interface FormValues {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    socialId?: string;
}

export const EditCandidateModal: React.FC<EditCandidateModalProps> = ({ isOpen, onClose, candidate }) => {
    const [updateCandidate] = useUpdateCandidateMutation();
    const [validateEmail] = useValidateEmailMutation();

    const initialValues: FormValues = {
        firstName: candidate.name.split(" ")[0],
        lastName: candidate.name.split(" ").slice(1).join(" "),
        email: candidate.email || "",
        phoneNumber: candidate.phoneNumber || "",
        socialId: candidate.socialId || "",
    };

    const validationSchema = Yup.object().shape({
        firstName: Yup.string().required("First name is required"),
        lastName: Yup.string().required("Last name is required"),
        email: Yup.string()
            .email("Invalid email")
            .required("Email is required")
            .test("unique-email", "This email is already in use for this job", async function (value) {
                if (!value) return true;
                if (value === initialValues.email) return true;

                try {
                    const result = await validateEmail({
                        email: value,
                        jobId: candidate.jobId,
                    }).unwrap();
                    return result.isValid;
                } catch (error) {
                    return false;
                }
            }),
        phoneNumber: Yup.string().nullable(),
        socialId: Yup.string().nullable(),
    });

    const handleSubmit = async (values: FormValues) => {
        try {
            console.log(values);
            await updateCandidate({
                candidateId: candidate.id,
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                phoneNumber: values.phoneNumber,
                socialId: values.socialId,
            }).unwrap();

            toast.success("Candidate details updated successfully");
            onClose();
        } catch (error) {
            console.error("Failed to update candidate:", error);
            toast.error("Failed to update candidate details");
        }
    };

    return (
        <CenteredModal isOpen={isOpen} onClose={onClose} width={500} height="auto">
            <Stack spacing={3}>
                <H3>Edit Candidate Details</H3>
                <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
                    {({ handleSubmit, isSubmitting }) => (
                        <Stack spacing={2}>
                            <FormikTextField name="firstName" label="First Name" required />
                            <FormikTextField name="lastName" label="Last Name" required />
                            <FormikTextField name="email" label="Email" required />
                            <FormikTextField name="phoneNumber" label="Phone Number" />
                            <FormikTextField name="socialId" label="Social ID" />
                            <Stack direction="row" spacing={2} justifyContent="flex-end">
                                <PrimaryButton content="Cancel" onClick={onClose} variant="outlined" width="auto" />
                                <PrimaryButton
                                    content="Save"
                                    onClick={handleSubmit}
                                    disabled={isSubmitting}
                                    variant="contained"
                                    width="auto"
                                />
                            </Stack>
                        </Stack>
                    )}
                </Formik>
            </Stack>
        </CenteredModal>
    );
};
