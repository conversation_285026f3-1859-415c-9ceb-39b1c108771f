import {
    InterviewerJobD<PERSON>,
    InterviewerJobStep,
    SubmitAnswerRequest,
    UploadCandidateImageRequest,
} from "@eva/shared-common";
import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseQuery";

export const interviewApi = createApi({
    reducerPath: "InterviewerApi",
    baseQuery,
    tagTypes: ["CurrentStep", "Terms"],
    endpoints: (builder) => ({
        setupPilatInterview: builder.query<{ interviewId: string }, { jobId: string; pilatInterviewId: string }>({
            query: ({ jobId, pilatInterviewId }) => {
                return {
                    url: `/setup/${jobId}/${pilatInterviewId}`,
                    method: "GET",
                };
            },
        }),
        getInterviewSetup: builder.query<InterviewerJobData, string>({
            query: (interviewId) => {
                return {
                    url: `/setup/${interviewId}`,
                    method: "GET",
                };
            },
        }),
        saveCandidateImage: builder.mutation<void, UploadCandidateImageRequest>({
            query: (request) => {
                return {
                    url: `/upload-image`,
                    method: "POST",
                    body: request,
                };
            },
            onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
                try {
                    await queryFulfilled;
                    dispatch(
                        interviewApi.util.updateQueryData("getInterviewSetup", arg.interviewId, (draft) => {
                            draft.isImageExists = true;
                        })
                    );
                } catch (error) {
                    console.error("Error updating setupInterview data:", error);
                }
            },
        }),
        getCurrentStep: builder.query<InterviewerJobStep | null, string>({
            query: (interviewId) => {
                return {
                    url: `/current-step/${interviewId}`,
                    method: "GET",
                };
            },
        }),
        submitAnswer: builder.mutation<void, SubmitAnswerRequest>({
            query: (request) => {
                return {
                    url: `/submit-answer`,
                    method: "POST",
                    body: request,
                };
            },
            invalidatesTags: ["CurrentStep"],
        }),
        startInterview: builder.mutation<void, string>({
            query: (interviewId) => {
                return {
                    url: `/start-interview/${interviewId}`,
                    method: "POST",
                };
            },
        }),
        getTermsAndConditionsUrl: builder.query<{ termsUrl: string }, void>({
            query: () => ({
                url: `/config/terms-url`,
                method: "GET",
            }),
            transformResponse: (response: { termsUrl: string }) => {
                return response;
            },
            providesTags: ["Terms"],
        }),
    }),
});

export const {
    useGetInterviewSetupQuery,
    useSaveCandidateImageMutation,
    useGetCurrentStepQuery,
    useSubmitAnswerMutation,
    useStartInterviewMutation,
    useSetupPilatInterviewQuery,
    useGetTermsAndConditionsUrlQuery,
} = interviewApi;
