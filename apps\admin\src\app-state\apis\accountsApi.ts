import { createApi } from "@reduxjs/toolkit/query/react";
import { AccountsResponse } from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const accountsApi = createApi({
    reducerPath: "accountsApi",
    baseQuery,
    tagTypes: ["AccountsResponse"],
    endpoints: (builder) => ({
        getAllAccounts: builder.query<AccountsResponse, void>({
            query: () => ({ url: "/accounts", method: "GET" }),
            providesTags: ["AccountsResponse"],
        }),
        checkIfInternalAccount: builder.query<{ isInternal: boolean }, string>({
            query: (accountId) => ({
                url: `/is-internal-account/${accountId}`,
                method: "GET",
            }),
        }),
    }),
});

export const {
    useGetAllAccountsQuery,
    useCheckIfInternalAccountQuery,
    useLazyCheckIfInternalAccountQuery,
} = accountsApi; 