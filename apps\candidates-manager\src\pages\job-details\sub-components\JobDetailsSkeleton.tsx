import { Skeleton, Stack } from "@mui/material";

export const HeaderSkeleton = () => (
    <Stack direction="row" justifyContent="space-between" alignItems="center" height="60px">
        <Skeleton variant="rectangular" width={100} height={40} />
        <Stack direction="row" gap={2}>
            <Skeleton variant="rectangular" width={150} height={40} />
            <Skeleton variant="rectangular" width={150} height={40} />
            <Skeleton variant="rectangular" width={150} height={40} />
        </Stack>
    </Stack>
);

export const JobDetailsSkeleton = () => (
    <Stack spacing={2} height="200px">
        <Skeleton variant="rectangular" width="100%" height={50} />
        <Skeleton variant="rectangular" width="100%" height={30} />
        <Skeleton variant="rectangular" width="80%" height={30} />
        <Skeleton variant="rectangular" width="60%" height={30} />
    </Stack>
);

export const CandidatesSectionSkeleton = () => (
    <Stack spacing={2} height="400px">
        <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Skeleton variant="rectangular" width={200} height={40} />
            <Skeleton variant="rectangular" width={150} height={40} />
        </Stack>
        <Skeleton variant="rectangular" width="100%" height={50} />
        <Skeleton variant="rectangular" width="100%" height={300} />
    </Stack>
);

export const HiddenSkeleton = () => (
    <Skeleton variant="rectangular" width={0} height={0} sx={{ display: 'none' }} />
); 