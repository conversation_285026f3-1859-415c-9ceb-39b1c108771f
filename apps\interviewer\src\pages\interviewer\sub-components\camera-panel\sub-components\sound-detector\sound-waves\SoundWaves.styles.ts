import { styled } from "@mui/material";
import { calculateMaxRadiusSize } from "./SoundWaves.utils";

interface SoundWavesContainerProps {
    value?: number;
    top: string;
    left: string;
}

export const SoundWavesContainer = styled("div")<SoundWavesContainerProps>(
    ({ value, top, left }) => ({
        position: "absolute",
        top,
        left,
        transform: "translate(-50%,-50%)",
        ".outer-circle": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(38, 255, 161, 0.3)",
            height: `${25 + calculateMaxRadiusSize(value, 2.2, 22)}px`,
            width: `${25 + calculateMaxRadiusSize(value, 2.2, 22)}px`,
            borderRadius: "50px",
        },
        ".mid-circle": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(38, 255, 161, 0.4)",
            height: `${25 + calculateMaxRadiusSize(value, 1.5, 18)}px`,
            width: `${25 + calculateMaxRadiusSize(value, 1.5, 18)}px`,
            borderRadius: "50px",
        },
        ".inner-circle": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(38, 255, 161, 6)",
            height: `${25 + calculateMaxRadiusSize(value, 1, 16)}px`,
            width: `${25 + calculateMaxRadiusSize(value, 1, 16)}px`,
            borderRadius: "50px",
        },
        ".core-circle": {
            borderRadius: "50px",
            height: "30px",
            width: "30px",
            backgroundColor: "rgba(38, 255, 161, 1)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        },
    })
);
