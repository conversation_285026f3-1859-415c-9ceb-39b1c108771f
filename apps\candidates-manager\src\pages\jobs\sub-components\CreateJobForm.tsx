import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Stack, MenuItem } from "@mui/material";
import { toast } from "react-toastify";
import { FormikTextField, FormikSelectField } from "../../../components/FormikFields";
import { PrimaryButton } from "@eva/shared-ui";
import { useCreateJobMutation } from "../../../app-state/apis";
import { useUserDetails } from "../../../app-state/context/UserDetailsProvider";
import { CMCreateJobRequest } from "@eva/shared-common";


const languageDefaultOptions = [
    { value: "en", label: "English" },
    { value: "he", label: "Hebrew" },
];

interface CreateJobFormProps {
    onSuccess: () => void;
    onCancel: () => void;
}

interface FormValues {
    jobTitle: string;
    description: string;
    language: "en" | "he"; // Assuming these are the only supported languages for now
}

const validationSchema = Yup.object({
    jobTitle: Yup.string().required("Job title is required"),
    description: Yup.string().required("Description is required"),
    language: Yup.string().oneOf(["en", "he"]).required("Language is required"),
});

export const CreateJobForm: React.FC<CreateJobFormProps> = ({ onSuccess, onCancel }) => {
    const [createJob, { isLoading }] = useCreateJobMutation();
    const { customerId } = useUserDetails();

    const initialValues: FormValues = {
        jobTitle: "",
        description: "",
        language: "en", // Default to English
    };

    const handleSubmit = async (values: FormValues) => {
        if (!customerId) {
            toast.error("Customer ID not found. Cannot create job.");
            return;
        }

        const jobData: CMCreateJobRequest = {
            ...values,
            customerId,
        };

        try {
            await createJob(jobData).unwrap();
            toast.success("Job created successfully!");
            onSuccess();
        } catch (error) {
            console.error("Failed to create job:", error);
            toast.error("Failed to create job. Please try again.");
        }
    };

    return (
        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
            {({ handleSubmit: formikSubmit }) => (
                <Form onSubmit={formikSubmit}>
                    <Stack spacing={3}>
                        <h2>Create New Job</h2>
                        <FormikTextField name="jobTitle" label="Job Title" required />
                        <FormikTextField name="description" label="Description" required multiline  />
                        <FormikSelectField
                            name="language"
                            label="Language"
                            required
                            options={languageDefaultOptions}
                        />
                        <Stack direction="row" spacing={2} justifyContent="flex-end">
                            <PrimaryButton
                                content="Cancel"
                                onClick={onCancel}
                                variant="outlined"
                                width="auto"
                                disabled={isLoading}
                            />
                            <PrimaryButton
                                content="Create Job"
                                onClick={() => formikSubmit()}
                                disabled={isLoading}
                                variant="contained"
                                width="auto"
                                isLoading={isLoading}
                            />
                        </Stack>
                    </Stack>
                </Form>
            )}
        </Formik>
    );
};
