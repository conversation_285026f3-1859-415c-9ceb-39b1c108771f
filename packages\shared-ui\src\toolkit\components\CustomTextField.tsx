import { FormControl, Stack, TextField } from "@mui/material";
import React, { FC } from "react";

interface CustomTextFieldProps {
    value: string;
    onChange: (event: React.ChangeEvent<{ value: string }>) => void;
    label: string;
    width?: number;
    disabled?: boolean;
}

export const CustomTextField: FC<CustomTextFieldProps> = ({ disabled, label, onChange, value, width = "100%" }) => {
    return (
        <Stack width={width}>
            <FormControl fullWidth>
                <TextField value={value} onChange={onChange} label={label} disabled={disabled} />
            </FormControl>
        </Stack>
    );
};
