export async function convertBlobUrlToBlob(blobUrl: string): Promise<Blob | void> {
    try {
        const response = await fetch(blobUrl);
        const blob = await response.blob();
        return blob;
    } catch (error) {
        console.error(error);
    }
}

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const fetchBlob = async (url: string): Promise<Blob> => {
    const response = await fetch(url);
    return await response.blob();
};

export const splitAndCapitalize = (str: string): string =>
    str
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

export const formatDate = (strDate: string, withHours: boolean = true) => {
    if (strDate.includes(" ")) {
        strDate = strDate.split(".")[0].replace(" ", "T");
    }

    const date = new Date(strDate.endsWith("Z") ? strDate : strDate + "Z");

    const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    const month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
    const year = date.getFullYear();

    const hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
    const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();

    return withHours ? `${day}.${month}.${year} ${hours}:${minutes}` : `${day}.${month}.${year}`;
};

export const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = () => {
            const base64data = reader.result?.toString().split(",")[1] || "";
            resolve(base64data);
        };
        reader.onerror = (error) => reject(error);
    });
};

export const generateUniqueString = (length: number): string => {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
};
