import { Search } from "@mui/icons-material";
import { InputAdornment, Stack, TextField } from "@mui/material";
import { FC } from "react";

export interface CandidatesFilterFields {
    searchText: string;
}

interface CandidatesDataTableFilterProps {
    filterFields: CandidatesFilterFields;
    handleFilterChange: (filterFields: CandidatesFilterFields) => void;
    resultsCount: number;
}

export const CandidatesDataTableFilter: FC<CandidatesDataTableFilterProps> = ({
    filterFields,
    handleFilterChange,
    resultsCount,
}) => {
    return (
        <Stack direction="row" spacing={2} alignItems="center">
            <TextField
                placeholder="Search candidates..."
                value={filterFields.searchText}
                onChange={(e) => handleFilterChange({ ...filterFields, searchText: e.target.value })}
                size="small"
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                    ),
                }}
            />
            <div>{resultsCount} results</div>
        </Stack>
    );
}; 