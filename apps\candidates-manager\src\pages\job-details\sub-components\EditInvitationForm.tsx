import React from "react";
import { Formik } from "formik";
import * as Yup from "yup";
import { useGetJobByIdQuery, useUpdateJobInvitationMutation } from "../../../app-state/apis";
import { toast } from "react-toastify";
import { LoadingScreen } from "@eva/shared-ui";
import { createDefaultInvitationTemplates, JobInvitationData } from "@eva/shared-common";
import { EditInvitationFormBody } from "./EditInvitationFormBody";

interface EditInvitationFormProps {
    jobId: string;
    onSuccess: () => void;
    onCancel: () => void;
}

export const EditInvitationForm: React.FC<EditInvitationFormProps> = ({ jobId, onSuccess, onCancel }) => {
    const { data: job, isLoading } = useGetJobByIdQuery(jobId, {
        skip: !jobId,
    });

    const [updateJobInvitation] = useUpdateJobInvitationMutation();

    const initialValues: JobInvitationData = {
        emailTag: job?.invitation?.emailTag || "[email]",
        nameTag: job?.invitation?.nameTag || "[name]",
        phoneTag: job?.invitation?.phoneTag || "[phone]",
        mailTemplateOption: job?.invitation?.mailTemplateOption || createDefaultInvitationTemplates().mail,
        smsTemplateOption: job?.invitation?.smsTemplateOption || createDefaultInvitationTemplates().sms,
    };

    const validationSchema = Yup.object({
        mailTemplateOption: Yup.object({
            en: Yup.string().required("English email template is required"),
            he: Yup.string().required("Hebrew email template is required"),
        }),
        smsTemplateOption: Yup.object({
            en: Yup.string().required("English SMS template is required"),
            he: Yup.string().required("Hebrew SMS template is required"),
        }),
    });

    const handleSubmit = async (values: JobInvitationData) => {
        try {
            await updateJobInvitation({
                jobId,
                invitation: values,
            }).unwrap();

            toast.success("Invitation templates updated successfully");
            onSuccess();
        } catch (error) {
            toast.error("Failed to update invitation templates");
            console.error("Error updating invitation templates:", error);
        }
    };

    if (isLoading) {
        return <LoadingScreen />;
    }

    const jobLanguage = job?.language || "en";

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
        >
            <EditInvitationFormBody onCancel={onCancel} jobLanguage={jobLanguage} />
        </Formik>
    );
};
