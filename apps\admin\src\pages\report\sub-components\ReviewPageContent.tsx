import { Skeleton, Stack } from "@mui/material";
import { LoadingScreen, BackButton, ConfirmationDialog } from "@eva/shared-ui";
import { useReviewPageContext } from "../context/ReviewPageProvider";
import { ReviewPageFamilies } from "./ReviewPageFamilies";

import React, { Suspense } from "react";
import { ReviewPageOpenQuestions } from "./ReviewPageOpenQuestions";
import { ReviewPageWarmupQuestions } from "./ReviewPageWarmupQuestions";

const ReviewPageGeneralDetails = React.lazy(() => import("./ReviewPageGeneralDetails"));

export const ReviewPageContent = () => {
    const { isLoading, isConfirmDialogOpen, onCloseConfirmationDialogClicked, onApproveExitEditModeClicked } =
        useReviewPageContext();

    return (
        <>
            {!isLoading ? (
                <>
                    <BackButton />
                    <Stack>
                        <Suspense fallback={<Skeleton width="100%" height="50px" />}>
                            <ReviewPageGeneralDetails />
                        </Suspense>
                        <ReviewPageWarmupQuestions />
                        <ReviewPageFamilies />
                        <ReviewPageOpenQuestions />
                    </Stack>
                </>
            ) : (
                <LoadingScreen />
            )}
            {isConfirmDialogOpen && (
                <Suspense fallback={null}>
                    <ConfirmationDialog
                        message="The unsaved changes will be lost, are you sure?"
                        isOpen={isConfirmDialogOpen}
                        onClose={onCloseConfirmationDialogClicked}
                        onSubmit={onApproveExitEditModeClicked}
                    />
                </Suspense>
            )}
        </>
    );
};
