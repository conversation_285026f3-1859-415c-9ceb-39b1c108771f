import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../../PDF.styles";

interface FinalScoreSectionHeaderProps {
    finalScore: number;
    isRtl: boolean;
}

export const FinalScoreSectionHeader: FC<FinalScoreSectionHeaderProps> = ({ isRtl, finalScore }) => {
    const scoreText = isRtl ? "ציון סופי" : "Final Score";
    const headerText = isRtl ? "סיכום הערכה" : "Evaluation Summary";

    return (
        <View
            style={[
                styles.paddingVertical2,
                styles.displayFlex,
                styles.flewDirectionRow,
                styles.alignStart,
                styles.fullWidth,
                styles.spaceBetween,
                styles.marginBottom6,
                isRtl ? styles.flexReverse : {},
            ]}
        >
            <Text style={isRtl ? styles.hebrewTextBoldMainHeader : styles.textBoldMainHeader}>{headerText}</Text>
            <View
                style={[
                    styles.roundedBorders,
                    styles.backgroundColorDefault,
                    styles.displayFlex,
                    styles.alignItemsCenter,
                    styles.flexGap3,
                    styles.padding8
                    
                ]}
            >
                <Text style={styles.textGlobalScore}>{finalScore.toFixed(1)}</Text>
                <Text style={isRtl ? styles.hebrewTextBoldSubTitle : styles.textBoldSubTitle}>{scoreText}</Text>
            </View>
        </View>
    );
};
