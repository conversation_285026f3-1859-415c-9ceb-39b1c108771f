import { Router, Request, Response, NextFunction } from "express";
import { UpdateCandidateGenderPreInterviewRequest, UploadCandidateImageRequest } from "@eva/shared-common";
import { getInterviewService, getLogger } from "../config/initialize";

const router = Router();
const logger = getLogger();

router.post(
    "/upload-image",
    async (req: Request<{}, {}, UploadCandidateImageRequest, {}>, res: Response, next: NextFunction) => {
        try {
            const interviewService = getInterviewService();
            await interviewService.saveCandidateImage(req.body);
            res.json({});
        } catch (error) {
            logger.error("Error uploading image");
            next(error);
        }
    }
);

router.post(
    "/candidate/:interviewId/gender",
    async (
        req: Request<{ interviewId: string }, {}, UpdateCandidateGenderPreInterviewRequest, {}>,
        res: Response,
        next: NextFunction
    ) => {
        try {
            const interviewService = getInterviewService();
            await interviewService.updateCandidateGender(req.params.interviewId, req.body.gender);
            res.json({});
        } catch (error) {
            logger.error("Error updating candidate gender");
            next(error);
        }
    }
);

export default router;
