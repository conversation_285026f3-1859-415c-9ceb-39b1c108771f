import React, { FC } from "react";
import { stylesPDF as styles } from "../../PDF.styles";
import { View } from "@react-pdf/renderer";

interface TraitProgressBarEmptySegmentProps {
    idx: number;
    segmentWidth: number;
    emptySegments: number;
    isRtl: boolean;
}

export const TraitProgressBarEmptySegment: FC<TraitProgressBarEmptySegmentProps> = ({
    emptySegments,
    idx,
    segmentWidth,
    isRtl,
}) => (
    <View
        key={`empty-${idx}`}
        style={[
            styles.segment,
            {
                width: segmentWidth,
                backgroundColor: "#E0E0E0",
            },
            idx === emptySegments - 1 ? (isRtl ? styles.leftRounded : styles.rightRounded) : {},
            idx === 0 && emptySegments === 5 ? (isRtl ? styles.rightRounded : styles.leftRounded) : {},
        ]}
    />
);
