import { Text, View } from "@react-pdf/renderer";
import { stylesPDF as styles } from "../../../../../PDF.styles";
import { LineSVGIcon } from "../../../icons/LineSVGIcon";
import { FC } from "react";
import { FinalScoreSectionPerformanceTableHeaderScoreCell } from "./FinalScoreSectionPerformanceTableHeaderScoreCell";

interface FinalScoreSectionPerformanceTableHeaderProps {
    isRtl: boolean;
}

export const FinalScoreSectionPerformanceTableHeader: FC<FinalScoreSectionPerformanceTableHeaderProps> = ({ isRtl }) => {
    const veryHighScoreText = isRtl ? "גבוה מאוד" : "Very High";
    const highScoreText = isRtl ? "גבוה" : "High";
    const averageScoreText = isRtl ? "ממוצע" : "Average";
    const lowScoreText = isRtl ? "נמוך" : "Low";
    const veryLowScoreText = isRtl ? "נמוך מאוד" : "Very Low";

    return (
        <>
            <View style={[styles.displayFlex, styles.flewDirectionRow, styles.justifyEnd, styles.alignEnd, isRtl ? styles.flexReverse : {}]}>
                <FinalScoreSectionPerformanceTableHeaderScoreCell
                    radius={isRtl ? "topRight" : "topLeft"}
                    isRtl={isRtl}
                    score={1}
                    text={veryLowScoreText}
                    width='12.5%'
                />
                <FinalScoreSectionPerformanceTableHeaderScoreCell isRtl={isRtl} score={2} text={lowScoreText} width='12.5%' />
                <FinalScoreSectionPerformanceTableHeaderScoreCell isRtl={isRtl} score={3} text={averageScoreText} width='12.5%' />
                <FinalScoreSectionPerformanceTableHeaderScoreCell isRtl={isRtl} score={4} text={highScoreText} width='12.5%' />
                <FinalScoreSectionPerformanceTableHeaderScoreCell isRtl={isRtl} score={5} text={veryHighScoreText} width='12.5%' />
                <View
                    style={[
                        styles.border,
                        styles.displayFlex,
                        styles.alignItemsCenter,
                        styles.justifyCenter,
                        { width: "6.25%", height: 30 },
                        isRtl ? styles.leftTopRounded : styles.rightTopRounded,
                    ]}
                ></View>
            </View>
        </>
    );
};
