import * as XLSX from "xlsx";
import { ParsedCandidate, REQUIRED_COLUMNS } from "./types/BulkImportTypes";

export const parseExcelFile = (
    file: File, 
    isHebrew: boolean,
    onSuccess: (candidates: ParsedCandidate[]) => void,
    onError: (error: string) => void,
    onLoadingChange: (isLoading: boolean) => void,
    jobId?: string,
    checkDuplicates?: (email: string) => Promise<boolean>,
    validateEmail?: (email: string) => Promise<{ isValid: boolean; message?: string }>
): void => {
    onLoadingChange(true);

    const reader = new FileReader();
    reader.onload = async (e) => {
        try {
            if (!e.target?.result) {
                throw new Error("Failed to read file");
            }

            const data = new Uint8Array(e.target.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: "array" });

            if (workbook.SheetNames.length === 0) {
                throw new Error("Excel file has no sheets");
            }

            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                throw new Error("No data found in Excel file");
            }

            const candidates = validateCandidates(jsonData, isHebrew);
            
            if (jobId && checkDuplicates) {
                const emailsMap = new Map<string, number[]>();
                
                candidates.forEach((candidate, index) => {
                    if (candidate.email) {
                        const normalizedEmail = candidate.email.toLowerCase().trim();
                        if (emailsMap.has(normalizedEmail)) {
                            emailsMap.get(normalizedEmail)?.push(index);
                        } else {
                            emailsMap.set(normalizedEmail, [index]);
                        }
                    }
                });
                
                emailsMap.forEach((indices) => {
                    if (indices.length > 1) {
                        for (let i = 1; i < indices.length; i++) {
                            const index = indices[i];
                            candidates[index].isDuplicate = true;
                            candidates[index].isValid = false;
                            candidates[index].errors.push("Duplicate email in the uploaded file");
                        }
                    }
                });
                
                const checkPromises = candidates
                    .filter(candidate => !candidate.isDuplicate && candidate.email)
                    .map(async (candidate, index) => {
                        try {
                            const isDuplicate = await checkDuplicates(candidate.email);
                            if (isDuplicate) {
                                candidates[index].isDuplicate = true;
                                candidates[index].isValid = false;
                                candidates[index].errors.push("Candidate with this email already exists");
                            } 
                            else if (validateEmail) {
                                const emailValidation = await validateEmail(candidate.email);
                                if (!emailValidation.isValid) {
                                    candidates[index].isDuplicate = true;
                                    candidates[index].isValid = false;
                                    candidates[index].errors.push(emailValidation.message || "Email already in use");
                                }
                            }
                        } catch (error) {
                            console.error("Error checking duplicate:", error);
                        }
                    });
                
                await Promise.all(checkPromises);
            }
            
            onSuccess(candidates);

            if (candidates.every((c) => !c.isValid)) {
                onError("None of the candidates are valid. Please check the file format.");
            }
        } catch (error) {
            console.error("Error parsing Excel file:", error);
            onError(error instanceof Error ? error.message : "Failed to parse Excel file");
        } finally {
            onLoadingChange(false);
        }
    };

    reader.onerror = () => {
        onError("Error reading file");
        onLoadingChange(false);
    };

    reader.readAsArrayBuffer(file);
};

export const validateCandidates = (jsonData: any[], isHebrew: boolean): ParsedCandidate[] => {
    return jsonData.map((row) => {
        const candidate: ParsedCandidate = {
            firstName: row.firstName || "",
            lastName: row.lastName || "",
            email: row.email || "",
            socialId: row.socialId || "",
            phoneNumber: row.phoneNumber || "",
            isValid: true,
            errors: [],
            isDuplicate: false,
        };

        for (const field of REQUIRED_COLUMNS) {
            if (!row[field]) {
                candidate.isValid = false;
                candidate.errors.push(`Missing ${field}`);
            }
        }

        if (candidate.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(candidate.email)) {
            candidate.isValid = false;
            candidate.errors.push("Invalid email format");
        }

        if (isHebrew) {
            if (candidate.socialId && !/^\d{9}$/.test(candidate.socialId)) {
                candidate.isValid = false;
                candidate.errors.push("Social ID must be 9 digits");
            }

            if (candidate.phoneNumber && !/^(05\d{8})$/.test(candidate.phoneNumber)) {
                candidate.isValid = false;  
                candidate.errors.push("Phone number must be a valid Israeli number (e.g., 05xxxxxxxx)");
            }
        }

        return candidate;
    });
};

export const createExcelTemplate = (isHebrew: boolean): void => {
    const template = [
        {
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>",
            ...(isHebrew
                ? {
                    socialId: "123456789",
                    phoneNumber: "0501234567",
                }
                : {}),
        },
    ];

    const worksheet = XLSX.utils.json_to_sheet(template);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Candidates");

    XLSX.writeFile(workbook, "candidates_template.xlsx");
}; 