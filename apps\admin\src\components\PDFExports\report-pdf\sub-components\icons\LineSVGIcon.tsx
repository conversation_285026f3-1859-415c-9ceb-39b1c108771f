import { Svg, Path } from "@react-pdf/renderer";
import { FC } from "react";

interface LineSVGIconProps {
    width: number | string;
}

export const LineSVGIcon: FC<LineSVGIconProps> = ({ width }) => {
    return (
        <Svg width={width} height="6" viewBox="0 0 557 6">
            <Path
                d="M5 2.5L0 0.113249V5.88675L5 3.5V2.5ZM552 3.5L557 5.88675V0.113249L552 2.5V3.5ZM4.5 3.5H552.5V2.5H4.5V3.5Z"
                fill="black"
            />
        </Svg>
    );
};
