import { Stack, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, Skeleton } from "@mui/material";

export interface CustomSelectOption {
    label: string;
    value: string;
}

interface CustomSelectProps {
    label: string;
    value: string;
    options: CustomSelectOption[];
    onChange: (event: SelectChangeEvent) => void;
    mapper?: (option: string) => string;
    width?: number;
    disabled?: boolean;
    loading?: boolean; 
}

export const CustomSelect = ({ disabled, width = 200, label, value, options, onChange, mapper, loading }: CustomSelectProps) => {
    return (
        <Stack width={width}>
            {loading ? (
                <Skeleton variant="rectangular" width={width} height={56} />
            ) : (
                <FormControl fullWidth>
                    <InputLabel disabled={disabled} id={`${label}-label`}>{label}</InputLabel>
                    <Select  disabled={disabled} labelId={`${label}-label`} label={label} value={value} onChange={onChange}>
                        {options.map((option) => (
                            <MenuItem key={String(option.value)} value={option.value} style={{textAlign:'right'}}>
                                <>{mapper ? mapper(option.value) : String(option.label)}</>
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            )}
        </Stack>
    );
};
