import { Gender, Language } from ".";
export type SimulationStory = {
    [key in Language]: key extends "en"
        ? {
              male: {
                  text: string;
                  videoLink?: string;
              };
              female?: {
                  text: string;
                  videoLink?: string;
              };
          }
        : {
              [key in Gender]: {
                  text: string;
                  videoLink?: string;
              };
          };
};

export interface SimulationData {
    story: SimulationStory;
    questionIds: string[];
}

