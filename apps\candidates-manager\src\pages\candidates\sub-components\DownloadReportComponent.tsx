import DownloadIcon from "@mui/icons-material/Download";
import { CircularProgress, IconButton, Tooltip } from "@mui/material";
import { FC } from "react";
import {
    useCheckIfReportExistsQuery,
    useLazyDownloadCandidateReportQuery,
} from "../../../app-state/apis/candidatesManagerApi";
interface DownloadReportComponentProps {
    candidateId: string;
    candidateName: string;
}

export const DownloadReportComponent: FC<DownloadReportComponentProps> = ({ candidateId, candidateName }) => {
    const { data: reportExists, isLoading: isCheckingIfReportExists } = useCheckIfReportExistsQuery(candidateId);
    const [downloadReport, { isLoading }] = useLazyDownloadCandidateReportQuery();
    const handleDownload = async () => {
        if (!reportExists?.interviewId) {
            return;
        }

        try {
            const response = await downloadReport(reportExists?.interviewId).unwrap();
            console.log("response", response);

            const byteCharacters = atob(response.base64Report);
            const byteArrays = [];

            for (let offset = 0; offset < byteCharacters.length; offset += 512) {
                const slice = byteCharacters.slice(offset, offset + 512);

                const byteNumbers = new Array(slice.length);
                for (let i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }

                const byteArray = new Uint8Array(byteNumbers);
                byteArrays.push(byteArray);
            }

            const blob = new Blob(byteArrays, { type: "application/pdf" });
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", `report-${candidateName}.pdf`);
            document.body.appendChild(link);
            link.click();

            link.parentNode?.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error("Error downloading report:", error);
        }
    };

    return (
        <Tooltip title={reportExists?.interviewId ? "Download Report" : "Report not available"}>
            <div>
                <IconButton
                    onClick={handleDownload}
                    disabled={isLoading || isCheckingIfReportExists || !reportExists?.interviewId}
                    size="small"
                >
                    {isLoading ? <CircularProgress size={20} color="inherit" /> : <DownloadIcon />}
                </IconButton>
            </div>
        </Tooltip>
    );
};
