import { Grid, styled, Pagination } from "@mui/material";
import { useGetAllQuestionVideosQuery } from "../../../app-state/apis";
import { H3, LoadingScreen } from "@eva/shared-ui";
import { QuestionVideoCard } from "./QuestionVideoCard";
import { useState } from "react";
import { QuestionVideo } from "@eva/shared-common";
import { QuestionsCardsListFilter } from "./QuestionsCardsListFilter";

const ITEMS_PER_PAGE = 8;

const CustomGrid = styled(Grid)({
    overflowX: "hidden",
    overflowY: "scroll",
    height: "60dvh",
});

export interface QuestionsCardsListFilterFields {
    searchText: string;
    customer: string;
    gender: string;
    trait: string;
    language: string;
}

export const QuestionsCardsList = () => {
    const { data, isLoading, error } = useGetAllQuestionVideosQuery();
    const [videos, setVideos] = useState(data?.videos ? data.videos : []);
    const [currentPage, setCurrentPage] = useState(1);

    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    const currentVideos = videos.slice(startIndex, endIndex);
    const pageCount = Math.ceil(videos.length / ITEMS_PER_PAGE);

    const handleVideosChange = (filteredVideos: QuestionVideo[]) => {
        setVideos(filteredVideos);
        setCurrentPage(1);
    };

    const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
        setCurrentPage(value);
    };

    return !isLoading && data ? (
        <>
            <QuestionsCardsListFilter questionsVideosData={data} handleVideosChange={handleVideosChange} />
            <CustomGrid container overflow="scroll" gap={4}>
                {currentVideos.length === 0 && (
                    <Grid item xs={12} sm={6} md={3}>
                        <H3>No videos found</H3>
                    </Grid>
                )}
                {currentVideos.map((video, idx) => (
                    <QuestionVideoCard
                        videoId={video.questionVideoId}
                        key={video.videoLink}
                        videoUrl={video.videoLink}
                        customerName={video.customerName}
                        trait={video.trait}
                        content={video.question}
                        gender={video.gender}
                        language={video.language}
                    />
                ))}
            </CustomGrid>
            {pageCount > 1 && (
                <Pagination
                    count={pageCount}
                    page={currentPage}
                    onChange={handlePageChange}
                    sx={{ display: "flex", justifyContent: "center", mt: 2 }}
                />
            )}
        </>
    ) : (
        <LoadingScreen />
    );
};
