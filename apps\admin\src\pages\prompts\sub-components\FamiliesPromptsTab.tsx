import { useGetPromptsPageFamiliesMetadataQuery } from "../../../app-state/apis";
import { LoadingScreen } from "@eva/shared-ui";
import { FamilyPromptsCard } from "./FamilyPromptsCard";

export const FamiliesPromptsTab = () => {
    const { data, isLoading } = useGetPromptsPageFamiliesMetadataQuery();
    return (
        <>
            {!isLoading && data ? (
                <>
                    {data.families.map((family) => (
                        <FamilyPromptsCard key={family.id} family={family} />
                    ))}
                </>
            ) : (
                <LoadingScreen />
            )}
        </>
    );
};
