import React, { FC } from "react";
import { stylesPDF as styles } from "../../../../../PDF.styles";
import { View } from "@react-pdf/renderer";
import { FinalScoreSectionPerformanceTableSegment } from "./FinalScoreSectionPerformanceTableSegment";

interface FinalScoreSectionPerformanceTableCellProps {
    score: number;
    segment: number;
    isRtl: boolean;
}

const determineFirstSegmentAlignment = (isRtl: boolean, segment: number) => {
    if (segment !== 1) return {};
    if (isRtl && segment === 1) {
        return styles.alignStart;
    } else {
        return styles.alignEnd;
    }
};

const determineLastSegmentAlignment = (isRtl: boolean, segment: number) => {
    if (segment !== 5) return {};
    if (isRtl && segment === 5) {
        return styles.alignEnd;
    } else {
        return styles.alignStart;
    }
};

export const FinalScoreSectionPerformanceTableCell: FC<FinalScoreSectionPerformanceTableCellProps> = ({ isRtl, score, segment }) => {
    return (
        <View
            style={[
                styles.border,
                styles.displayFlex,
                styles.alignItemsCenter,
                styles.flexGap4,
                styles.justifyCenter,
                { width: "12.5%", height: 30 },
                determineFirstSegmentAlignment(isRtl, segment),
                determineLastSegmentAlignment(isRtl, segment),
            ]}
        >
            <FinalScoreSectionPerformanceTableSegment isRtl={isRtl} score={score} segment={segment} />
        </View>
    );
};
