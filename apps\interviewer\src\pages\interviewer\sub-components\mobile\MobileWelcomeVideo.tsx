import talkingAvatar from "../../../../assets/animations/talking-avatar.json";
import { MobileVideoPlayer } from "../../../../components/video-player/MobileVideoPlayer";
import { useInterviewer } from "../../InterviewerProvider";
import { useLayoutEffect } from "react";
import Lottie from "lottie-react";

export const MobileWelcomeVideo = () => {
    const { interviewWelcomeVideo, onWelcomeVideoEnd } = useInterviewer();

    useLayoutEffect(() => {
        if (!interviewWelcomeVideo) {
            onWelcomeVideoEnd();
        }
    }, [interviewWelcomeVideo]);

    if (!interviewWelcomeVideo) {
        return <Lottie style={{ width: "100%" }} animationData={talkingAvatar} loop />;
    }

    return (
        <MobileVideoPlayer
            onVideoEnd={onWelcomeVideoEnd}
            controls
            data-qa="welcome-video-preview"
            videoUrl={interviewWelcomeVideo}
            width="100%"
            height="50dvh"
        />
    );
};
