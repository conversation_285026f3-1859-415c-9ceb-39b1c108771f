{"name": "@eva/drizzle", "version": "0.1.0", "private": true, "main": "index.js", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.665.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "pg": "^8.13.0", "@eva/logger": "workspace:*", "@eva/shared-common": "workspace:*"}, "devDependencies": {"@types/node": "^20.6.1", "@types/pg": "^8.10.2", "eslint": "^8.49.0", "typescript": "^5.2.2"}}