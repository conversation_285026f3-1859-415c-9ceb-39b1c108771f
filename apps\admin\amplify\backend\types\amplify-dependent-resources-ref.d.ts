export type AmplifyDependentResourcesAttributes = {
  "api": {
    "IverseAdminExpressApi": {
      "ApiId": "string",
      "ApiName": "string",
      "RootUrl": "string"
    }
  },
  "auth": {
    "iverseadminclient": {
      "AppClientID": "string",
      "AppClientIDWeb": "string",
      "IdentityPoolId": "string",
      "IdentityPoolName": "string",
      "UserPoolArn": "string",
      "UserPoolId": "string",
      "UserPoolName": "string"
    },
    "userPoolGroups": {
      "psychologistsGroupRole": "string"
    }
  },
  "function": {
    "IverseAdminExpressFunction": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    }
  },
  "storage": {
    "uploads": {
      "BucketName": "string",
      "Region": "string"
    }
  }
}