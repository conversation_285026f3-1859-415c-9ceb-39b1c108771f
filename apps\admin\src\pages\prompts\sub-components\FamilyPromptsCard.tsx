import { FamilyPromptMetadata, LanguageVariation } from "@eva/shared-common";
import { <PERSON>, CardContent, Stack, Tab, Tabs } from "@mui/material";
import { Formik } from "formik";
import { FC, useState } from "react";
import * as yup from "yup";
import { useUpdateFamilyPromptMetadataMutation } from "../../../app-state/apis";
import { H4, H5, EditableContentControls, EditableFormikTextField, useAppText } from "@eva/shared-ui";

interface FamilyPromptsCardProps {
    family: { id: string; name: LanguageVariation; metadata: FamilyPromptMetadata };
}

const validationSchema = yup.object().shape({
    hebrewCorrectionLayer: yup
        .string()
        .required("Required")
        .matches(/{score}/, "Must include {score}"),
});

export const FamilyPromptsCard: FC<FamilyPromptsCardProps> = ({ family }) => {
    const [updateFamilyPromptMetadata] = useUpdateFamilyPromptMetadataMutation();
    const { currentLanguage } = useAppText();
    const [tabValue, setTabValue] = useState(0);
    const [isEditMode, setIsEditMode] = useState(false);
    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const handleEditButtonClick = () => {
        setIsEditMode(!isEditMode);
    };

    const handleSubmit = async (values: { hebrewCorrectionLayer: string }) => {
        updateFamilyPromptMetadata({
            id: family.id,
            metadata: { ...family.metadata, he: { correctionLayerRemarks: values.hebrewCorrectionLayer } },
        });

        setIsEditMode(false);
    };

    const initialValues: { hebrewCorrectionLayer: string } = {
        hebrewCorrectionLayer: family.metadata.he.correctionLayerRemarks,
    };

    return (
        <Card>
            <CardContent>
                <H4 color='secondary'>{family.name[currentLanguage]}</H4>
                <Formik initialValues={initialValues} onSubmit={handleSubmit} validationSchema={validationSchema} validateOnChange={false}>
                    <CardContent>
                        <H5>{currentLanguage === "he" ? "שכבה מתקנת" : "Correction Layer"}</H5>
                        <Stack direction='row' justifyContent='space-between'>
                            <Tabs value={tabValue} onChange={handleTabChange}>
                                <Tab label={currentLanguage === "he" ? "עברית" : "Hebrew"} id='hebrew' />
                            </Tabs>
                            <EditableContentControls isEditMode={isEditMode} handleEditButtonClick={handleEditButtonClick} />
                        </Stack>
                        <CardContent>
                            {tabValue === 0 && (
                                <EditableFormikTextField
                                    content={family.metadata.he.correctionLayerRemarks}
                                    fieldName='hebrewCorrectionLayer'
                                    isEditing={isEditMode}
                                />
                            )}
                        </CardContent>
                    </CardContent>
                </Formik>
            </CardContent>
        </Card>
    );
};
