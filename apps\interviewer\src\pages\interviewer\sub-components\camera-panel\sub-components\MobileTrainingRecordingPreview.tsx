import { useMediaQueryContext } from "@eva/shared-ui";
import { VideoPlayer } from "../../../../../components/video-player/VideoPlayer";
import { useInterviewer } from "../../../InterviewerProvider";

export const MobileTrainingRecordingPreview = () => {
    const { trainingRecordingUrl } = useInterviewer();
    const { isMobile } = useMediaQueryContext();
    return trainingRecordingUrl && isMobile ? (
        <VideoPlayer data-qa='question-video-preview' width="100%" height="50dvh" videoUrl={trainingRecordingUrl} />
    ) : (
        <></>
    );
};
