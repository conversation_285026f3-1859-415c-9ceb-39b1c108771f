import { Family } from "@eva/shared-common";
import { Stack, styled, TextField } from "@mui/material";
import { TextAlign } from "@eva/shared-ui";

interface CustomTextFieldProps {
    textAlign?: TextAlign;
}

const CustomTextField = styled(TextField)<CustomTextFieldProps>(({ textAlign = "left" }) => ({
    "& .MuiInputBase-input": {
        fontSize: "0.875rem",
        textAlign,
    },
}));

interface ReviewPageEditModeProps {
    family: Family;
    handleFamilyChange: (family: Family) => void;
    language: string;
}

export const ReviewPageEditMode: React.FC<ReviewPageEditModeProps> = ({ family, language, handleFamilyChange }) => {
    const handleSummaryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        handleFamilyChange({ ...family, summary: event.target.value });
    };

    return (
        <Stack padding={3} gap={3} width="45%">
            <CustomTextField
                textAlign={language === "he" ? "right" : "left"}
                value={family.summary}
                onChange={handleSummaryChange}
                multiline
                fullWidth
            />
        </Stack>
    );
};
