import { useAppText } from "@eva/shared-ui";
import { AbsolutePositionWrapper } from "@eva/shared-ui";
import { useInterviewer } from "../../../../InterviewerProvider";
import { StopWatchCountdown } from "../stop-watch-countdown/StopWatchCountdown";
import { CircularStopWatch } from "./CircularStopWatch";
import { InterviewerPrimaryButton } from "../../../../../../components/buttons/InterviewerPrimaryButton";

interface RecordingControlsProps {
    isRecording: boolean;
    onStartRecordingClicked: () => void;
    onStartRecordingCountdownComplete: () => void;
    onRecordingComplete: () => void;
}

export const RecordingControls = ({
    isRecording,
    onRecordingComplete,
    onStartRecordingClicked,
    onStartRecordingCountdownComplete,
}: RecordingControlsProps) => {
    const { isInterviewOn, isTrainingOn, trainingRecordingUrl, isCountingDown } = useInterviewer();
    const { getContentForDisplay } = useAppText();

    const stopRecordingButtonContent = getContentForDisplay(
        "interviewer.cameraPreview.recordingControls.stopRecording"
    );
    const stopWatchMessage = getContentForDisplay(
        "interviewer.cameraPreview.recordingControls.recordingStopWatchMessage"
    );
    const startRecordingButtonContent = isTrainingOn
        ? trainingRecordingUrl
            ? getContentForDisplay("interviewer.cameraPreview.recordingControls.retryRecording")
            : getContentForDisplay("interviewer.cameraPreview.recordingControls.startRecording")
        : getContentForDisplay("interviewer.cameraPreview.recordingControls.startRecording");

    return (
        <>
            {isRecording && (
                <AbsolutePositionWrapper top="87%" left="50%" zIndex={1000}>
                    <InterviewerPrimaryButton
                        data-qa="stop-recording-button"
                        onClick={onRecordingComplete}
                        content={stopRecordingButtonContent}
                        timeDisabled={10000}
                    />
                </AbsolutePositionWrapper>
            )}
            {!isRecording && !isCountingDown && !isInterviewOn && isTrainingOn && (
                <AbsolutePositionWrapper top="87%" left="50%" zIndex={1000}>
                    <InterviewerPrimaryButton
                        data-qa="start-recording-button"
                        onClick={onStartRecordingClicked}
                        content={startRecordingButtonContent}
                    />
                </AbsolutePositionWrapper>
            )}
            {isCountingDown && (
                <AbsolutePositionWrapper left="50%" top="50%" zIndex={200}>
                    <CircularStopWatch
                        text={stopWatchMessage}
                        data-qa="circular-stop-watch"
                        duration={5}
                        onComplete={onStartRecordingCountdownComplete}
                    />
                </AbsolutePositionWrapper>
            )}
            {isRecording && (
                <AbsolutePositionWrapper top="87%" right="0" zIndex={1000}>
                    <StopWatchCountdown
                        data-qa="stop-watch-countdown"
                        duration={1000 * 120}
                        onComplete={onRecordingComplete}
                    />
                </AbsolutePositionWrapper>
            )}
        </>
    );
};
