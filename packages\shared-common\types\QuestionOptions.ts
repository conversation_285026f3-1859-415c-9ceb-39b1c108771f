export type QuestionsLanguageVariations = {
    en: { male: { text: string } };
    he: { male: { text: string }; female: { text: string } };
};

export type QuestionType = 'simulation' | 'general' | 'warmup' | 'open'

export type QuestionOptions = {
    order: number;
    type: QuestionType;
    options: {
        default: QuestionsLanguageVariations;
        [key: string]: QuestionsLanguageVariations;
    };
};
