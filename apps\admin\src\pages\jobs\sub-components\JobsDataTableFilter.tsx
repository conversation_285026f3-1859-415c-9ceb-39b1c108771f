import { TableJobData } from "@eva/shared-common";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { IconButton, SelectChangeEvent } from "@mui/material";
import { FC } from "react";
import { CustomSelect, CustomSelectOption, CustomTextField, DataTableFilterWrapper } from "@eva/shared-ui";
import { JobsFilterFields } from "../JobsPage";

interface JobsDataTableFilterProps {
    jobs: TableJobData[];
    filterFields: JobsFilterFields;
    handleFilterChange: (filterFields: JobsFilterFields) => void;
    resultsCount: number;
}

export const JobsDataTableFilter: FC<JobsDataTableFilterProps> = ({ filterFields, handleFilterChange, jobs, resultsCount }) => {
    const handleSearchChange = (event: React.ChangeEvent<{ value: string }>) => {
        handleFilterChange({ ...filterFields, searchText: event.target.value });
    };

    const handleCompanyChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filterFields, company: event.target.value });
    };

    const handleAccountChange = (event: SelectChangeEvent) => {
        handleFilterChange({ ...filterFields, account: event.target.value });
    };

    const uniqueCompanies = [...new Set(jobs.map((interview) => interview.companyName))];

    const companyOptions: CustomSelectOption[] = jobs
        ? [
              {
                  value: "",
                  label: "All",
              },
              ...uniqueCompanies.map((company) => ({
                  value: company,
                  label: company,
              })),
          ]
        : [];

    const uniqueAccounts = [...new Set(jobs.map((interview) => interview.accountName))];

    const accountOptions: CustomSelectOption[] = jobs
        ? [
              {
                  value: "",
                  label: "All",
              },
              ...uniqueAccounts.map((account) => ({
                  value: account,
                  label: account,
              })),
          ]
        : [];

    const handleResetFilters = () => {
        handleFilterChange({
            searchText: "",
            company: "",
            account: "",
        });
    };
    return (
        <DataTableFilterWrapper resultsCount={resultsCount}>
            <CustomTextField width={300} value={filterFields.searchText} onChange={handleSearchChange} label='Search' />
            <CustomSelect value={filterFields.company} label='Company' options={companyOptions} onChange={handleCompanyChange} />
            <CustomSelect value={filterFields.account} label='Account' options={accountOptions} onChange={handleAccountChange} />
            <IconButton onClick={handleResetFilters}>
                <RestartAltIcon />
            </IconButton>
        </DataTableFilterWrapper>
    );
};
