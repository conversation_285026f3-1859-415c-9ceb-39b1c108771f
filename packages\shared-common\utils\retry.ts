export function retry(retries: number = 3, delay: number = 1000): any {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = async function (...args: any[]) {
            for (let i = 0; i < retries; i++) {
                try {
                    return await originalMethod.apply(this, args);
                } catch (err:any) {
                    console.error(`Error in ${propertyKey}: ${err.message}`);
                    if (i < retries - 1) {
                        console.log(`Retrying in ${delay}ms...`);
                        await new Promise((res) => setTimeout(res, delay));
                    } else {
                        throw err;
                    }
                }
            }
        };

        return descriptor;
    };
}
