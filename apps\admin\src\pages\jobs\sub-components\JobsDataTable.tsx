import { GridColDef } from "@mui/x-data-grid";
import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { languageMapper } from "../../../app-state/appText";
import { formatDate, CustomDataGridClient } from "@eva/shared-ui";
import { Language, TableJobData } from "@eva/shared-common";
import { ExportJobQuestionsButton } from "../../../components/ExportJobQuestionsButton";
import { JobStatusChip } from "../../../components/JobStatusChip";

const columns: GridColDef<TableJobData>[] = [
    {
        field: "jobTitle",
        headerName: "Job Title",
        type: "string",
        width: 200,
        disableColumnMenu: true,
        renderCell: (params) => <Link to={`/jobs/builder/${params.row.id}`}>{params.value}</Link>,
    },
    {
        field: "companyName",
        headerName: "Company",
        type: "string",
        disableColumnMenu: true,
        width: 200,
    },
    {
        field: "accountName",
        headerName: "MI Account",
        type: "string",
        width: 200,
        disableColumnMenu: true,
    },
    {
        field: "language",
        headerName: "Language",
        type: "string",
        disableColumnMenu: true,
        width: 200,
        renderCell: (params) => (params.row ? languageMapper(params.row.language as Language) : ""),
    },
    {
        field: "createdAt",
        headerName: "Created At",
        type: "string",
        disableColumnMenu: true,
        width: 200,
        renderCell: (params) => {
            return params.value ? formatDate(params.value) : "";
        },
    },
    {
        field: "status",
        headerName: "Status",
        type: "string",
        disableColumnMenu: true,
        width: 200,
        renderCell: (params) => <JobStatusChip status={params.row.status} />,
    },
    {
        field: "id",
        headerName: "",
        type: "string",
        disableColumnMenu: true,
        width: 200,
        renderCell: (params) => (
            <ExportJobQuestionsButton
                jobTitle={params.row.jobTitle}
                companyName={params.row.companyName}
                createdAt={params.row.createdAt}
                jobId={params.value}
            />
        ),
    },
];

interface JobsDataTableProps {
    jobs: TableJobData[];
}

export const JobsDataTable: FC<JobsDataTableProps> = ({ jobs }) => {
    console.log(jobs);
    return <CustomDataGridClient tableName="jobs-table" defaultSortField="createdAt" rows={jobs} columns={columns} />;
};
