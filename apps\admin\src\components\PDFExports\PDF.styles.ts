import { Font, StyleSheet } from "@react-pdf/renderer";
import poppinsRegular from "../../assets/fonts/Poppins-Regular.ttf";
import poppinsBold from "../../assets/fonts/Poppins-Bold.ttf";
import poppinsMedium from "../../assets/fonts/Poppins-Medium.ttf";
import poppinsSemiBold from "../../assets/fonts/Poppins-SemiBold.ttf";
import rubikRegular from "../../assets/fonts/Rubik-Regular.ttf";
import rubikBold from "../../assets/fonts/Rubik-Bold.ttf";
import rubikMedium from "../../assets/fonts/Rubik-Medium.ttf";
import rubikSemiBold from "../../assets/fonts/Rubik-SemiBold.ttf";
import { basicTheme } from "@eva/shared-ui";

Font.register({
  family: "Poppins",
  fonts: [
    { src: poppinsRegular, fontWeight: 400 },
    { src: poppinsBold, fontWeight: 700 },
    { src: poppinsMedium, fontWeight: 500 },
    { src: poppinsSemiBold, fontWeight: 600 },
  ],
});

Font.register({
  family: "Rubik",
  fonts: [
    { src: rubikRegular, fontWeight: 400 },
    { src: rubikBold, fontWeight: 700 },
    { src: rubikMedium, fontWeight: 500 },
    { src: rubikSemiBold, fontWeight: 600 },
  ],
});

export const stylesPDF = StyleSheet.create({
  page: {
    padding: "6px",
    backgroundColor: basicTheme.palette.background.default,
    gap: "6px",
  },
  rtlTextContainer: {
    flexDirection: "row",
    textAlign: "right",
  },
  reportFooterContainer: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 14,
    paddingVertical: 10,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    position: "absolute",
    bottom: 0,
    width: "100vw",
  },
  reportFooterPagingContainer: {
    position: "absolute",
    top: "75%",
    left: "50%",
  },
  sectionContainer: {
    backgroundColor: "#FFFFFF",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
    paddingTop: "10px",
    paddingBottom: "10px",
    paddingHorizontal: "12px",
    borderRadius: 5,
  },
  polygonContainer: {
    backgroundColor: basicTheme.palette.background.paper,
    borderRadius: 5,
    paddingTop: 8,
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
  },
  familySectionHeaderContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  familySectionHeaderScoreContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-end",
    gap: 2,
  },
  familySectionTitleContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "6px",
  },
  familySectionContentContainer: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    gap: "12px",
  },
  familySectionContentTextContainer: {
    maxWidth: "100%",
    gap: "8px",
  },
  familySectionScoresContainer: {
    borderRadius: 5,
    width: "100%",
  },
  barContainer: {
    display: "flex",
    flexDirection: "row",
    height: 8,
    gap: 1,
  },
  segment: {
    height: "100%",
  },
  roundedBorders: {
    borderRadius: 5,
  },
  leftRounded: {
    borderTopLeftRadius: 5,
    borderBottomLeftRadius: 5,
  },
  rightRounded: {
    borderTopRightRadius: 5,
    borderBottomRightRadius: 5,
  },
  topRounded: {
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
  },
  bottomRounded: {
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
  },
  leftTopRounded: {
    borderTopLeftRadius: 5,
  },
  rightTopRounded: {
    borderTopRightRadius: 5,
  },
  leftBottomRounded: {
    borderBottomLeftRadius: 5,
  },
  rightBottomRounded: {
    borderBottomRightRadius: 5,
  },
  displayFlex: {
    display: "flex",
  },

  flexReverse: {
    flexDirection: "row-reverse",
  },
  spaceBetween: {
    justifyContent: "space-between",
  },
  justifyStart: {
    justifyContent: "flex-start",
  },
  justifyEnd: {
    justifyContent: "flex-end",
  },
  justifyCenter: {
    justifyContent: "center",
  },
  alignItemsCenter: {
    alignItems: "center",
  },
  alignEnd: {
    alignItems: "flex-end",
  },
  alignStart: {
    alignItems: "flex-start",
  },
  flewDirectionRow: {
    flexDirection: "row",
  },
  flexDirectionColumn: {
    flexDirection: "column",
  },
  fullWidth: {
    width: "100%",
  },
  flexGap1: {
    gap: "1px",
  },
  flexGap2: {
    gap: "2px",
  },
  flexGap3: {
    gap: "3px",
  },
  flexGap4: {
    gap: "4px",
  },
  flexGap6: {
    gap: "6px",
  },
  flexGap8: {
    gap: "8px",
  },
  flexGap10: {
    gap: "12px",
  },
  flexGap12: {
    gap: "12px",
  },
  textGlobalScore: {
    fontFamily: "Poppins",
    fontSize: "16px",
    fontWeight: 700,
  },
  textBoldMainHeader: {
    fontFamily: "Poppins",
    fontSize: "17px",
    fontWeight: 700,
  },
  hebrewTextBoldMainHeader: {
    fontFamily: "Rubik",
    fontSize: "17px",
    fontWeight: 700,
  },
  textBoldHeader: {
    fontFamily: "Poppins",
    fontSize: 11,
    fontWeight: 600,
  },
  hebrewTextBoldHeaderExplanation: {
    fontFamily: "Rubik",
    fontSize: "13px",
    fontWeight: 600,
    direction: "rtl",
  },
  hebrewTextBoldHeader: {
    fontFamily: "Rubik",
    fontSize: "13px",
    fontWeight: 600,
  },
  textBoldHeader2: {
    fontFamily: "Poppins",
    fontSize: 10,
    fontWeight: 600,
  },
  hebrewTextBoldHeader2: {
    fontFamily: "Rubik",
    fontSize: 10,
    fontWeight: 600,
  },
  textBoldScore: {
    fontFamily: "Poppins",
    fontSize: "10px",
    fontWeight: 600,
  },
  hebrewTextBoldScore: {
    fontFamily: "Rubik",
    fontSize: 10,
    fontWeight: 600,
  },
  textContent: {
    fontFamily: "Poppins",
    fontSize: "9px",
    fontWeight: 400,
  },
  textBoldContent: {
    fontFamily: "Poppins",
    fontSize: "9px",
    fontWeight: 600,
  },
  hebrewTextContent: {
    fontFamily: "Rubik",
    fontSize: "10px",
    fontWeight: 400,
    direction: "rtl",
  },
  hebrewTextBoldContent: {
    fontFamily: "Rubik",
    fontSize: "10px",
    fontWeight: 600,
    direction: "rtl",
  },
  familyOverallScoreText: {
    fontFamily: "Poppins",
    fontSize: "6px",
    fontWeight: 400,
  },
  hebrewFamilyOverallScoreText: {
    fontFamily: "Rubik",
    fontSize: "6px",
    fontWeight: 400,
  },
  textSubTitle: {
    fontFamily: "Poppins",
    fontSize: "8px",
    fontWeight: 400,
  },
  hebrewTextSubTitle: {
    fontFamily: "Rubik",
    fontSize: "9px",
    fontWeight: 400,
  },
  textBoldSubTitle: {
    fontFamily: "Poppins",
    fontSize: "9px",
    fontWeight: 600,
  },
  hebrewTextBoldSubTitle: {
    fontFamily: "Rubik",
    fontSize: "10px",
    fontWeight: 600,
  },
  textProgressBar: {
    fontFamily: "Poppins",
    fontSize: "9px",
    fontWeight: 500,
  },
  hebrewTextProgressBar: {
    fontFamily: "Rubik",
    fontSize: "10px",
    fontWeight: 500,
  },
  hebrewTextAdditionalQuestions: {
    fontFamily: "Rubik",
    fontSize: "10px",
    fontWeight: 500,
    direction: "rtl",
  },
  textFamilyName: {
    fontFamily: "Poppins",
    fontSize: 14,
    fontWeight: 600,
  },
  hebrewTextFamilyName: {
    fontFamily: "Rubik",
    fontSize: 20,
    fontWeight: 400,
  },
  footerLogoText: {
    fontFamily: "Poppins",
    fontSize: 6.5,
    fontWeight: 500,
  },
  QuestionsListText: {
    fontFamily: "Poppins",
    fontSize: 10,
    fontWeight: 400,
  },
  hebrewQuestionsListText: {
    fontFamily: "Rubik",
    fontSize: 12,
    fontWeight: 400,
    direction: "rtl",
  },

  border: {
    border: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  borderLeft: {
    borderLeft: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  borderRight: {
    borderRight: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  borderTop: {
    borderTop: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  borderBottom: {
    borderBottom: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  smallBorderRadius: {
    borderRadius: 2,
  },
  padding1: {
    padding: 1,
  },
  padding2: {
    padding: 2,
  },
  padding3: {
    padding: 3,
  },
  padding4: {
    padding: 4,
  },
  padding6: {
    padding: 6,
  },
  padding8: {
    padding: 8,
  },
  paddingHorizontal1: {
    paddingHorizontal: 1,
  },
  paddingHorizontal2: {
    paddingHorizontal: 2,
  },
  paddingHorizontal3: {
    paddingHorizontal: 3,
  },
  paddingHorizontal4: {
    paddingHorizontal: 4,
  },
  paddingHorizontal6: {
    paddingHorizontal: 6,
  },
  paddingHorizontal8: {
    paddingHorizontal: 8,
  },
  paddingHorizonta10: {
    paddingHorizontal: 10,
  },
  paddingVertical1: {
    paddingVertical: 1,
  },
  paddingVertical2: {
    paddingVertical: 2,
  },
  paddingVertical3: {
    paddingVertical: 3,
  },
  paddingVertical4: {
    paddingVertical: 4,
  },
  paddingVertical6: {
    paddingVertical: 6,
  },
  paddingVertical8: {
    paddingVertical: 8,
  },
  paddingLeft1: {
    paddingLeft: 1,
  },
  paddingLeft2: {
    paddingLeft: 2,
  },
  paddingLeft3: {
    paddingLeft: 3,
  },
  paddingLeft4: {
    paddingLeft: 4,
  },
  paddingLeft6: {
    paddingLeft: 6,
  },
  paddingLeft8: {
    paddingLeft: 8,
  },
  paddingRight1: {
    paddingRight: 1,
  },
  paddingRight2: {
    paddingRight: 2,
  },
  paddingRight3: {
    paddingRight: 3,
  },
  paddingRight4: {
    paddingRight: 4,
  },
  paddingRight6: {
    paddingRight: 6,
  },
  paddingRight8: {
    paddingRight: 8,
  },

  backgroundColorDefault: {
    backgroundColor: basicTheme.palette.background.default,
  },
  backgroundColorBlack: {
    backgroundColor: basicTheme.palette.common.black,
  },
  pageBoxContainerWidth: {
    width: 12,
    height: 12,
  },
  textColorWhite: {
    color: "#FFFFFF",
  },
  textColorBlack: {
    color: "#000000",
  },
  leftBorderLine: {
    borderLeft: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  rightBorderLine: {
    borderRight: `0.5px solid ${basicTheme.palette.grey[300]}`,
  },
  userDot: {
    width: 6,
    height: 6,
    borderRadius: 50,
    backgroundColor: basicTheme.palette.primary.main,
  },
  averageDot: {
    width: 6,
    height: 6,
    borderRadius: 50,
    backgroundColor: basicTheme.palette.secondary.main,
  },
  marginBottom1: {
    marginBottom: 1,
  },
  marginBottom2: {
    marginBottom: 2,
  },
  marginBottom3: {
    marginBottom: 3,
  },
  marginBottom4: {
    marginBottom: 4,
  },
  marginBottom6: {
    marginBottom: 6,
  },
  marginBottom8: {
    marginBottom: 8,
  },
  segmentContainer: {
    height: 12,
    backgroundColor: "#f0f0f0",
    overflow: "hidden",
  },
  segmentFill: {
    height: "100%",
  },
  imageContainer: {
    width: 80,
  },
  image: {
    borderRadius: 5,
    width: "100%",
    height: "auto",
  },
  alignTextRight: {
    textAlign: "right",
  },
  alignTextLeft: {
    textAlign: "left",
  },
  pageText: {
    fontSize: 10,
    fontFamily: "Poppins",
    color: "red",
  },
});
