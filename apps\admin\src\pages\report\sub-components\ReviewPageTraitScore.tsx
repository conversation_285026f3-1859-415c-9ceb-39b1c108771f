import { ReportVersion, Trait, TraitForDisplay } from "@eva/shared-common";
import { Box, Stack } from "@mui/material";
import React from "react";
import { useAppText } from "@eva/shared-ui";
import { ReviewPageTraitCard } from "./ReviewPageTraitCard";

interface ReviewPageTraitScoreProps {
    isEditMode: boolean;
    traits: Trait[];
    editRequest?: ReportVersion;
    traitNamesToRender: Record<string, TraitForDisplay>;
    handleTraitChange: (trait: Trait) => void;
}

export const ReviewPageTraitScores: React.FC<ReviewPageTraitScoreProps> = ({
    isEditMode,
    traits,
    editRequest,
    traitNamesToRender,
    handleTraitChange,
}) => {
    const { currentLanguage } = useAppText();
    const traitsForDisplay = isEditMode && editRequest ? editRequest.traits : traits;

    const filteredTraits = traitsForDisplay.filter((trait) => Object.keys(traitNamesToRender).includes(trait.code));

    const handleTraitScoreChange = (code: string, score: number) => {
        const trait = filteredTraits.find((trait) => trait.code === code);
        if (trait) {
            handleTraitChange({
                ...trait,
                score: score ? score : 0,
            });
        }
    };

    
    return (
        <Stack padding={3} gap={3}>
            <Stack direction='row' flexWrap='wrap' gap={3}>
                {filteredTraits.map((trait) => (
                   <ReviewPageTraitCard 
                        key={trait.code}
                        trait={trait}
                        traitName={traitNamesToRender[trait.code].trait}
                        currentLanguage={currentLanguage}
                        isEditMode={isEditMode}
                        handleTraitScoreChange={handleTraitScoreChange}
                   />
                ))}
            </Stack>
        </Stack>
    );
};
