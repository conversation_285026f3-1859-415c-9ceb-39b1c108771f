import React from "react";
import {
    Box,
    CircularProgress,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@mui/material";
import { useGetTermsAndConditionsUrlQuery } from "../app-state/store/apis/interviewApi"; // Adjust path if needed
import { CenteredModal, H6, Body1, PrimaryButton, SecondaryButton } from "@eva/shared-ui"; // Import CenteredModal and custom typography

interface TermsAndConditionsModalProps {
    open: boolean;
    onClose: () => void; 
    onAccept: () => void;
    onDecline: () => void;
}

export const TermsAndConditionsModal: React.FC<
    TermsAndConditionsModalProps
> = ({ open, onClose, onAccept, onDecline }) => {
    const {
        data,
        error,
        isLoading,
        isFetching,
    }: { data?: { termsUrl: string }; error?: any; isLoading: boolean; isFetching: boolean } = useGetTermsAndConditionsUrlQuery();

    const handleAccept = () => {
        onAccept();
    };

    const handleDecline = () => {
        onDecline();
        onClose(); 
    };

    const handleModalClose = () => {
        handleDecline();
    };

    return (
        <CenteredModal
            isOpen={open}
            onClose={handleModalClose}
            width={500}
            height="auto"
        >
            <Stack spacing={2}>
                <H6 textAlign="center">
                    Terms and Conditions
                </H6>

                {isLoading || isFetching ? (
                    <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Alert severity="error">
                        Could not load Terms and Conditions. Please try again later.
                    </Alert>
                ) : data?.termsUrl ? (
                    <Box>
                        <Box sx={{ mt: 1 }}>
                             <Body1 textAlign="center">
                                Please review the terms and conditions before starting the interview.
                            </Body1>
                        </Box>
                        <Link href={data.termsUrl} target="_blank" rel="noopener noreferrer" sx={{ display: 'block', textAlign: 'center', mt: 1 }}>
                            Download/View Terms and Conditions (Word Document)
                        </Link>
                    </Box>
                ) : (
                     <Alert severity="warning">
                        Terms and Conditions URL not found.
                    </Alert>
                )}

                <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 3 }}>
                    <SecondaryButton
                        variant="outlined"
                        content="Decline"
                        onClick={handleDecline}
                        disabled={isLoading || isFetching || !!error || !data?.termsUrl}
                        width="auto"
                    />
                    <PrimaryButton
                        content="Accept"
                        onClick={handleAccept}
                        disabled={isLoading || isFetching || !!error || !data?.termsUrl}
                        width="auto"
                    />
                </Stack>
            </Stack>
        </CenteredModal>
    );
}; 