import styled from "@emotion/styled";
import { Card } from "@mui/material";
import React from "react";

const BorderlessCardContainer = styled(Card)({
    border: "none",
    overflow:"auto",
    boxShadow: "none",
});

interface BorderlessCardProps {
    border?: boolean;
    children: React.ReactNode;
}

export const BorderlessCard = ({ children }: BorderlessCardProps) => {
    return <BorderlessCardContainer>{children}</BorderlessCardContainer>;
};
