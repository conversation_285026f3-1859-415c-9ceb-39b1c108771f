import talkingAvatar from "../../../../assets/animations/talking-avatar.json";
import { DesktopVideoPlayer } from "../../../../components/video-player/DesktopVideoPlayer";
import { useInterviewer } from "../../InterviewerProvider";
import { useLayoutEffect } from "react";
import Lottie from "lottie-react";

export const DesktopWelcomeVideo = () => {
    const { interviewWelcomeVideo, onWelcomeVideoEnd } = useInterviewer();

    useLayoutEffect(() => {
        if (!interviewWelcomeVideo) {
            onWelcomeVideoEnd();
        }
    }, [interviewWelcomeVideo]);

    if (!interviewWelcomeVideo) {
        return <Lottie style={{ width: "450px" }} animationData={talkingAvatar} loop />;
    }

    return (
        <DesktopVideoPlayer
            onVideoEnd={onWelcomeVideoEnd}
            controls
            data-qa="welcome-video-preview"
            videoUrl={interviewWelcomeVideo}
            width="500px"
            height="50dvh"
        />
    );
}; 