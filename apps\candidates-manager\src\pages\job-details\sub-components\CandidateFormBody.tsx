import { CreateCandidateRequest } from "@eva/shared-common";
import { Box, Button, CircularProgress, Stack, styled } from "@mui/material";
import { useFormikContext } from "formik";
import React from "react";
import { H3 } from "@eva/shared-ui";
import { FormikTextField } from "../../../components/FormikFields";

const StyledBox = styled(Box)({
    display: "flex",
    justifyContent: "flex-end",
    gap: 2,
    marginTop: 2,
});

interface CandidateFormBodyProps {
    isHebrew: boolean;
    onCancel: () => void;
    isLoading?: boolean;
}

export const CandidateFormBody: React.FC<CandidateFormBodyProps> = ({ isHebrew, onCancel, isLoading = false }) => {
    const { isSubmitting, submitForm } = useFormikContext<CreateCandidateRequest>();

    return (
        <Stack gap={6}>
            <H3>Candidate Details</H3>
            <FormikTextField name="firstName" label="First Name" required />
            <FormikTextField name="lastName" label="Last Name" required />
            <FormikTextField name="email" label="Email" type="email" required />
            {isHebrew && (
                <>
                    <FormikTextField name="socialId" label="Social ID" maxLength={9} type="number" />
                    <FormikTextField name="phoneNumber" label="Phone Number" type="tel" />
                </>
            )}
            <StyledBox>
                <Button
                    type="button"
                    onClick={onCancel}
                    variant="outlined"
                    color="primary"
                    sx={{ marginRight: 2 }}
                    disabled={isSubmitting || isLoading}
                >
                    {isHebrew ? "ביטול" : "Cancel"}
                </Button>
                <Button type="submit" variant="contained" color="primary" disabled={isSubmitting || isLoading}>
                    {isSubmitting || isLoading ? <CircularProgress size={24} color="inherit" /> : "Save"}
                </Button>
            </StyledBox>
        </Stack>
    );
};
