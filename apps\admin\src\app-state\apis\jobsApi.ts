import { createApi } from "@reduxjs/toolkit/query/react";
import {
    JobsPageTablaDataResponse,
    JobDetailsDataResponse,
    CreateJobRequest,
    UpdateJobRequest,
    ActionStatus,
    JobQuestionsForExportResponse,
    FamiliesWithQuestionsForBuilderResponse,
    BuilderSimulationQuestionsResponse,
} from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const jobsApi = createApi({
    reducerPath: "jobsApi",
    baseQuery,
    tagTypes: ["JobsTableData", "JobDetailsData", "FamiliesWithTraitsAndQuestions"],
    endpoints: (builder) => ({
        getJobsPageTableData: builder.query<JobsPageTablaDataResponse, void>({
            query: () => ({ url: "/job-page-table-data", method: "GET" }),
            providesTags: ["JobsTableData"],
        }),
        getJob: builder.query<JobDetailsDataResponse, string | undefined>({
            query: (jobId) => ({
                url: `/jobs/${jobId}`,
                method: "GET",
            }),
            providesTags: ["JobDetailsData"],
        }),
        updateJob: builder.mutation<ActionStatus, UpdateJobRequest>({
            query: (jobData) => ({
                url: "/update-job",
                method: "POST",
                body: jobData,
            }),
            invalidatesTags: ["JobsTableData", "JobDetailsData"],
        }),
        validateMyInterviewJobId: builder.query<{ isValid: boolean }, { myInterviewJobId: string; accountId: string }>({
            query: ({ myInterviewJobId, accountId }) => ({
                url: `/validate-my-interview-job-id/${myInterviewJobId}/${accountId}`,
                method: "GET",
            }),
        }),
        getJobQuestionsForExport: builder.query<JobQuestionsForExportResponse, string>({
            query: (jobId) => ({
                url: `/export-job-questions/${jobId}`,
                method: "GET",
            }),
        }),
        getFamiliesWithTraitsAndQuestions: builder.query<FamiliesWithQuestionsForBuilderResponse, void>({
            query: () => ({
                url: "/families-traits-questions",
                method: "GET",
            }),
            providesTags: ["FamiliesWithTraitsAndQuestions"],
        }),
        getSimulations: builder.query<BuilderSimulationQuestionsResponse, void>({
            query: () => ({
                url: "/builder-simulations",
                method: "GET",
            }),
        }),
    }),
});

export const {
    useGetJobsPageTableDataQuery,
    useGetJobQuery,
    useUpdateJobMutation,
    useLazyValidateMyInterviewJobIdQuery,
    useLazyGetJobQuestionsForExportQuery,
    useGetFamiliesWithTraitsAndQuestionsQuery,
    useGetSimulationsQuery,
} = jobsApi;
