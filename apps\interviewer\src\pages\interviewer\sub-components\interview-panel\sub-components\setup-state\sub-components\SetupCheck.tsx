import { Stack } from "@mui/material";
import { useEffect, useState } from "react";
import { SetupStatusMessage } from "./SetupStatusMessage";
import { useAppText, useMediaQueryContext } from "@eva/shared-ui";

interface SetupCheckProps {
    changeConnectionState: (connectionState: boolean) => void;
}

export const SetupCheck: React.FC<SetupCheckProps> = ({ changeConnectionState }) => {
    const { isMobile } = useMediaQueryContext();
    const [isOnline, setIsOnline] = useState<boolean>(false);
    const [hasCamera, setHasCamera] = useState<boolean>(false);
    const [hasMicrophone, setHasMicrophone] = useState<boolean>(false);
    const { getContentForDisplay } = useAppText();

    const internetInputType = getContentForDisplay("interviewer.setupState.setupCheck.internet.externalInputType");
    const internetDisabledMessage = getContentForDisplay("interviewer.setupState.setupCheck.internet.disabled");
    const internetReadyMessage = getContentForDisplay("interviewer.setupState.setupCheck.internet.ready");
    const cameraInputType = getContentForDisplay("interviewer.setupState.setupCheck.camera.externalInputType");
    const cameraDisabledMessage = getContentForDisplay("interviewer.setupState.setupCheck.camera.disabled");
    const cameraReadyMessage = getContentForDisplay("interviewer.setupState.setupCheck.camera.ready");
    const microphoneInputType = getContentForDisplay("interviewer.setupState.setupCheck.microphone.externalInputType");
    const microphoneDisabledMessage = getContentForDisplay("interviewer.setupState.setupCheck.microphone.disabled");
    const microphoneReadyMessage = getContentForDisplay("interviewer.setupState.setupCheck.microphone.ready");

    useEffect(() => {
        const checkInternetConnection = () => {
            setIsOnline(navigator.onLine);
        };

        const checkCamera = async () => {
            try {
                const mediaStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                });
                mediaStream.getTracks().forEach((track) => track.stop());
                setHasCamera(true);
            } catch (error) {
                setHasCamera(false);
            }
        };

        const checkMicrophone = async () => {
            try {
                const mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: true,
                });
                mediaStream.getTracks().forEach((track) => track.stop());
                setHasMicrophone(true);
            } catch (error) {
                setHasMicrophone(false);
            }
        };

        window.addEventListener("online", checkInternetConnection);
        window.addEventListener("offline", checkInternetConnection);

        checkInternetConnection();
        checkCamera();
        checkMicrophone();

        return () => {
            window.removeEventListener("online", checkInternetConnection);
            window.removeEventListener("offline", checkInternetConnection);
        };
    }, []);

    useEffect(() => {
        if (isOnline && hasCamera && hasMicrophone) {
            changeConnectionState(true);
        } else {
            changeConnectionState(false);
        }
    }, [isOnline, hasCamera, hasMicrophone]);

    return (
        <Stack direction='column' gap={isMobile ? "24px" : "16px"} alignItems='start'>
            <SetupStatusMessage
                externalInputType={internetInputType}
                disabledMessage={internetDisabledMessage}
                readyMessage={internetReadyMessage}
                status={isOnline}
            />
            <SetupStatusMessage
                externalInputType={cameraInputType}
                disabledMessage={cameraDisabledMessage}
                readyMessage={cameraReadyMessage}
                status={hasCamera}
            />
            <SetupStatusMessage
                externalInputType={microphoneInputType}
                status={hasMicrophone}
                disabledMessage={microphoneDisabledMessage}
                readyMessage={microphoneReadyMessage}
            />
        </Stack>
    );
};
