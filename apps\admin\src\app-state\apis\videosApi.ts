import { createApi } from "@reduxjs/toolkit/query/react";
import { 
    UploadVideoRequest,
    QuestionVideosResponse,
    IntroVideosResponse,
    QuestionVideosForBuilderResponse
} from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const videosApi = createApi({
    reducerPath: "videosApi",
    baseQuery,
    tagTypes: ["QuestionVideos", "IntroVideos", "AnswersVideosSignedUrls"],
    endpoints: (builder) => ({
        getSignedUrl: builder.query<{ url: string }, { url: string; expiration: number }>({
            query: ({ expiration, url }) => ({
                url: `/signed-url`,
                method: "POST",
                body: { url, expiration },
            }),
            providesTags: ["AnswersVideosSignedUrls"],
        }),
        uploadVideo: builder.mutation<void, UploadVideoRequest>({
            query: (body) => ({
                url: "/upload-video",
                method: "POST",
                body,
            }),
            invalidatesTags: ["QuestionVideos", "IntroVideos"],
        }),
        bulkUploadVideos: builder.mutation<{successful: number; failed: number}, {videos: UploadVideoRequest[]}>({
            query: (body) => ({
                url: "/bulk-upload-videos",
                method: "POST",
                body,
            }),
            invalidatesTags: ["QuestionVideos", "IntroVideos"],
        }),
        getAllQuestionVideos: builder.query<QuestionVideosResponse, void>({
            query: () => ({
                url: "/question-videos",
                method: "GET",
            }),
            providesTags: ["QuestionVideos"],
        }),
        getAllIntroVideos: builder.query<IntroVideosResponse, void>({
            query: () => ({
                url: "/intro-videos",
                method: "GET",
            }),
            providesTags: ["IntroVideos"],
        }),
        getIntroVideosByCustomerId: builder.query<IntroVideosResponse, string>({
            query: (customerId) => ({
                url: `/intro-videos/${customerId}`,
                method: "GET",
            }),
        }),
        getQuestionVideosByQuestionId: builder.query<QuestionVideosForBuilderResponse, { customerId: string; questionId: string }>({
            query: ({ customerId, questionId }) => ({
                url: `/question-videos/${customerId}/${questionId}`,
                method: "GET",
            }),
        }),
        validateVideo: builder.query<{ isValid: boolean }, UploadVideoRequest>({
            query: (uploadVideoRequest) => ({
                url: `/validate-video`,
                method: "POST",
                body: uploadVideoRequest,
            }),
        }),
        deleteIntoVideo: builder.mutation<void, string>({
            query: (videoId) => ({
                url: `/intro-videos/${videoId}`,
                method: "DELETE",
            }),
            onQueryStarted: async (videoId, { dispatch, queryFulfilled }) => {
                const patchResult = dispatch(
                    videosApi.util.updateQueryData("getAllIntroVideos", undefined, (draft) => {
                        const index = draft.videos.findIndex((video) => video.id === videoId);
                        if (index !== -1) {
                            draft.videos.splice(index, 1);
                        }
                    })
                );
                try {
                    await queryFulfilled;
                } catch {
                    patchResult.undo();
                }
            },
        }),
        deleteQuestionVideo: builder.mutation<void, string>({
            query: (videoId) => ({
                url: `/question-videos/${videoId}`,
                method: "DELETE",
            }),
            onQueryStarted: async (videoId, { dispatch, queryFulfilled }) => {
                const patchResult = dispatch(
                    videosApi.util.updateQueryData("getAllQuestionVideos", undefined, (draft) => {
                        const index = draft.videos.findIndex((video) => video.questionVideoId === videoId);
                        if (index !== -1) {
                            draft.videos.splice(index, 1);
                        }
                    })
                );
                try {
                    await queryFulfilled;
                } catch {
                    patchResult.undo();
                }
            },
        }),
    }),
});

export const {
    useGetSignedUrlQuery,
    useLazyGetSignedUrlQuery,
    useUploadVideoMutation,
    useBulkUploadVideosMutation,
    useGetAllQuestionVideosQuery,
    useGetAllIntroVideosQuery,
    useGetIntroVideosByCustomerIdQuery,
    useGetQuestionVideosByQuestionIdQuery,
    useLazyValidateVideoQuery,
    useDeleteIntoVideoMutation,
    useDeleteQuestionVideoMutation
} = videosApi; 