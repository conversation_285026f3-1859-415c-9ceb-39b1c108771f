import { CircularProgress, Stack } from "@mui/material";
import { useAppText, useMediaQueryContext, H4 } from "@eva/shared-ui";
import { InterviewPanelSideLayout } from "../../Interviewer.styles";
import { useInterviewer } from "../../InterviewerProvider";
import { DesktopWelcomeVideo } from "../desktop/DesktopWelcomeVideo";
import { InterviewStateMessage } from "./sub-components/InterviewStateMessage";
import { SetupState } from "./sub-components/setup-state/SetupState";
import { TrainingQuestionPreview } from "./sub-components/training-questions/TrainingQuestionPreview";
import { TrainingQuestionsControls } from "./sub-components/training-questions/TrainingQuestionsControls";
import { InterviewStepper } from "./sub-components/stepper/InterviewStepper";
import { motion, AnimatePresence } from "framer-motion";

const toRenderWelcomeVideo = (isInterviewOn: boolean, isTrainingOn: boolean, isInterviewDone: boolean): boolean => {
    if (isInterviewOn || isTrainingOn || isInterviewDone) return false;
    else return true;
};

const toRenderSetupState = (isInterviewOn: boolean, isTrainingOn: boolean, hasSetup: boolean): boolean => {
    return (!isInterviewOn && !isTrainingOn) || !hasSetup;
};

const fadeInUpAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4 },
};

export const InterviewPanel = () => {
    const { isMobile } = useMediaQueryContext();
    const { getContentForDisplay } = useAppText();
    const { currentStep, isUploading, isError, isInterviewOn, isTrainingOn, hasSetup, isSubmittingInterview } =
        useInterviewer();

    const isInterviewDone = currentStep === null;

    const uploadingVideoText = getContentForDisplay("interviewer.uploadingMessage");
    const submittingInterviewText = getContentForDisplay("interviewer.submittingInterviewMessage");
    const isWelcomeVideo = toRenderWelcomeVideo(isInterviewOn, isTrainingOn, isInterviewDone);
    const isSetupState = toRenderSetupState(isInterviewOn, isTrainingOn, hasSetup);

    return !isError ? (
        <InterviewPanelSideLayout item xs={5}>
            <>
                <AnimatePresence mode="wait">
                    {isSubmittingInterview || isUploading ? (
                        <motion.div key="loading" {...fadeInUpAnimation}>
                            <Stack
                                height={isMobile ? "50dvh" : "60dvh"}
                                justifyContent="center"
                                alignItems="center"
                                gap={8}
                                paddingInline={35}
                            >
                                <CircularProgress />
                                {isUploading ? <H4>{uploadingVideoText}</H4> : <H4>{submittingInterviewText}</H4>}
                            </Stack>
                        </motion.div>
                    ) : (
                        <motion.div key="content" {...fadeInUpAnimation}>
                            <Stack
                                height={isMobile ? "50dvh" : "60dvh"}
                                justifyContent="center"
                                alignItems="center"
                                gap={8}
                            >
                                <AnimatePresence mode="wait">
                                    {isInterviewDone ? (
                                        <motion.div key="done" {...fadeInUpAnimation}>
                                            <InterviewStateMessage />
                                        </motion.div>
                                    ) : (
                                        <motion.div key="active" {...fadeInUpAnimation}>
                                            <>
                                                {isWelcomeVideo && !isMobile && (
                                                    <motion.div key="welcome" {...fadeInUpAnimation}>
                                                        <DesktopWelcomeVideo />
                                                    </motion.div>
                                                )}
                                                {isTrainingOn && (
                                                    <motion.div key="training" {...fadeInUpAnimation}>
                                                        <TrainingQuestionPreview />
                                                    </motion.div>
                                                )}
                                                {isInterviewOn && (
                                                    <motion.div key="interview" {...fadeInUpAnimation}>
                                                        <InterviewStepper />
                                                    </motion.div>
                                                )}
                                                {isSetupState && (
                                                    <motion.div key="setup" {...fadeInUpAnimation}>
                                                        <SetupState />
                                                    </motion.div>
                                                )}
                                                {isTrainingOn && (
                                                    <motion.div key="training-controls" {...fadeInUpAnimation}>
                                                        <TrainingQuestionsControls />
                                                    </motion.div>
                                                )}
                                            </>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </Stack>
                        </motion.div>
                    )}
                </AnimatePresence>
            </>
        </InterviewPanelSideLayout>
    ) : (
        <></>
    );
};

export default InterviewPanel;
