import { useNavigate, useParams } from "react-router";
import { useSetupPilatInterviewQuery } from "../../app-state/store/apis/interviewApi";
import { BackdropLoading } from "@eva/shared-ui";
import BadRequestPage from "../BadRequestPage";
import { useEffect } from "react";

const PilatSetupPage = () => {
    const { jobId, pilatInterviewId } = useParams();
    const navigate = useNavigate();
    const { data, isLoading, isError } = useSetupPilatInterviewQuery(
        { jobId: jobId!, pilatInterviewId: pilatInterviewId! },
        {
            skip: !jobId || !pilatInterviewId,
        }
    );

    useEffect(() => {
        if (data) {
            navigate(`/interviewer/${data.interviewId}`);
        }
    }, [data, navigate]);

    if (isError) return <BadRequestPage />;

    return <BackdropLoading isLoading={isLoading} />;
};

export default PilatSetupPage;
