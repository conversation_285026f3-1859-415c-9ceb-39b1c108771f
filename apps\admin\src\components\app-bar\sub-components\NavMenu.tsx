import * as React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import MenuIcon from "@mui/icons-material/Menu";
import Button from "@mui/material/Button";
import { H3 } from "@eva/shared-ui";
import { styled } from "@mui/material/styles";
import { useState } from "react";

interface NavBarButtonProps {
    selected: boolean;
}

const NavBarButton = styled(Button)<NavBarButtonProps>(({ theme, selected }) => ({
    my: 2,
    display: "block",
    color:theme.palette.common.black,
    borderBottom: selected ? `1px solid ${theme.palette.text.secondary}` : "none",
    borderRadius: 0,
}));

interface NavBarPage {
    name: string;
    path: string;
}

interface NavBarProps {
    pages: NavBarPage[];
}

export const NavMenu: React.FC<NavBarProps> = ({ pages }) => {
    const [anchorElNav, setAnchorElNav] = React.useState<null | HTMLElement>(null);
    const navigate = useNavigate();
    const location = useLocation(); // Get current path

    const handleOpenNavMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElNav(event.currentTarget);
    };

    const handleCloseNavMenu = () => {
        setAnchorElNav(null);
    };

    const handleMenuItemClick = (path: string) => {
        navigate(path);
        handleCloseNavMenu();
    };

    return (
        <Box sx={{ flexGrow: 1 }}>
            <Box sx={{ display: { xs: "flex", md: "none" } }}>
                <IconButton
                    size='large'
                    aria-label='menu'
                    aria-controls='menu-appbar'
                    aria-haspopup='true'
                    onClick={handleOpenNavMenu}
                    color='inherit'
                >
                    <MenuIcon />
                </IconButton>
                <Menu
                    id='menu-appbar'
                    anchorEl={anchorElNav}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                    }}
                    keepMounted
                    transformOrigin={{
                        vertical: "top",
                        horizontal: "left",
                    }}
                    open={Boolean(anchorElNav)}
                    onClose={handleCloseNavMenu}
                    sx={{ display: { xs: "block", md: "none" } }}
                >
                    {pages.map((page) => (
                        <MenuItem
                            key={page.name}
                            onClick={() => handleMenuItemClick(page.path)}
                            selected={location.pathname === page.path} 
                        >
                            <H3>{page.name}</H3>
                        </MenuItem>
                    ))}
                </Menu>
            </Box>

            <Box sx={{ display: { xs: "none", md: "flex" } }}>
                {pages.map((page) => (
                    <NavBarButton
                        key={page.name}
                        onClick={() => handleMenuItemClick(page.path)}
                        selected={location.pathname === page.path}
                    >
                        {page.name}
                    </NavBarButton>
                ))}
            </Box>
        </Box>
    );
};
