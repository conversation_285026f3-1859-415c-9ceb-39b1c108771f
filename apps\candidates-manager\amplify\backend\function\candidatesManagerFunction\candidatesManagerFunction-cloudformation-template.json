{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Linux\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.14.4\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"Prefer not to answer\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"SMSInvitationSNSTopic": {"Type": "AWS::SNS::Topic", "Properties": {"TopicName": {"Fn::Join": ["", ["candidates-sms-invitation-", {"Ref": "env"}]]}, "DisplayName": {"Fn::Join": ["", ["Candidates SMS Invitation - ", {"Ref": "env"}]]}, "Tags": [{"Key": "Environment", "Value": {"Ref": "env"}}, {"Key": "Type", "Value": "SMS"}]}}, "LambdaFunction": {"DependsOn": ["LambdaExecutionPolicyCustom", "SNSExecutionPolicy", "SESExecutionPolicy"], "Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "VpcConfig": {"SecurityGroupIds": ["sg-0e5ec430bb34861cd"], "SubnetIds": ["subnet-0c7f1790620ba14cd", "subnet-0dcbafdbc0f986f26", "subnet-0d04bdd454254e45f"]}, "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "candidates<PERSON>anager<PERSON>unction", {"Fn::Join": ["", ["candidates<PERSON>anager<PERSON>unction", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "SMS_INVITATION_SNS_TOPIC_ARN": {"Ref": "SMSInvitationSNSTopic"}, "EMAIL_SENDER": "<EMAIL>", "INTERVIEW_BASE_URL": "https://staging.d347b5q9wh9bm.amplifyapp.com"}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Layers": [], "Timeout": 25, "MemorySize": 512}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "candidatesmanagerLambdaRoledac3261b", {"Fn::Join": ["", ["candidatesmanagerLambdaRoledac3261b", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "LambdaExecutionPolicyCustom": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "candidatesmanager-lambda-execution-policy-custom", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface"], "Resource": "*"}]}}}, "SESExecutionPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "candidatesmanager-ses-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail", "ses:SendTemplatedEmail", "ses:SendBulkTemplatedEmail"], "Resource": "*"}]}}}, "SNSExecutionPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "candidatesmanager-sns-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["sns:Publish"], "Resource": "*"}, {"Effect": "Allow", "Action": ["sns:CreateTopic", "sns:ListTopics", "sns:ListSubscriptionsByTopic"], "Resource": "*"}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}, {"Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject"], "Resource": ["arn:aws:s3:::interviewer-dev133751-dev/*", "arn:aws:s3:::analzyer-staging/*", "arn:aws:s3:::analyzer-prod/*"]}, {"Effect": "Allow", "Action": ["secretsmanager:GetSecretValue"], "Resource": [{"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/iverse-db/postgres-*"]]}, {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/iverse-db-*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "SMSInvitationSNSTopicArn": {"Value": {"Ref": "SMSInvitationSNSTopic"}, "Description": "ARN of the SNS topic for SMS invitations"}}}