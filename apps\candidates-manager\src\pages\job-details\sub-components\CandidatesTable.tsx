import React from "react";
import { Box, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { Body2 } from "@eva/shared-ui";
import { ParsedCandidate } from "./types/BulkImportTypes";

export interface CandidatesTableProps {
    candidates: ParsedCandidate[];
    isHebrew: boolean;
}

export const CandidatesTable: React.FC<CandidatesTableProps> = ({ candidates, isHebrew }) => (
    <Box marginTop={3}>
        <TableContainer component={Paper} style={{ maxHeight: "40vh" }}>
            <Table stickyHeader size="small">
                <TableHead>
                    <TableRow>
                        <TableCell>First Name</TableCell>
                        <TableCell>Last Name</TableCell>
                        <TableCell>Email</TableCell>
                        {isHebrew && (
                            <>
                                <TableCell>Social ID</TableCell>
                                <TableCell>Phone Number</TableCell>
                            </>
                        )}
                        <TableCell>Status</TableCell>
                        <TableCell>Errors</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {candidates.map((candidate, index) => (
                        <TableRow
                            key={index}
                            style={{
                                backgroundColor: candidate.isValid ? "inherit" : "rgba(255, 0, 0, 0.05)",
                            }}
                        >
                            <TableCell>{candidate.firstName}</TableCell>
                            <TableCell>{candidate.lastName}</TableCell>
                            <TableCell>{candidate.email}</TableCell>
                            {isHebrew && (
                                <>
                                    <TableCell>{candidate.socialId}</TableCell>
                                    <TableCell>{candidate.phoneNumber}</TableCell>
                                </>
                            )}
                            <TableCell>
                                {candidate.isValid ? (
                                    <Body2 color="success">Valid</Body2>
                                ) : (
                                    <Body2 color="error">Invalid</Body2>
                                )}
                            </TableCell>
                            <TableCell>
                                {candidate.errors.map((error: string, i: number) => (
                                    <Body2 key={i} color="error">
                                        {error}
                                    </Body2>
                                ))}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    </Box>
);
