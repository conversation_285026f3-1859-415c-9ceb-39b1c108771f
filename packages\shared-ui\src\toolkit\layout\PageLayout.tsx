import { Paper, Stack } from "@mui/material";
import { FC } from "react";

interface PageLayoutProps {
    children: React.ReactNode;
    transparent?: boolean;
}

export const PageLayout: FC<PageLayoutProps> = ({ children, transparent = false }) => {
    return transparent ? (
        <Stack gap={4} padding={4} >
            {children}
        </Stack>
    ) : (
        <Paper>
            <Stack gap={4} padding={4}>
                {children}
            </Stack>
        </Paper>
    );
};
