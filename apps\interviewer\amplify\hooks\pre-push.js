import { execSync } from 'child_process';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const amplifyLambdaPackageNames = [
    '@eva/interviewer-api-function'
];

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function prePushBuild() {
    const monorepoRoot = join(__dirname, '../../..');
    console.log(`Monorepo root determined as: ${monorepoRoot}`);

    if (amplifyLambdaPackageNames.length === 0) {
        console.log('No Amplify Lambda package names specified in hook, skipping build.');
        return;
    }

    try {
        const filters = amplifyLambdaPackageNames.map(pkgName => `--filter=${pkgName}`).join(' ');
        const turboCommand = `npx turbo run build ${filters}`;

        console.log(`Running Turborepo build from root for Amplify functions: ${turboCommand}`);
        execSync(turboCommand, { cwd: monorepoRoot, stdio: 'inherit' });

        console.log('Amplify Lambda functions built successfully via Turborepo!');

    } catch (error) {
        console.error('Error building Amplify Lambda functions via Turborepo:', error);
        process.exit(1);
    }
}

prePushBuild();