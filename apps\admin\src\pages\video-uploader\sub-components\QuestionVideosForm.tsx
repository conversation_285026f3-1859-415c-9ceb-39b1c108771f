import { CustomersResponse, FamiliesWithQuestionsForBuilderResponse, UploadVideoRequest } from "@eva/shared-common";
import { uploadData } from "aws-amplify/storage";
import { Formik, FormikHelpers, FormikProps } from "formik";
import { FC, useState } from "react";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { useBulkUploadVideosMutation, useLazyValidateVideoQuery } from "../../../app-state/apis";
import { QuestionVideosFormBody } from "./QuestionVideosFormBody";
import { v4 as uuidv4 } from "uuid";

interface QuestionVideosFormProps {
    questionsData: FamiliesWithQuestionsForBuilderResponse;
    customersData: CustomersResponse;
    isLoading: boolean;
}

export interface QuestionVideoItem {
    id: string;
    questionId: string;
    file: File | null;
    key?: string;
}

export interface QuestionVideosFormValues {
    language: string;
    customerId: string;
    gender: string;
    questionVideos: QuestionVideoItem[];
}

const QuestionVideosValidationSchema = Yup.object().shape({
    language: Yup.string().required("Language is required"),
    customerId: Yup.string().required("Customer is required"),
    gender: Yup.string().when("language", {
        is: "he",
        then: (schema) => schema.required("Gender is required when Hebrew is selected"),
        otherwise: (schema) => schema,
    }),
    questionVideos: Yup.array()
        .of(
            Yup.object().shape({
                questionId: Yup.string().required("Question is required"),
                file: Yup.mixed()
                    .required("Video file is required")
                    .test("is-file", "Must be a valid video file", (value) => {
                        return value instanceof File;
                    }),
            })
        )
        .min(1, "At least one question video is required"),
});

export const QuestionVideosForm: FC<QuestionVideosFormProps> = ({ questionsData, customersData, isLoading }) => {
    const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
    const [uploading, setUploading] = useState(false);
    const [bulkUploadVideos] = useBulkUploadVideosMutation();
    const [validateVideo] = useLazyValidateVideoQuery();

    const initialValues: QuestionVideosFormValues = {
        language: "",
        customerId: "",
        gender: "all",
        questionVideos: [
            {
                id: uuidv4(),
                questionId: "",
                file: null,
            },
        ],
    };

    const handleSubmit = async (
        values: QuestionVideosFormValues,
        { resetForm, setSubmitting }: FormikHelpers<QuestionVideosFormValues>
    ) => {
        setUploading(true);

        try {
            for (const item of values.questionVideos) {
                if (!item.file) continue;

                const videoKey = `public/uploads/questions/${values.customerId}-${values.language}-${
                    item.questionId
                }-${uuidv4()}.mp4`;

                const uploadRequest: UploadVideoRequest = {
                    type: "question",
                    customerId: values.customerId,
                    key: videoKey,
                    language: values.language,
                    questionId: item.questionId,
                    gender: values.gender,
                };

                const { isValid } = await validateVideo(uploadRequest).unwrap();
                if (!isValid) {
                    toast.error(`Video for question "${item.questionId}" is already uploaded`);
                    setSubmitting(false);
                    setUploading(false);
                    return;
                }
            }

            const uploadedKeys: Record<string, string> = {};

            for (const item of values.questionVideos) {
                if (!item.file) continue;

                const videoKey = `public/uploads/questions/${values.customerId}-${values.language}-${
                    item.questionId
                }-${uuidv4()}.mp4`;
                uploadedKeys[item.id] = videoKey;

                await uploadData({
                    path: videoKey,
                    data: item.file,
                    options: {
                        contentType: "video/mp4",
                        onProgress: (progress) => {
                            if (progress.transferredBytes && progress.totalBytes) {
                                const progressPercent = (progress.transferredBytes / progress.totalBytes) * 100;
                                setUploadProgress((prev) => ({
                                    ...prev,
                                    [item.id]: progressPercent,
                                }));
                            }
                        },
                    },
                }).result;

                setUploadProgress((prev) => ({
                    ...prev,
                    [item.id]: 100,
                }));
            }

            const videosToUpload: UploadVideoRequest[] = values.questionVideos
                .filter((item) => item.file)
                .map((item) => ({
                    type: "question",
                    customerId: values.customerId,
                    key: uploadedKeys[item.id],
                    language: values.language,
                    questionId: item.questionId,
                    gender: values.gender,
                }));

            const result = await bulkUploadVideos({ videos: videosToUpload }).unwrap();

            if (result.successful === videosToUpload.length) {
                toast.success(`Successfully uploaded ${result.successful} videos`);
                resetForm();
                setUploadProgress({});
            } else {
                toast.warning(
                    `Uploaded ${result.successful} out of ${videosToUpload.length} videos (${result.failed} failed)`
                );
            }
        } catch (error) {
            console.error("Bulk upload failed:", error);
            toast.error("Failed to upload videos. Please try again.");
        } finally {
            setSubmitting(false);
            setUploading(false);
        }
    };

    return (
        <Formik initialValues={initialValues} validationSchema={QuestionVideosValidationSchema} onSubmit={handleSubmit}>
            {(formikProps: FormikProps<QuestionVideosFormValues>) => (
                <QuestionVideosFormBody
                    questionsData={questionsData}
                    customersData={customersData}
                    isSubmitting={formikProps.isSubmitting || uploading}
                    uploadProgress={uploadProgress}
                    isLoading={isLoading}
                />
            )}
        </Formik>
    );
};
