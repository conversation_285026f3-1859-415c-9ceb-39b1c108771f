import React from 'react';
import { PageLayout } from '../toolkit/layout/PageLayout';
import { PageContainer } from '../toolkit/layout/PageContainer';
import { NoDataMessage } from '../toolkit/components';

interface NoDataPageProps {
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
}

export const NoDataPage: React.FC<NoDataPageProps> = ({ 
  message = "No data available", 
  actionLabel = "Refresh",
  onAction 
}) => {
  return (
    <PageContainer>
      <PageLayout>
        <NoDataMessage 
          message={message}
          actionLabel={actionLabel}
          onAction={onAction}
        />
      </PageLayout>
    </PageContainer>
  );
}; 