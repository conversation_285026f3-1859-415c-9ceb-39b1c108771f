
export interface QuestionVideoForCreation {
    questionId: string;
    videoLink: string;
}

export interface CreateJobRequest {
    A46: string[];
    A47: string[];
    A48: string[];
    A49: string[];
    simulations: string[];
    computedTraits: string[];
    jobId?: string;
    openQuestions: string[];
    warmupQuestions: string[];
    title: string;
    description: string;
    customerId: string;
    language: string;
    accountId: string;
    introVideo: string;
}
