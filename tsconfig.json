{
    "compilerOptions": {
      // --- Base Settings for All Packages ---
      // Target ECMAScript version for emitted JavaScript. ES2020 is a good modern baseline.
      "target": "ES2020",
      // Module system for the generated code. ESNext allows using latest ES module features.
      "module": "ESNext",
      // Specify JSX code generation (assuming React is common in the monorepo).
      "jsx": "react-jsx",
      // Module resolution strategy. 'bundler' works well with modern tools like Vite/esbuild.
      "moduleResolution": "bundler",
      // Allow importing JSON modules.
      "resolveJsonModule": true,
      // Allow default imports from modules with no default export. Requires `esModuleInterop`.
      "allowSyntheticDefaultImports": true,
      // Enables compatibility between CommonJS and ES Modules. Recommended.
      "esModuleInterop": true,
      // Ensure that Babel/esbuild can safely transpile files in isolation.
      "isolatedModules": true,
      // Standard library types to include (e.g., DOM for browser APIs).
      "lib": ["DOM", "DOM.Iterable", "ESNext"],
  
      // --- Strictness & Quality ---
      // Enable all strict type-checking options. Highly recommended.
      "strict": true,
      // Ensure that casing is correct in imports.
      "forceConsistentCasingInFileNames": true,
      // Skip type checking of declaration files (.d.ts) in node_modules. Speeds up compilation.
      "skipLibCheck": true,
  
      // --- Emit Control (Root typically doesn't emit) ---
      // Prevent the root config from emitting files itself. Packages override this if they build.
      "noEmit": true,
      // Base defaults - packages override if they need declarations/sourcemaps.
      "declaration": false,
      "sourceMap": false
    },
    // Define files/patterns to exclude globally
    "exclude": [
      "node_modules", // Always exclude node_modules
      ".turbo", // Exclude Turborepo cache directory
      "dist", // Exclude root dist folders if any
      ".*/dist" // Exclude dist folders in any subdirectory (like packages/app/dist)
    ]
  }