import { FC } from "react";
import { SoundWavesContainer } from "./SoundWaves.styles";
import MicIcon from "@mui/icons-material/Mic";

interface SoundWavesProps {
    averageValue?: number;
    top: string;
    left: string;
}

export const SoundWaves: FC<SoundWavesProps> = ({ averageValue, top, left }) => {
    return (
        <SoundWavesContainer top={top} left={left} value={averageValue}>
            <div className='outer-circle'>
                <div className='mid-circle'>
                    <div className='inner-circle'>
                        <div className='core-circle'>
                            <MicIcon  />
                        </div>
                    </div>
                </div>
            </div>
        </SoundWavesContainer>
    );
};
