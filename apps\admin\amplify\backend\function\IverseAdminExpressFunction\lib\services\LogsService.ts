import { LogsLinksRequest, LogsLinksResponse } from "@eva/shared-common";
import {
    CloudWatchLogsClient,
    StartQueryCommand,
    GetQueryResultsCommand,
    StartQueryCommandInput,
    GetQueryResultsCommandInput,
    QueryStatus,
} from "@aws-sdk/client-cloudwatch-logs";
import { Logger } from "@eva/logger";
export class LogsService {
    private readonly env: string;
    private readonly region: string;
    private readonly cloudWatchLogsClient: CloudWatchLogsClient;
    private readonly analyzerLogsGroupName: string;

    constructor(private logger: Logger) {
        this.env = process.env.ENV || "dev";
        this.region = process.env.AWS_REGION || "us-east-1";
        this.cloudWatchLogsClient = new CloudWatchLogsClient({ region: this.region });
        this.analyzerLogsGroupName = `/aws/lambda/${this.env}-analyzer`;
    }

    private getStartTime(startTimeStr: string): number {
        const parsedTime = new Date(startTimeStr).getTime();
        return parsedTime - 120000;
    }

    private async searchLogsByCorrelationId(
        logGroupName: string,
        correlationId: string,
        startTimeStr: string
    ): Promise<string[]> {
        const startTime = this.getStartTime(startTimeStr);
        const endTime = Date.now();

        const queryString = `
      fields @logStream
      | filter @message like /${correlationId}/
      | limit 100
    `;

        const startQueryInput: StartQueryCommandInput = {
            logGroupName,
            startTime,
            endTime,
            queryString,
        };

        const startQueryCommand = new StartQueryCommand(startQueryInput);
        const startQueryResponse = await this.cloudWatchLogsClient.send(startQueryCommand);

        if (!startQueryResponse.queryId) {
            throw new Error("Failed to start query");
        }

        const queryId = startQueryResponse.queryId;
        let queryStatus: QueryStatus | undefined;
        let queryResults: string[] = [];

        do {
            await new Promise((resolve) => setTimeout(resolve, 1000));

            const getQueryResultsInput: GetQueryResultsCommandInput = { queryId };
            const getQueryResultsCommand = new GetQueryResultsCommand(getQueryResultsInput);
            const getQueryResultsResponse = await this.cloudWatchLogsClient.send(getQueryResultsCommand);

            queryStatus = getQueryResultsResponse.status;
            if (queryStatus === "Complete" && getQueryResultsResponse.results) {
                queryResults = Array.from(
                    new Set(
                        getQueryResultsResponse.results
                            .flatMap((result) => result.find((field) => field.field === "@logStream")?.value)
                            .filter((value): value is string => value !== undefined)
                    )
                );
            }
        } while (queryStatus !== "Complete");

        return queryResults;
    }

    private generateLogStreamLinks(logGroupName: string, logStreamNames: string[]): string[] {
        const baseUrl = `https://${this.region}.console.aws.amazon.com/cloudwatch/home?region=${this.region}#logsV2:log-groups/log-group/`;
        const encodedLogGroupName = encodeURIComponent(logGroupName).replace(/\//g, "$252F");

        return logStreamNames.map((logStream) => {
            const encodedLogStream = encodeURIComponent(logStream)
                .replace(/\//g, "$252F")
                .replace(/\[/g, "$255B")
                .replace(/\]/g, "$255D");
            return `${baseUrl}${encodedLogGroupName}/log-events/${encodedLogStream}`;
        });
    }

    private async generateAnalyzerLinks(runId: string, createdAt: string): Promise<string[]> {
        const logStreamNames = await this.searchLogsByCorrelationId(this.analyzerLogsGroupName, runId, createdAt);
        return this.generateLogStreamLinks(this.analyzerLogsGroupName, logStreamNames);
    }

    public async getLogsLinks(logsRequest: LogsLinksRequest): Promise<LogsLinksResponse> {
        const { runId, interviewDate } = logsRequest;

        const analyzerLinks = await this.generateAnalyzerLinks(runId, interviewDate);

        return {
            analyzerLinks,
        };
    }
}
