AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  reports-handler

  <PERSON><PERSON> SAM Template for reports-handler

Parameters:
  Env:
    Type: String
    Description: The deployment environment (e.g., staging, prod)
    Default: staging 
    AllowedValues:
      - staging
      - prod

Globals:
  Function:
    Timeout: 10
    MemorySize: 256

Resources:
  ReportsHandlerFunction:
    Type: AWS::Serverless::Function 
    Properties:
      FunctionName: !Sub "${Env}-reports-handler"
      CodeUri: src/dist/
      Handler: app.lambdaHandler 
      Runtime: nodejs22.x
      Architectures:
      - x86_64
      Events:
         ReportsHandlerIntegration:
            Type: Api
            Properties:
                Path: /reports
                Method: post
                Auth:
                    ApiKeyRequired: true
      Policies:
        - Statement:
            Effect: Allow
            Action:
                - secretsmanager:GetSecretValue
            Resource:
                - !Sub arn:aws:secretsmanager:us-east-1:405424696292:secret:${Env}/iverse-db/postgres-*
                - !Sub arn:aws:secretsmanager:us-east-1:405424696292:secret:${Env}/iverse-db-*
                - arn:aws:secretsmanager:us-east-1:405424696292:secret:cloudfront_access_pair-ma60FR
     

  ServerlessRestApi:
      Type: AWS::Serverless::Api
      Properties:
        StageName: !Ref Env
        
  ApplicationResourceGroup:
    Type: AWS::ResourceGroups::Group
    Properties:
      Name:
        Fn::Sub: ApplicationInsights-SAM-${AWS::StackName}
      ResourceQuery:
        Type: CLOUDFORMATION_STACK_1_0
  ApplicationInsightsMonitoring:
    Type: AWS::ApplicationInsights::Application
    Properties:
      ResourceGroupName:
        Ref: ApplicationResourceGroup
      AutoConfigurationEnabled: 'true'
Outputs:
  ReportsHandlerApi:
    Description: API Gateway endpoint URL for Prod stage for Reports Handler function
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/reports/"
  ReportsHandlerFunction:
    Description: Reports Handler Lambda Function ARN
    Value: !GetAtt ReportsHandlerFunction.Arn
  ReportsHandlerFunctionIamRole:
    Description: Implicit IAM Role created for Reports Handler function
    Value: !GetAtt ReportsHandlerFunctionRole.Arn
