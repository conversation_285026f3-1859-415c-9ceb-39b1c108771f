{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Linux\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.13.0\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"DependsOn": ["LambdaExecutionPolicyCustom"], "Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "VpcConfig": {"SecurityGroupIds": ["sg-0e5ec430bb34861cd"], "SubnetIds": ["subnet-0c7f1790620ba14cd", "subnet-0dcbafdbc0f986f26", "subnet-0d04bdd454254e45f"]}, "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "IverseAdminExpressFunction", {"Fn::Join": ["", ["IverseAdminExpressFunction", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "ANALYZER_QUEUE_URL": {"Fn::Join": ["", ["https://sqs.us-east-1.amazonaws.com/************/", {"Ref": "env"}, "_analyzer.fifo"]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Layers": [], "Timeout": 25, "MemorySize": 512}}, "LambdaExecutionPolicyCustom": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy-custom", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface"], "Resource": "*"}]}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "iverseadminclientLambdaRole29b4ae0b", {"Fn::Join": ["", ["iverseadminclientLambdaRole29b4ae0b", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}, {"Effect": "Allow", "Action": "sqs:SendMessage", "Resource": {"Fn::Join": ["", ["arn:aws:sqs:us-east-1:************:", {"Ref": "env"}, "_analyzer.fifo"]]}}, {"Effect": "Allow", "Action": ["logs:DescribeLogGroups", "logs:DescribeLogStreams", "logs:GetLogEvents", "logs:StartQuery", "logs:GetQueryResults"], "Resource": "*"}, {"Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject"], "Resource": ["arn:aws:s3:::interviewer-dev133751-dev/*", "arn:aws:s3:::analzyer-staging/*", "arn:aws:s3:::analyzer-prod/*"]}, {"Action": ["secretsmanager:GetSecretValue"], "Resource": ["arn:aws:secretsmanager:us-east-1:************:secret:pilat/secrets-*", {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/iverse-db/postgres-*"]]}, {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/iverse-db-*"]]}, {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/iverseAdminCloudfronDist-*"]]}, {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/bucketUrlPrefixAndSuffix-*"]]}, {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:cloudfront_access_pair-ma60FR"]]}, "arn:aws:secretsmanager:us-east-1:************:secret:myInterviewCreds/*", {"Fn::Join": ["", ["arn:aws:secretsmanager:us-east-1:************:secret:", {"Ref": "env"}, "/rerunURL-*"]]}], "Effect": "Allow"}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}