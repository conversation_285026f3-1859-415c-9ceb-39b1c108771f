import { FamiliesWithQuestionsForBuilderResponse, CustomersResponse } from "@eva/shared-common";
import { Stack, Paper, SelectChangeEvent } from "@mui/material";
import { FC } from "react";
import { languageMapper, languages } from "../../../app-state/appText";
import { FileInput, PrimaryButton, H5, CustomSelect, CustomSelectOption } from "@eva/shared-ui";
import { Form, useFormikContext } from "formik";
import { WelcomeVideoFormValues } from "./WelcomeVideoForm";

interface WelcomeVideoFormBodyProps {
    questionsData: FamiliesWithQuestionsForBuilderResponse;
    customersData: CustomersResponse;   
    isSubmitting: boolean;
    uploadProgress: number;
}

export const WelcomeVideoFormBody: FC<WelcomeVideoFormBodyProps> = ({
    customersData,
    isSubmitting,
    uploadProgress,
}) => {
    const { values, setFieldValue, errors, touched, submitForm } = useFormikContext<WelcomeVideoFormValues>();

    const generateLanguageOptions = (): CustomSelectOption[] => {
        return languages.map((language) => ({
            value: language,
            label: languageMapper(language),
        }));
    };

    const generateCustomerOptions = (): CustomSelectOption[] => {
        return [
            {
                label: "All",
                value: "all",
            },
            ...customersData.customers.map((customer) => ({
                value: customer.id,
                label: customer.companyName,
            })),
        ];
    };

    const generateGenderOptions = (): CustomSelectOption[] => {
        return [
            {
                value: "all",
                label: "All",
            },
            {
                value: "male",
                label: "Male",
            },
            {
                value: "female",
                label: "Female",
            },
        ];
    };

    const handleLanguageChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("language", event.target.value);
        if (event.target.value !== "he") {
            setFieldValue("gender", "all");
        }
    };

    const handleCustomerChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("customerId", event.target.value);
    };

    const handleGenderChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("gender", event.target.value);
    };

    const handleFileSelect = (file: File | null) => {
        setFieldValue("videoFile", file);
    };

    const isUploadDisabled = (): boolean => {
        return (
            !values.videoFile ||
            !values.language ||
            !values.customerId ||
            (values.language === "he" && !values.gender) ||
            isSubmitting
        );
    };

    const uploadButtonContent = isSubmitting ? `${uploadProgress.toFixed(0)}%` : "Upload Welcome Video";

    return (
        <Form>
            <Stack gap={4}>
                <Paper elevation={1} sx={{ padding: 3 }}>
                    <Stack gap={3}>
                        <H5>Welcome Video Settings</H5>

                        <Stack direction="row" gap={2}>
                            <CustomSelect
                                label="Language"
                                options={generateLanguageOptions()}
                                value={values.language}
                                onChange={handleLanguageChange}
                                mapper={languageMapper}
                                disabled={isSubmitting}
                            />

                            <CustomSelect
                                label="Customer"
                                options={generateCustomerOptions()}
                                value={values.customerId}
                                onChange={handleCustomerChange}
                                disabled={isSubmitting}
                            />

                            {values.language === "he" && (
                                <CustomSelect
                                    label="Gender"
                                    options={generateGenderOptions()}
                                    value={values.gender}
                                    onChange={handleGenderChange}
                                    disabled={isSubmitting}
                                />
                            )}
                        </Stack>

                        <Stack direction="row" gap={3} alignItems="center">
                            <FileInput
                                onFileSelect={handleFileSelect}
                                buttonLabel="Choose Welcome Video"
                                disabled={isSubmitting}
                            />
                            {values.videoFile && <span>{values.videoFile.name}</span>}
                            {touched.videoFile && errors.videoFile && (
                                <span style={{ color: "red" }}>{errors.videoFile as string}</span>
                            )}
                        </Stack>

                        <Stack direction="row" justifyContent="flex-end">
                            <PrimaryButton
                                content={uploadButtonContent}
                                onClick={submitForm}
                                disabled={isUploadDisabled()}
                            />
                        </Stack>
                    </Stack>
                </Paper>
            </Stack>
        </Form>
    );
};
