import { useInterviewer } from "../../../../../../InterviewerProvider";
import { QuestionStepContent } from "./sub-components/QuestionStepConenet";

export const QuestionStep = () => {
    const { currentStep } = useInterviewer();

    const currentQuestion = currentStep?.question!;

    return currentQuestion ? (
        <>
            <QuestionStepContent content={currentQuestion.text} videoUrl={currentQuestion.videoLink} />
        </>
    ) : (
        <></>
    );
};
