{"api": {"IverseAdminExpressApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "IverseAdminExpressFunction"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "auth": {"iverseadminclient": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed"}, "userPoolGroups": {"dependsOn": [{"attributes": ["UserPoolId", "AppClientIDWeb", "AppClientID", "IdentityPoolId"], "category": "auth", "resourceName": "iverseadminclient"}], "providerPlugin": "awscloudformation", "service": "Cognito-UserPool-Groups"}}, "function": {"IverseAdminExpressFunction": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "hosting": {"amplifyhosting": {"providerPlugin": "awscloudformation", "service": "amplifyhosting", "type": "manual"}}, "parameters": {"AMPLIFY_function_IverseAdminExpressFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "IverseAdminExpressFunction"}]}, "AMPLIFY_function_IverseAdminExpressFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "IverseAdminExpressFunction"}]}, "AMPLIFY_hosting_amplifyhosting_appId": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}, "AMPLIFY_hosting_amplifyhosting_type": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}}, "storage": {"uploads": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3"}}}