import React, { Suspense, useEffect, useState } from "react";
import { useGetCustomersPageTableDataQuery } from "../../app-state/apis";
import { basicTheme, LoadingScreen, PageContainer, PageLayout } from "@eva/shared-ui";
import { CreateCustomerFormInputs, CustomersTableData } from "@eva/shared-common";


const CustomerCreateModal = React.lazy(() => import("./sub-components/CustomerCreateModal"));
const CustomerPageHeader = React.lazy(() => import("./sub-components/CustomersPageHeader"));
const CustomersDataTable = React.lazy(() => import("./sub-components/CustomersDataTable"));

const createInitialValuesForEdit = (customerId: string, customers: CustomersTableData[]): CreateCustomerFormInputs | null => {
    const customer = customers.find((customer) => customer.id === customerId);
    return customer
        ? {
              themeColor: customer.themeColor ? customer.themeColor :  basicTheme.palette.primary.main,
              customerName: customer.companyName,
              accountId: customer.accountId,
              customerId,
              logoFile: null,
              logoFilePath: null,
              integration: customer.integration,
          }
        : null;
};

const CustomersPage = () => {
    const { data, isLoading } = useGetCustomersPageTableDataQuery();
    const [searchText, setSearchText] = useState<string>("");
    const [filteredCustomers, setFilteredCustomers] = useState(data?.customers ? data.customers : []);
    const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
    const [isAddCustomerDialogOpen, setIsAddCustomerDialogOpen] = useState(false);



    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchText(event.target.value);
    };

    const handleAddCustomerClicked = () => {
        setIsAddCustomerDialogOpen((prev) => !prev);
        setSelectedCustomer(null);
    };

    const handleEditCustomerClicked = (customerId: string) => {
        setIsAddCustomerDialogOpen(true);
        setSelectedCustomer(customerId);
    };

    useEffect(() => {
        if (data) {
            const customers = data.customers;
            const filtered = customers.filter(
                (customer) =>
                    customer.companyName.toLowerCase().includes(searchText.toLowerCase()) ||
                    customer.accountName.toLowerCase().includes(searchText.toLowerCase())
            );
            setFilteredCustomers(filtered);
        }
    }, [data, searchText]);

    const customerForEditValues: CreateCustomerFormInputs | null =
        data?.customers && selectedCustomer ? createInitialValuesForEdit(selectedCustomer, data.customers) : null;

    return (
        <PageContainer>
            {isLoading ? (
                <LoadingScreen />
            ) : (
                <PageLayout>
                    <Suspense fallback={<LoadingScreen />}>
                        <CustomerPageHeader
                            handleAddCustomerClicked={handleAddCustomerClicked}
                            handleSearchChange={handleSearchChange}
                            searchText={searchText}
                        />
                        <CustomersDataTable handleEditCustomerClicked={handleEditCustomerClicked} customers={filteredCustomers} />
                        <CustomerCreateModal
                            customerForEditValues={customerForEditValues}
                            isOpen={isAddCustomerDialogOpen}
                            onClose={handleAddCustomerClicked}
                        />
                    </Suspense>
                </PageLayout>
            )}
        </PageContainer>
    );
};

export default CustomersPage;
