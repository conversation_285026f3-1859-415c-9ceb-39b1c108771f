import styled from "@emotion/styled";
import { Paper, Stack } from "@mui/material";
import { Authenticator } from "@aws-amplify/ui-react";
import { formFields, components } from "./CustomAuthenticatorComponets";
import { basicTheme } from "../../toolkit/theme/AppThemeProvider";

const CustomAuthenticatorContainer = styled(Stack)(() => ({
    width: "100%",
    fontFamily: basicTheme.typography.fontFamily,
    height: "100vh",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f7fa",
    "& .MuiPaper-root": {
        padding: "40px",
        width: "420px",
        backgroundColor: "white",
        borderRadius: "12px",
        boxShadow: "0 8px 24px rgba(0,0,0,0.08)",
    },
    ".amplify-input": {
        backgroundColor: "white",
        borderRadius: "8px",
        height: "42px",
        border: `1px solid ${basicTheme.palette.grey[300]}`,
        width: "100%",
        marginBottom: basicTheme.spacing(2),
        padding: "0 16px",
        fontSize: "15px",
        transition: "border-color 0.2s ease",
        "&:focus-visible": {
            border: `1.5px solid ${basicTheme.palette.primary.main}`,
            outline: "none",
        },
    },
    fieldset: {
        justifyContent: "center",
        alignItems: "center",
        border: "none",
        padding: "0",
        width: "100%",
    },
    ".amplify-label": {
        marginBottom: "6px",
        fontSize: "14px",
        fontWeight: "500",
        color: "#333",
    },
    legend: {
        display: "none",
    },
    ".amplify-field__show-password": {
        display: "none",
    },
    ".amplify-button": {
        backgroundColor: basicTheme.palette.primary.main,
        color: basicTheme.palette.common.white,
        width: "100%",
        height: "44px",
        borderRadius: "8px",
        border: "none",
        cursor: "pointer",
        marginTop: "8px",
        marginBottom: basicTheme.spacing(2),
        fontSize: "15px",
        fontWeight: "600",
        transition: "background-color 0.2s ease",
        "&:hover": {
            backgroundColor: basicTheme.palette.primary.dark,
        },
    },
    ".amplify-button[data-variation='link']": {
        backgroundColor: "transparent",
        color: basicTheme.palette.primary.main,
        "&:hover": {
            backgroundColor: "transparent",
            textDecoration: "underline",
        },
    },
    h3: {
        fontSize: "20px",
        fontWeight: "600",
        marginBottom: "24px",
        color: "#333",
    },
    circle: {
        display: "none",
    },
    ".amplify-loader": {
        display: "none",
    },
}));

export const AuthenticatorPage = () => {
    return (
        <CustomAuthenticatorContainer>
            <Paper>
                <Authenticator hideSignUp components={components} formFields={formFields} />
            </Paper>
        </CustomAuthenticatorContainer>
    );
};

