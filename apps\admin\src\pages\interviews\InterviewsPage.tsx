import { InterviewsFilterParams, InterviewsPageTableDataRequest } from "@eva/shared-common";
import { GridPaginationModel, GridSortModel } from "@mui/x-data-grid";
import { useGetInterviewsPageTableDataQuery } from "../../app-state/apis";
import { LoadingScreen, PageContainer, PageLayout, useLocalStorageState } from "@eva/shared-ui";
import { InterviewsDataTable } from "./sub-components/InterviewsDataTable";
import { InterviewsDataTableFilter } from "./sub-components/InterviewsDataTableFilter";


const defaultQuery: InterviewsPageTableDataRequest = {
    pagination: {
        page: 0,
        pageSize: 25,
    },
    sort: {
        field: "interviewDate",
        sort: "desc",
    },
    filter: {
        searchText: "",
        jobTitle: "",
        company: "",
        account: "",
        reviewStatus: "",
    },
};

const InterviewsPage = () => {
    const [queryState, setQueryState] = useLocalStorageState<InterviewsPageTableDataRequest>(
        "interviews-query-state",
        defaultQuery
    );

    const { data, isLoading, isFetching } = useGetInterviewsPageTableDataQuery(queryState, {
        pollingInterval: 20000,
    });

    const handleFilterChange = (filterFields: InterviewsFilterParams) => {
        setQueryState({
            ...queryState,
            pagination: {
                ...queryState.pagination,
                page: 0, 
            },
            filter: filterFields,
        });
    };

    const handlePaginationModelChange = (model: GridPaginationModel) => {
        setQueryState({
            ...queryState,
            pagination: {
                page: model.page,
                pageSize: model.pageSize,
            },
        });
    };

    const handleSortModelChange = (newSortModel: GridSortModel) => {


        if (newSortModel.length > 0) {
            setQueryState({
                ...queryState,
                sort: {
                    field: newSortModel[0].field,
                    sort: newSortModel[0].sort!,
                },
            });
        } else {
            setQueryState({
                ...queryState,
                sort: {
                    field: queryState.sort.field,
                    sort: queryState.sort.sort === "asc" ? "desc" : "asc",
                }
            });
        }
    };

    return (
        <PageContainer>
            {isLoading && !data ? (
                <LoadingScreen />
            ) : (
                <PageLayout>
                    <InterviewsDataTableFilter
                        filerFields={{
                            searchText: queryState.filter?.searchText || "",
                            jobTitle: queryState.filter?.jobTitle || "",
                            company: queryState.filter?.company || "",
                            account: queryState.filter?.account || "",
                            reviewStatus: queryState.filter?.reviewStatus || "",
                        }}
                        handleFilterChange={handleFilterChange}
                        resultsCount={data?.totalCount ? Number(data.totalCount) : 0}
                    />
                    <InterviewsDataTable
                        interviews={data?.interviews || []}
                        isLoading={isLoading || isFetching}
                        paginationModel={queryState.pagination}
                        onPaginationModelChange={handlePaginationModelChange}
                        sortModel={queryState.sort ? [{ field: queryState.sort.field, sort: queryState.sort.sort }] : []}
                        onSortModelChange={handleSortModelChange}
                        rowCount={data?.totalCount ? Number(data.totalCount) : 0}
                    />
                </PageLayout>
            )}
        </PageContainer>
    );
};

export default InterviewsPage;
