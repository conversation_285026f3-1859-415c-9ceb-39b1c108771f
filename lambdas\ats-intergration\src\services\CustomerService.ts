import { Logger } from '@eva/logger';
import { accounts, customers, DB } from '@eva/drizzle';
import { CreateCustomerRequest } from '../types';
import { eq } from '@eva/drizzle';

export class CustomerService {
    constructor(private db: DB, private logger: Logger) {}

    async createCustomer(createCustomerRequest: CreateCustomerRequest, accountId: string): Promise<string> {
        this.logger.info(
            `Attempting to create customer for accountId: ${accountId} with name: ${createCustomerRequest.name}`,
        );
        try {
            const account = await this.db
                .select({ name: accounts.name })
                .from(accounts)
                .where(eq(accounts.id, accountId))
                .limit(1);

            if (account.length === 0) {
                this.logger.warn(`Account not found: ${accountId}`);
                throw Object.assign(new Error('Account not found'), { statusCode: 404 });
            }

            const accountName = account[0].name;
            this.logger.info(`Found account: ${accountName}. Proceeding with customer creation.`);
            const customerCreationResponse = await this.db
                .insert(customers)
                .values({
                    companyName: createCustomerRequest.name,
                    accountId: accountId,
                    logoLink: createCustomerRequest.logo || null,
                    themeColor: createCustomerRequest.themeColor || null,
                })
                .returning({ internalCustomerId: customers.id });

            const internalCustomerId = customerCreationResponse[0].internalCustomerId;
            this.logger.info(`Successfully created customer with internalCustomerId: ${internalCustomerId}`);
            return internalCustomerId;
        } catch (error) {
            const err = error as Error;
            this.logger.error(`Database error creating customer for account ${accountId}: ${err.message}`, {
                stack: err.stack,
            });
            throw new Error('Failed to create customer');
        }
    }
}
