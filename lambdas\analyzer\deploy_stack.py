import subprocess
import os

def deploy_sam_stack():
    # --- Configuration ---
    available_envs = ["dev", "staging", "prod"]
    lambda_name = "analyzer"
    region = "us-east-1"
    monorepo_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")) 
    lambda_dir = os.path.join(monorepo_root, "lambdas", lambda_name)
    
    # --- Get User Input for Environment ---
    while True:
        env_choice = input(f"Choose environment to deploy ({', '.join(available_envs)}): ").lower()
        if env_choice in available_envs:
            break
        else:
            print(f"Invalid environment. Please choose from: {', '.join(available_envs)}")

    # --- Production Confirmation ---
    if env_choice == "prod":
        prod_confirm = input("You are about to deploy to PRODUCTION. Are you absolutely sure? (yes/no): ").lower()
        if prod_confirm != 'yes':
            print("Exiting without deploying to production.")
            return

    # --- Set Variables ---
    stack_name = f"{lambda_name}-{env_choice}"
    env_parameter = f"Env={env_choice}"
    filter_name = f"@eva/{lambda_name}"

    print(f"\n--- Deployment Details ---")
    print(f"Environment: {env_choice}")
    print(f"Stack Name: {stack_name}")
    print(f"Lambda Name: {lambda_name}")
    print(f"Region: {region}")
    print(f"Monorepo root: {monorepo_root}")
    print(f"Lambda dir: {lambda_dir}")
    print(f"Filter: {filter_name}")
    print(f"------------------------\n")

    # --- Final Confirmation ---
    deploy_confirm = input(f"Proceed with deployment to {env_choice}? (yes/no): ").lower()
    if deploy_confirm != 'yes':
        print("Exiting without deploying the stack.")
        return

    # --- Define Commands ---
    turbo_build_command = [
        "npx", "turbo", "run", "build", f"--filter={filter_name}"
    ]

    sam_build_command = ["sam", "build"]
    sam_deploy_command = [
        "sam", "deploy",
        "--stack-name", stack_name,
        "--capabilities", "CAPABILITY_IAM",
        "--parameter-overrides",
        env_parameter,
        f"Region={region}", # Pass region as parameter if needed by template
        "--no-fail-on-empty-changeset",
        "--region", region,
        "--resolve-s3",
    ]

    # --- Execute Commands ---
    try:
        print('\nRunning monorepo build for Lambda...')
        subprocess.run(turbo_build_command, check=True, cwd=monorepo_root)
        print("Monorepo build successful.")

        print('\nRunning SAM build (packaging pre-built code)...')
        subprocess.run(sam_build_command, check=True, cwd=lambda_dir)
        print("SAM Build successful.")

        print('\nDeploying the stack...')
        subprocess.run(sam_deploy_command, check=True, cwd=lambda_dir)
        print("\nDeployment successful.")

    except subprocess.CalledProcessError as e:
        print(f"\nError during build/deployment: {e}")
    except FileNotFoundError as e:
        print(f"\nError: Command not found or path incorrect - {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

# --- Run the deployment function ---
if __name__ == "__main__":
    deploy_sam_stack()
