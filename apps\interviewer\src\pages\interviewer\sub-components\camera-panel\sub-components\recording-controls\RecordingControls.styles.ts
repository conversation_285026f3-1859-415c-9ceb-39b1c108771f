import { styled } from "@mui/material";
import { basicTheme } from "@eva/shared-ui";


interface RecordingControlsContainerProps {
    isRecording: boolean;
}

export const RecordingControlsBackScreenContainer = styled(
    "div"
)<RecordingControlsContainerProps>(({ isRecording}) => ({
    backgroundColor: isRecording
        ? "rgba(255,255,255,0)"
        : "rgba(255,255,255,0.85)",
    display: isRecording ? "none" : "flex",
    zIndex: "100",
    position: "absolute",
    width: "800px",
    height: "100%",
    borderRadius: "16px",
    alignContent: "center",
    alignItems: "center",
    [basicTheme.breakpoints.down("sm")]: {
        maxHeight: "50dvh",
        width: "100%",
        borderRadius: 0,
    },
}));
