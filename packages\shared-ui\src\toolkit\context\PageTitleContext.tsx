import { ReactNode, createContext, useContext, useState } from 'react';

interface PageTitleContextType {
  subtitle: string | null;
  setSubtitle: (subtitle: string | null) => void;
}

const PageTitleContext = createContext<PageTitleContextType>({
  subtitle: null,
  setSubtitle: () => {},
});

export const usePageTitle = () => useContext(PageTitleContext);

export const PageTitleProvider = ({ children }: { children: ReactNode }) => {
  const [subtitle, setSubtitle] = useState<string | null>(null);

  return (
    <PageTitleContext.Provider value={{ subtitle, setSubtitle }}>
      {children}
    </PageTitleContext.Provider>
  );
}; 