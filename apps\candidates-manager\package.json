{"name": "@eva/candidates-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-amplify/ui-react": "^6.9.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.0.12", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.16.7", "@mui/x-data-grid": "^7.8.0", "@mui/x-date-pickers": "^7.27.3", "@reduxjs/toolkit": "^2.2.3", "aws-amplify": "^6.13.5", "formik": "^2.4.6", "node-rsa": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.0", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-toastify": "^10.0.5", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "xlsx": "^0.18.5", "yup": "^1.6.1", "@eva/shared-common": "workspace:*", "@eva/shared-ui": "workspace:*"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.6", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/stylis": "^4.2.5", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^9.0.0", "eslint-plugin-react-hooks": "^5.0.0-canary-89a6b912e-20240531", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.4.5", "vite": "^4.4.5", "vite-plugin-svgr": "^4.3.0"}}