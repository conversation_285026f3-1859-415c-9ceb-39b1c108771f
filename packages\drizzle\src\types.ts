import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { GradingPrinciplesMap, LanguageVariation, TraitSummariesMap } from '@eva/shared-common';

export type DB = NodePgDatabase<Record<string, never>>;

export interface AnswerScore {
    score: number;
    summary: string;
    videoLink: string;
    code: string;
}

export interface Answer {
    text: string;
    videoLink: string;
}

export interface GradeModelQuery {
    questionText: string;
    answerText: string;
    gradingPrinciple: string;
    personalityTrait: string;
    promptTemplate: string;
}

export interface PersonalityTraitsFamily {
    languageVariation: LanguageVariation;
    code: string;
}

export interface TextCorrection {
    text: string;
    correction: string;
}

export interface TraitFamilyScore {
    score: number;
    summary: string;
    code: string;
}

export interface AnalyzerReport {
    traits: AnswerScore[];
    families: TraitFamilyScore[];
    interviewScore: number;
    summary: string;
}

export interface PersonalityTrait {
    languageVariation: LanguageVariation;
    code: string;
    traitFamily: PersonalityTraitsFamily;
    traitSummariesMap: TraitSummariesMap;
}

export interface Question {
    trait: PersonalityTrait;
    gradingPrinciplesMap: GradingPrinciplesMap;
    text: string;
    code: string;
}

export interface AnalyzerAnswerResult {
    score: number;
    summary: string;
    videoLink: string;
    trait: PersonalityTrait;
}

export interface Interview {
    answers: AnalyzerAnswerResult[];
    interviewSummarySentencesMap: Record<number, string[]>;
    familiesSummaryTemplate: string;
    language: string;
    corrections: TextCorrection[];
}

export interface TemplatesMap {
    en: Record<string, string>;
    he?: Record<string, string>;
}

export interface QuestionAnswerPair {
    question: Question;
    answer: Answer;
    templatesMap: TemplatesMap;
}
