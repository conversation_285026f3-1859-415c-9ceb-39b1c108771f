import { Link, Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../PDF.styles";
import { determineBorderRadius } from "../../PDF.utils";
import { PlaySVGIcon } from "./icons/ PlaySVGIcon";
import { AdditionalQuestion } from "@eva/shared-common";

interface AdditionalQuestionsSectionProps {
    additionalQuestions: AdditionalQuestion[];
    isRtl: boolean;
    warmup?: boolean;
}

export const AdditionalQuestionsSection: FC<AdditionalQuestionsSectionProps> = ({
    additionalQuestions,
    isRtl,
    warmup,
}) => {
    const headerText = warmup
        ? isRtl
            ? "שאלות פתיחה"
            : "Warmup Questions"
        : isRtl
        ? "שאלות פתוחות"
        : "Additional Questions";
    return (
        <View style={[styles.sectionContainer]}>
            <View style={isRtl ? styles.alignEnd : {}}>
                <Text style={isRtl ? styles.hebrewTextBoldHeader : styles.textBoldHeader}>{headerText}</Text>
            </View>
            <View style={styles.displayFlex}>
                {additionalQuestions.map((question, idx) => (
                    <View
                        key={question.question}
                        style={[
                            styles.border,
                            styles.displayFlex,
                            styles.spaceBetween,
                            styles.flewDirectionRow,
                            styles.padding6,
                            styles.alignItemsCenter,
                            ...determineBorderRadius(additionalQuestions.length, idx),
                            isRtl ? styles.justifyEnd : styles.justifyStart,
                        ]}
                    >
                        <View
                            style={[
                                styles.displayFlex,
                                styles.flexGap3,
                                styles.alignItemsCenter,
                                styles.flewDirectionRow,
                                isRtl ? styles.flexReverse : {},
                            ]}
                        >
                            <Link src={question.video_link}>
                                <PlaySVGIcon />
                            </Link>
                            <View style={isRtl ? styles.rtlTextContainer : {}}>
                                <Text style={isRtl ? styles.hebrewTextAdditionalQuestions : styles.textProgressBar}>
                                    {question.question}
                                </Text>
                            </View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
