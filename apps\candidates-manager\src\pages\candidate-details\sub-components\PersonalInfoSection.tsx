import { FC } from "react";
import { Stack } from "@mui/material";
import { Email, Phone, Person, Badge } from "@mui/icons-material";
import { H4 } from "@eva/shared-ui";
import { MetadataItem } from "./MetadataItem";

interface PersonalInfoSectionProps {
    email: string;
    phoneNumber?: string;
    gender?: string;
    socialId?: string;
}

export const PersonalInfoSection: FC<PersonalInfoSectionProps> = ({
    email,
    phoneNumber,
    gender,
    socialId,
}) => {
    return (
        <Stack spacing={2}>
            <H4>Personal Information</H4>
            <MetadataItem
                icon={<Email fontSize="small" />}
                label="Email"
                value={email}
            />
            <MetadataItem
                icon={<Phone fontSize="small" />}
                label="Phone"
                value={phoneNumber || "N/A"}
            />
            <MetadataItem
                icon={<Person fontSize="small" />}
                label="Gender"
                value={gender || "N/A"}
            />
            {socialId && (
                <MetadataItem
                    icon={<Badge fontSize="small" />}
                    label="Social ID"
                    value={socialId}
                />
            )}
        </Stack>
    );
}; 