import { Link, Text, View } from "@react-pdf/renderer";
import { determineBorderRadius, determineColor } from "../../PDF.utils";
import { stylesPDF as styles } from "../../PDF.styles";
import { PlaySVGIcon } from "./icons/ PlaySVGIcon";
import { TraitProgressBarEmptySegment } from "./TraitProgressBarEmptySegment";
import { TraitProgressBarFilledSegment } from "./TraitProgressBarFilledSegment";

interface ProgressBarProps {
    totalTraits: number;
    traitIndex: number;
    score: number;
    max: number;
    width: number;
    segments: number;
    gap: number;
    traitName: string;
    videoLink: string;
    isRtl: boolean;
}

export const TraitProgressBar: React.FC<ProgressBarProps> = ({
    score,
    traitName,
    max,
    width,
    segments,
    gap,
    totalTraits,
    traitIndex,
    videoLink,
    isRtl,
}) => {
    const segmentWidth = (width - gap * (segments - 1)) / segments;
    const filledSegments = Math.round((score / max) * segments);
    const emptySegments = segments - filledSegments;

    const colorToRender = determineColor(score);
    const borderStyles = determineBorderRadius(totalTraits, traitIndex);
    const scoreForDisplay = score === 0 ? "*" : score;

    return (
        <View
            style={[
                styles.border,
                styles.displayFlex,
                styles.spaceBetween,
                styles.flewDirectionRow,
                styles.padding6,
                styles.alignItemsCenter,
                ...borderStyles,
                isRtl ? styles.flexReverse : {},
            ]}
        >
            <View
                style={[styles.displayFlex, styles.flexGap4, styles.alignItemsCenter, styles.flewDirectionRow, isRtl ? styles.flexReverse : {}]}
            >
                {videoLink && (
                    <Link src={videoLink}>
                        <PlaySVGIcon />
                    </Link>
                )}
                <Text style={isRtl ? styles.hebrewTextProgressBar : styles.textProgressBar}>{traitName}</Text>
            </View>
            <View
                style={[styles.displayFlex, styles.flewDirectionRow, styles.alignItemsCenter, styles.flexGap6, isRtl ? styles.flexReverse : {}]}
            >
                <View style={[styles.barContainer, isRtl ? styles.flexReverse : {}, { width }]}>
                    {Array.from({ length: filledSegments }).map((_, index) => (
                        <TraitProgressBarFilledSegment
                            key={index}
                            idx={index}
                            segmentWidth={segmentWidth}
                            filledSegments={filledSegments}
                            isRtl={isRtl}
                            colorToRender={colorToRender}
                            segments={segments}
                        />
                    ))}
                    {Array.from({ length: emptySegments }).map((_, index) => (
                        <TraitProgressBarEmptySegment
                            key={index}
                            idx={index}
                            segmentWidth={segmentWidth}
                            emptySegments={emptySegments}
                            isRtl={isRtl}
                        />
                    ))}
                </View>
                <Text style={isRtl ? styles.textBoldScore : styles.textBoldScore}>{scoreForDisplay}</Text>
            </View>
        </View>
    );
};
