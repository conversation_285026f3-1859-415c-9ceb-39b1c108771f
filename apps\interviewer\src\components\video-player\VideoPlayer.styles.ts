import { styled } from "@mui/material";

export const StyledVideo = styled("video")<{ hidden?: boolean }>(({ hidden }) => ({
    borderRadius: "20px",
    objectFit: "cover",
    backgroundColor: "white",
    height: "100%",
}));

export const MobileStyledVideo = styled("video")<{ hidden?: boolean; width?: string; height?: string }>(
    ({ hidden, width, height }) => ({
        objectFit: "cover",
        display: hidden ? "none" : "block",
        width,
        height,
    })
);
