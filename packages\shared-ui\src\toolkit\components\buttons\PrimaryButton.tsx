import { Button, CircularProgress, styled } from "@mui/material";
import { useState } from "react";

interface PrimaryButtonContainerProps {
    width: string;
}

const PrimaryButtonContainer = styled(Button)<PrimaryButtonContainerProps>(({ width }) => ({
    width,
}));

export interface PrimaryButtonProps {
    variant?: "contained" | "outlined";
    content: string | JSX.Element;
    onClick: () => void;
    disabled?: boolean;
    timeDisabled?: number;
    "data-qa"?: string;
    isLoading?: boolean;
    endIcon?: JSX.Element;
    width?: string;
}

export const PrimaryButton = ({
    variant = "contained",
    content,
    onClick,
    disabled,
    timeDisabled,
    "data-qa": dataQa,
    isLoading,
    endIcon,
    width = "100%",
}: PrimaryButtonProps) => {
    const [isTimeDisabled, setIsTimeDisabled] = useState(timeDisabled ? true : false);

    if (timeDisabled) {
        setTimeout(() => {
            setIsTimeDisabled(false);
        }, timeDisabled);
    }

    const isDisabled = disabled || isTimeDisabled;

    return (
        <PrimaryButtonContainer
            width={width}
            data-qa={dataQa}
            color='primary'
            variant={variant}
            onClick={onClick}
            disabled={isDisabled}
            endIcon={endIcon}
        >
            {isLoading ? <CircularProgress color='inherit' size='20px' /> : content}
        </PrimaryButtonContainer>
    );
};
