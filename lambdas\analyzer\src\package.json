{"name": "@eva/analyzer", "version": "1.0.0", "description": "hello world sample for NodeJS", "main": "app.js", "repository": "https://github.com/awslabs/aws-sam-cli/tree/develop/samcli/local/init/templates/cookiecutter-aws-sam-hello-nodejs", "author": "SAM CLI", "license": "MIT", "scripts": {"unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc", "test": "npm run compile && npm run unit", "build": "esbuild app.ts --bundle --platform=node --target=node20 --external:pg-native --external:@aws-sdk/* --outfile=dist/app.js --minify --sourcemap"}, "dependencies": {"esbuild": "^0.14.14", "@aws-sdk/client-s3": "^3.726.1", "@aws-sdk/client-sqs": "^3.682.0", "axios": "^1.7.9", "openai": "^4.78.1", "@eva/shared-common": "workspace:*", "@eva/drizzle": "workspace:*", "@eva/logger": "workspace:*", "@eva/aws-utils": "workspace:*"}, "devDependencies": {"@types/aws-lambda": "^8.10.92", "@types/jest": "^29.2.0", "@jest/globals": "^29.2.0", "@types/node": "^20.5.7", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "esbuild": "^0.24.0", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.2.1", "prettier": "^2.5.1", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}