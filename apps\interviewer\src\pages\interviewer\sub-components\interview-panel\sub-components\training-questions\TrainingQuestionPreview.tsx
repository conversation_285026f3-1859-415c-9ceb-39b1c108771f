import { Stack } from "@mui/material";
import <PERSON><PERSON> from "lottie-react";
import talkingAvatar from "../../../../../../assets/animations/talking-avatar.json";
import { H4, useMediaQueryContext } from "@eva/shared-ui";
import { VideoPlayer } from "../../../../../../components/video-player/VideoPlayer";
import { useInterviewer } from "../../../../InterviewerProvider";

const englishTrainingQuestion: string = "Tell me about yourself.";
const hebrewTrainingQuestion: string = "ספר לי על עצמך.";

export const TrainingQuestionPreview = () => {
    const { trainingRecordingUrl, data } = useInterviewer();
    const { isMobile } = useMediaQueryContext();

    const trainingQuestion = data?.language === "he" ? hebrewTrainingQuestion : englishTrainingQuestion;

    return (
        <>
            <Stack alignItems="center" justifyContent="center" gap="20px" position="relative">
                {!isMobile && (
                    <>
                        {trainingRecordingUrl ? (
                            <VideoPlayer data-qa="question-video-preview"  videoUrl={trainingRecordingUrl} />
                        ) : (
                            <Lottie width={450} animationData={talkingAvatar} loop />
                        )}
                    </>
                )}
                <H4 data-qa="question-text-preview">{trainingQuestion}</H4>
            </Stack>
        </>
    );
};
