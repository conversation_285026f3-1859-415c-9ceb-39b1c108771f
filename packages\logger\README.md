# Node Logger

This is a simple logger module for Node.js projects using the `winston` library.

## Installation

To use the logger in your parent project, add the following dependency to your `package.json`:

```json
"dependencies": {
    "winston": "^3.16.0"
}
```

Then, install the package using npm:

```sh
npm install
```

## Usage

Here is an example of how to use the logger in your project:

```javascript
const winston = require('winston');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.json(),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'combined.log' })
    ]
});

logger.info('This is an info message');
logger.error('This is an error message');
```

## License

This project is licensed under the MIT License.