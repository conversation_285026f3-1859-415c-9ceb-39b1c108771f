import { stylesPDF as styles } from "./PDF.styles";

export interface DataValue {
    label: string;
    value: number;
}

export type ProgressBarColor = "#04e05c" | "#FF8A8A" | "#f2aa4b";

export const determineColor = (score: number): ProgressBarColor => {
    if (score > 3) {
        return "#04e05c";
    } else if (score > 2 && score <= 3) {
        return "#f2aa4b";
    } else {
        return "#FF8A8A";
    }
};

export const determineBorderRadius = (totalTraits: number, traitIndex: number) => {
    if (traitIndex === 0 && totalTraits === 1) {
        return [styles.topRounded, styles.bottomRounded];
    } else if (traitIndex === 0) {
        return [styles.topRounded];
    } else if (traitIndex === totalTraits - 1) {
        return [styles.bottomRounded];
    } else {
        return [];
    }
};

export const recommendationMessage = (finalScore: number, isRtl: boolean) => {
    if (finalScore < 3) return isRtl ? "לא ממולץ" : "Not recommended";
    if (finalScore === 3 && finalScore < 4) return isRtl ? "מומלץ" : "Recommended";
    return isRtl ? "מומלץ מאוד" : "Highly recommended";
};
