{"version": "1", "cognitoConfig": {"identityPoolName": "iverseadminclient_identitypool_90996916", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "iverse90996916", "userPoolName": "iverseadminclient_userpool_90996916", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "iverse90996916_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "authSelections": "identityPoolAndUserPool", "resourceName": "iverseadminclient", "sharedId": "90996916", "serviceName": "Cognito", "useDefault": "manual", "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "useEnabledMfas": true, "dependsOn": [], "userPoolGroupList": ["psychologists"]}}