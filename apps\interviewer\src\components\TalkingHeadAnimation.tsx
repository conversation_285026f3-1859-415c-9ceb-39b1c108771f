import Lottie from "lottie-react";
import talkingAvatar from "../assets/animations/talking-avatar.json";
import { FC, useEffect } from "react";
import { useMediaQueryContext } from "@eva/shared-ui";

interface TalkingHeadAnimationProps {
    onMount?: () => void;
}

export const TalkingHeadAnimation: FC<TalkingHeadAnimationProps> = ({ onMount }) => {
    const { isMobile } = useMediaQueryContext();
    useEffect(() => {
        if (onMount) {
            onMount();
        }
    }, [onMount]);

    return !isMobile ? <Lottie width={450} animationData={talkingAvatar} loop /> : <></>;
};
