{"name": "eva", "private": true, "workspaces": ["apps/*", "packages/*", "lambdas/*"], "pnpm": {"overrides": {"drizzle-orm": "0.33.0", "winston": "3.11.0", "@aws-sdk/client-secrets-manager": "3.665.0", "@aws-sdk/client-sns": "3.726.1", "@aws-sdk/client-sqs": "3.741.0", "@aws-sdk/cloudfront-signer": "3.696.0", "typescript": "5.3.3", "@types/node": "20.11.0", "dotenv": "16.4.5", "express": "4.15.2", "@types/express": "4.17.17", "@types/aws-lambda": "8.10.52", "esbuild": "0.24.0"}}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "format": "prettier --write \\\"**/*.{js,tsx,md}\\\"", "deploy": "turbo run deploy", "type-check": "turbo run type-check", "db:migrate": "turbo run db:migrate --filter-db-module", "db:generate": "turbo run db:generate --filter-db-module", "update-deps": "turbo run install --scope=apps/* --scope=packages/* --scope=lambdas/*"}, "devDependencies": {"@changesets/cli": "2.27.1", "prettier": "3.1.1", "turbo": "1.11.3", "@types/node": "20.11.0"}, "packageManager": "pnpm@8.10.4"}