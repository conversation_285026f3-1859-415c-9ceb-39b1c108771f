import { NextFunction, Request, Response, Router } from "express";
import { getJobService, getLogger } from "../config/initialize";
import { 
    CMJobDetailsResponse, 
    CMJobsTableDataResponse, 
    CMCreateJobRequest,
    JobInvitationData 
} from "@eva/shared-common";
const router = Router();
const logger = getLogger();

router.get(
    "/:customerId",
    async (
        req: Request<{ customerId: string }, {}, {}, {}>,
        res: Response<CMJobsTableDataResponse, {}>,
        next: NextFunction
    ) => {
        try {
            const jobService = getJobService();
            const jobs = await jobService.getJobs(req.params.customerId);
            res.json(jobs);
        } catch (error) {
            logger.error("Error getting jobs");
            next(error);
        }
    }
);

router.get(
    "/details/:jobId",
    async (
        req: Request<{ jobId: string }, {}, {}, {}>,
        res: Response<CMJobDetailsResponse, {}>,
        next: NextFunction
    ) => {
        try {
            const jobService = getJobService();
            const job = await jobService.getJobById(req.params.jobId);
            res.json(job);
        } catch (error) {
            logger.error(`Error getting job details for job ${req.params.jobId}`);
            next(error);
        }
    }
);

router.post(
    "/:jobId/invitation",
    async (
        req: Request<{ jobId: string }, {}, { invitation: JobInvitationData }, {}>,
        res: Response,
        next: NextFunction
    ) => {
        try {
            const jobService = getJobService();
            await jobService.updateJobInvitation(req.params.jobId, req.body.invitation);
            res.json({ message: "Job invitation updated successfully" });
        } catch (error) {
            logger.error(`Error updating job invitation for job ${req.params.jobId}`);
            next(error);
        }
    }
);

router.post(
    "/", 
    async (
        req: Request<{}, {}, CMCreateJobRequest, {}>,
        res: Response<{ id: string } | { message: string }, {}>,
        next: NextFunction
    ) => {
        try {
            const { jobTitle, description, language, customerId } = req.body;
            if (!jobTitle || !description || !language || !customerId) {
                logger.warn("Missing required fields for job creation");
                return res.status(400).json({ message: "Missing required fields: jobTitle, description, language, customerId" });
            }

            const jobService = getJobService();
            const result = await jobService.createJob(req.body);
            res.status(201).json(result);
        } catch (error) {
            logger.error("Error creating job route", error);
            next(error);
        }
    }
);

export default router;
