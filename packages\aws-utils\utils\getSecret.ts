import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";

export async function getSecret<T>(secretName: string, regionName: string = "us-east-1"): Promise<T> {
    const client = new SecretsManagerClient({
        region: regionName,
        
    });

    const response = await client.send(
        new GetSecretValueCommand({
            SecretId: secretName,
            VersionStage: "AWSCURRENT",
        })
    );
    if (response.SecretString) {
        return JSON.parse(response.SecretString) as T;
    } else {
        throw new Error("SecretString not found in response");
    }
}
