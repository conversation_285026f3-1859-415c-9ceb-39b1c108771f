import { DB, candidates, jobs, customers, interviews } from "@eva/drizzle";
import { eq, and, inArray, sql } from "@eva/drizzle";
import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import {
    CMCandidateDetailsResponse,
    CMCandidatesTableDataResponse,
    CMInviteCandidatesRequest,
    CMUpdateCandidateRequest,
    CreateCandidateRequest,
    getBucketName,
    InterviewStatus,
    JobInvitationData,
    ValidateCandidatesResponse,
    ValidateEmailResponse,
} from "@eva/shared-common";
import { SendEmailCommand, SendEmailCommandInput, SESClient } from "@aws-sdk/client-ses";
import { Logger } from "@eva/logger";

interface CandidateData {
    id: string;
    name: string;
    email: string | null;
    phoneNumber: string | null;
}

interface JobData {
    id: string;
    jobTitle: string;
    invitation: JobInvitationData | null;
    customerId: string;
    language: string;
}

interface CustomerData {
    companyName: string;
}

interface EnvironmentVariables {
    emailSender: string;
    interviewBaseUrl: string;
    smsTopicArn?: string;
}

interface TemplateValues {
    [key: string]: string;
}

const IN_PROGRESS_STATUSES: InterviewStatus[] = ["pending", "interviewing", "transferring", "running"];

const scoreField = sql<number>`
COALESCE(
    CASE 
        WHEN (${interviews.analyzerReport}->'report_versions'->-1->>'approved')::boolean = true 
        THEN (${interviews.analyzerReport}->'report_versions'->-1->>'interview_score')::numeric 
        ELSE NULL
    END,
    NULL
)
`;

const reportExistsField = sql<boolean>`
COALESCE(
    CASE 
        WHEN (${interviews.analyzerReport}->'report_versions'->-1->>'approved')::boolean = true 
        THEN true
        ELSE false
    END,
    false
)
`;

export class CandidateService {
    private sesClient: SESClient;
    private env: string = process.env.ENV || "staging";
    constructor(
        private db: DB,
        private logger: Logger
    ) {
        this.sesClient = new SESClient({ region: process.env.REGION || "us-east-1" });
    }
    async getCandidates(customerId: string): Promise<CMCandidatesTableDataResponse> {
        try {
            this.logger.info(`Getting candidates for customer ${customerId}`);

            const res = await this.db
                .select({
                    id: candidates.id,
                    name: candidates.name,
                    createdAt: candidates.createdAt,
                    jobId: candidates.jobId,
                    jobTitle: jobs.jobTitle,
                    email: candidates.email,
                })
                .from(candidates)
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .where(eq(jobs.customerId, customerId));

            this.logger.info(`GETTING CANDIDATES FOR CUSTOMER ${customerId}: ${JSON.stringify(res)}`);

            return {
                candidates: res.map((candidate) => ({
                    id: candidate.id,
                    name: candidate.name,
                    email: candidate.email ?? "",
                    createdAt: candidate.createdAt?.toISOString() ?? "",
                    jobTitle: candidate.jobTitle,
                })),
            };
        } catch (error) {
            this.logger.error(`Error getting candidates for customer ${customerId}`, error);
            throw error;
        }
    }

    async getCandidatesByJobId(jobId: string): Promise<CMCandidatesTableDataResponse> {
        try {
            this.logger.info(`Getting candidates for job ${jobId}`);

            const candidatesQuery = await this.db
                .select({
                    id: candidates.id,
                    name: candidates.name,
                    email: candidates.email,
                    gender: candidates.gender,
                    socialId: candidates.socialId,
                    phoneNumber: candidates.phoneNumber,
                    jobId: candidates.jobId,
                    createdAt: candidates.createdAt,
                    jobTitle: jobs.description,
                })
                .from(candidates)
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .where(eq(candidates.jobId, jobId));

            return {
                candidates: candidatesQuery.map((c) => {
                    return {
                        id: c.id,
                        name: c.name,
                        email: c.email ?? "",
                        jobId: c.jobId,
                        jobTitle: c.jobTitle ?? "Unknown Job",
                        createdAt: c.createdAt?.toISOString() ?? new Date().toISOString(),
                    };
                }),
            };
        } catch (error) {
            this.logger.error(`Error getting candidates for job ${jobId}: ${error}`);
            throw error;
        }
    }

    async getCandidateById(id: string): Promise<CMCandidateDetailsResponse | null> {
        try {
            this.logger.info(`Getting candidate ${id}`);

            const candidateQuery = await this.db
                .select({
                    id: candidates.id,
                    name: candidates.name,
                    email: candidates.email,
                    gender: candidates.gender,
                    socialId: candidates.socialId,
                    phoneNumber: candidates.phoneNumber,
                    jobId: candidates.jobId,
                    createdAt: candidates.createdAt,
                    imageLink: candidates.imageLink,
                    jobTitle: jobs.description,
                    language: jobs.language,
                })
                .from(candidates)
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .where(eq(candidates.id, id))
                .limit(1);

            if (candidateQuery.length === 0) {
                this.logger.warn(`Candidate with ID ${id} not found`);
                return null;
            }

            const candidate = candidateQuery[0];

            return {
                id: candidate.id,
                name: candidate.name,
                jobId: candidate.jobId,
                jobTitle: candidate.jobTitle,
                createdAt: candidate.createdAt.toISOString(),
                email: candidate.email ?? undefined,
                gender: candidate.gender ?? undefined,
                socialId: candidate.socialId ?? undefined,
                language: candidate.language ?? undefined,
                imageLink: candidate.imageLink ?? undefined,
                phoneNumber: candidate.phoneNumber ?? undefined,
            };
        } catch (error) {
            this.logger.error(`Error getting candidate ${id}: ${error}`);
            throw error;
        }
    }

    async createCandidate(candidateData: CreateCandidateRequest): Promise<string> {
        try {
            this.logger.info(`Creating candidate for job ${candidateData.jobId}`);

            const job = await this.db
                .select({
                    language: jobs.language,
                })
                .from(jobs)
                .where(eq(jobs.id, candidateData.jobId))
                .then((res) => res[0]);

            if (!job) {
                throw new Error(`Job with ID ${candidateData.jobId} not found`);
            }

            const result = await this.db
                .insert(candidates)
                .values({
                    name: `${candidateData.firstName} ${candidateData.lastName}`,
                    email: candidateData.email,
                    socialId: candidateData.socialId ? candidateData.socialId : null,
                    phoneNumber: candidateData.phoneNumber ? candidateData.phoneNumber : null,
                    jobId: candidateData.jobId,
                })
                .returning({ id: candidates.id });

            if (!result[0]?.id) {
                throw new Error("Failed to create candidate");
            }

            return result[0].id;
        } catch (error) {
            this.logger.error(`Error creating candidate: ${error}`);
            throw error;
        }
    }

    async bulkCreateCandidates(candidatesData: CreateCandidateRequest[]): Promise<string[]> {
        try {
            this.logger.info(`Bulk creating ${candidatesData.length} candidates`);

            const jobIds = [...new Set(candidatesData.map((c) => c.jobId))];
            if (jobIds.length > 1) {
                throw new Error("All candidates must be for the same job");
            }

            const jobId = jobIds[0];

            const job = await this.db
                .select({
                    language: jobs.language,
                })
                .from(jobs)
                .where(eq(jobs.id, jobId))
                .then((res) => res[0]);

            if (!job) {
                throw new Error(`Job with ID ${jobId} not found`);
            }

            const candidateRecords = candidatesData.map((candidate) => ({
                name: `${candidate.firstName} ${candidate.lastName}`,
                email: candidate.email,
                socialId: candidate.socialId ? candidate.socialId : null,
                phoneNumber: candidate.phoneNumber ? candidate.phoneNumber : null,
                jobId: candidate.jobId,
            }));

            const results = await this.db.insert(candidates).values(candidateRecords).returning({ id: candidates.id });

            const createdIds = results.map((r) => r.id);
            this.logger.info(`Successfully created ${createdIds.length} candidates for job ${jobId}`);

            return createdIds;
        } catch (error) {
            this.logger.error(`Error bulk creating candidates: ${error}`);
            throw error;
        }
    }

    async inviteCandidates(request: CMInviteCandidatesRequest): Promise<void> {
        try {
            this.logger.info(`Inviting candidates for job ${request.jobId}`, { candidateIds: request.candidateIds });

            const [jobData, candidatesData] = await Promise.all([
                this.getJobData(request.jobId),
                this.getCandidatesData(request.candidateIds),
            ]);

            const customerData = await this.getCustomerData(jobData.customerId);

            const language = jobData.language === "he" ? "he" : "en";

            const emailTemplate = jobData.invitation?.mailTemplateOption?.[language] || "";

            const { emailSender, interviewBaseUrl } = this.getEnvironmentVariables();

            await this.processCandidateInvitations(
                candidatesData,
                jobData,
                customerData,
                language,
                emailTemplate,
                emailSender,
                interviewBaseUrl
            );

            this.logger.info(
                `Successfully processed invitations for ${candidatesData.length} candidates for job ${request.jobId}`
            );
        } catch (error) {
            this.logger.error(`Error inviting candidates for job ${request.jobId}`, error);
            throw error;
        }
    }

    private async getJobData(jobId: string): Promise<JobData> {
        const jobData = await this.db
            .select({
                id: jobs.id,
                jobTitle: jobs.jobTitle,
                invitation: jobs.invitation,
                customerId: jobs.customerId,
                language: jobs.language,
            })
            .from(jobs)
            .where(eq(jobs.id, jobId))
            .then((res) => res[0]);

        if (!jobData) {
            throw new Error(`Job with ID ${jobId} not found`);
        }

        return jobData;
    }

    private async getCustomerData(customerId: string): Promise<CustomerData> {
        const customerData = await this.db
            .select({
                companyName: customers.companyName,
            })
            .from(customers)
            .where(eq(customers.id, customerId))
            .then((res) => res[0]);

        if (!customerData) {
            throw new Error(`Customer with ID ${customerId} not found`);
        }

        return customerData;
    }

    private async getCandidatesData(candidateIds: string[]): Promise<CandidateData[]> {
        const candidatesData = await this.db
            .select({
                id: candidates.id,
                name: candidates.name,
                email: candidates.email,
                phoneNumber: candidates.phoneNumber,
            })
            .from(candidates)
            .where(inArray(candidates.id, candidateIds));

        if (candidatesData.length === 0) {
            throw new Error("No candidates found with the provided IDs");
        }

        return candidatesData;
    }

    private getEnvironmentVariables(): EnvironmentVariables {
        const emailSender = process.env.EMAIL_SENDER;
        if (!emailSender) {
            throw new Error("EMAIL_SENDER environment variable is not set");
        }

        const interviewBaseUrl = process.env.INTERVIEW_BASE_URL;
        if (!interviewBaseUrl) {
            throw new Error("INTERVIEW_BASE_URL environment variable is not set");
        }

        const smsTopicArn = process.env.SMS_INVITATION_SNS_TOPIC_ARN;
        if (!smsTopicArn) {
            this.logger.warn(
                "SMS_INVITATION_SNS_TOPIC_ARN environment variable is not set. SMS invitations will be skipped."
            );
        }

        return { emailSender, interviewBaseUrl, smsTopicArn };
    }

    private async createInterviewForInvitedCandidate(candidateId: string, jobId: string): Promise<string> {
        try {
            const runId = crypto.randomUUID();
            const jobQueryResult = await this.db
                .select({
                    questions: jobs.questions,
                })
                .from(jobs)
                .where(eq(jobs.id, jobId));

            const jobData = jobQueryResult[0];

            const interviewId = await this.db
                .insert(interviews)
                .values({ candidateId: candidateId, status: "invited", runId: runId, questions: jobData.questions })
                .returning({ id: interviews.id });
            return interviewId[0].id;
        } catch (error) {
            this.logger.error(`Error creating interview for candidate ${candidateId}`, error);
            throw error;
        }
    }

    private async processCandidateInvitations(
        candidatesData: CandidateData[],
        jobData: JobData,
        customerData: CustomerData,
        language: string,
        emailTemplate: string,
        emailSender: string,
        interviewBaseUrl: string
    ): Promise<void> {
        for (const candidate of candidatesData) {
            try {
                let emailText = this.replaceTemplateTags(emailTemplate, {
                    name: candidate.name || "",
                    job: jobData.jobTitle || "",
                    company: customerData.companyName || "",
                });

                if (candidate.email) {
                    let interviewId: string | null = null;

                    const res = await this.db
                        .select({ id: interviews.id })
                        .from(interviews)
                        .where(and(eq(interviews.candidateId, candidate.id), eq(interviews.status, "invited")));

                    if (res.length > 0) {
                        interviewId = res[0].id;
                    } else {
                        interviewId = await this.createInterviewForInvitedCandidate(candidate.id, jobData.id);
                    }

                    const interviewLink = `${interviewBaseUrl}/interviewer/${interviewId}`;

                    this.logger.info(`Sending email invitation to candidate ${candidate.id} at ${candidate.email}`);

                    await this.sendEmailInvitation(candidate, jobData, emailText, interviewLink, language, emailSender);
                } else {
                    this.logger.warn(
                        `Skipping email invitation for candidate ${candidate.id} - no email address provided`
                    );
                }
            } catch (error) {
                this.logger.error(`Error sending invitation to candidate ${candidate.id}`, error);
            }
        }
    }

    // private async sendSmsInvitation(
    //     candidate: CandidateData,
    //     jobData: JobData,
    //     customerData: CustomerData,
    //     interviewLink: string
    // ): Promise<void> {
    //     try {
    //         if (!candidate.phoneNumber) {
    //             this.logger.warn(`Cannot send SMS to candidate ${candidate.id} - no phone number provided`);
    //             return;
    //         }

    //         let formattedPhoneNumber = candidate.phoneNumber;

    //         if (formattedPhoneNumber.startsWith("0")) {
    //             this.logger.info(
    //                 `Formatting phone number for SMS invitation to candidate ${candidate.id} at ${formattedPhoneNumber}`
    //             );
    //             formattedPhoneNumber = "+972" + formattedPhoneNumber.substring(1);
    //         } else if (!formattedPhoneNumber.startsWith("+")) {
    //             this.logger.info(
    //                 `Formatting phone number for SMS invitation to candidate ${candidate.id} at ${formattedPhoneNumber}`
    //             );
    //             formattedPhoneNumber = "+972" + formattedPhoneNumber;
    //         }

    //         const smsMessage = `${customerData.companyName} מזמין אותך לראיון עבור משרת ${jobData.jobTitle}. קישור לראיון: ${interviewLink}`;

    //         await this.publishToSNS(formattedPhoneNumber, smsMessage);
    //         this.logger.info(`SMS invitation sent to candidate ${candidate.id} at ${formattedPhoneNumber}`);
    //     } catch (smsError) {
    //         this.logger.error(`Error sending SMS invitation to candidate ${candidate.id}`, smsError);
    //     }
    // }

    private async sendEmailInvitation(
        candidate: CandidateData,
        jobData: JobData,
        emailText: string,
        interviewLink: string,
        language: string,
        emailSender: string
    ): Promise<void> {
        if (!candidate.email) {
            this.logger.warn(`Cannot send email to candidate ${candidate.id} - no email address provided`);
            return;
        }

        const htmlBody = this.createHtmlEmail(emailText, interviewLink, language);

        const emailSubject =
            language === "he"
                ? `הזמנה לראיון עבור ${jobData.jobTitle}`
                : `Interview Invitation for ${jobData.jobTitle}`;

        await this.sendEmailWithSES(emailSender, candidate.email, emailSubject, htmlBody);

        this.logger.info(`Email invitation sent to candidate ${candidate.id} at ${candidate.email}`);
    }

    private createHtmlEmail(bodyText: string, interviewLink: string, language: string): string {
        const textWithParagraphs = bodyText
            .split("\n")
            .map((line) => `<p class="email-text">${line}</p>`)
            .join("");

        const buttonText = language === "he" ? "התחל את הראיון" : "Start Interview";

        const direction = language === "he" ? "rtl" : "ltr";

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #000;
                    direction: ${direction};
                    text-align: ${direction === "rtl" ? "right" : "left"};
                }
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .button-container {
                    text-align: center;
                    margin: 30px 0;
                }
                .interview-button {
                    display: inline-block;
                    background-color: #1E90FF;
                    color: white !important;
                    padding: 12px 28px;
                    text-decoration: none;
                    font-weight: bold;
                    border-radius: 4px;
                }
                .interview-button:hover {
                    background-color: #187bcd;
                    color: white !important;
                }
                .email-text {
                    text-align: ${direction === "rtl" ? "right" : "left"};
                    direction: ${direction};
                }
                p {
                    margin: 0 0 10px 0;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-text">${textWithParagraphs}</div>
                <div class="button-container">
                    <a href="${interviewLink}" class="interview-button" style="color: white !important;">${buttonText}</a>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    private replaceTemplateTags(template: string, values: TemplateValues): string {
        let result = template;

        for (const [key, value] of Object.entries(values)) {
            result = result.replace(new RegExp(`\\[${key}\\]`, "g"), value);
        }

        return result;
    }

    private async sendEmailWithSES(
        from: string,
        to: string,
        subject: string,
        body: string,
        isHtml: boolean = true
    ): Promise<void> {
        try {
            const params: SendEmailCommandInput = {
                Source: from,
                Destination: {
                    ToAddresses: [to],
                },
                Message: {
                    Subject: {
                        Data: subject,
                        Charset: "UTF-8",
                    },
                    Body: isHtml
                        ? {
                              Html: {
                                  Data: body,
                                  Charset: "UTF-8",
                              },
                          }
                        : {
                              Text: {
                                  Data: body,
                                  Charset: "UTF-8",
                              },
                          },
                },
            };

            const command = new SendEmailCommand(params);
            await this.sesClient.send(command);
        } catch (error) {
            this.logger.error("Error sending email with SES", error);
            throw error;
        }
    }

    // private async publishToSNS(phoneNumber: string, message: string): Promise<void> {
    //     try {
    //         const command = new PublishCommand({
    //             PhoneNumber: phoneNumber,
    //             Message: message,
    //             MessageAttributes: {
    //                 messageType: {
    //                     DataType: "String",
    //                     StringValue: "candidate_invitation",
    //                 },
    //             },
    //         });

    //         await this.snsClient.send(command);
    //     } catch (error) {
    //         this.logger.error("Error publishing to SNS", error);
    //         throw error;
    //     }
    // }

    async checkDuplicateCandidate(email: string, jobId: string): Promise<boolean> {
        try {
            this.logger.info(`Checking if candidate with email ${email} exists for job ${jobId}`);

            const existingCandidate = await this.db
                .select({ id: candidates.id })
                .from(candidates)
                .where(and(eq(candidates.jobId, jobId), eq(candidates.email, email)))
                .limit(1);

            return existingCandidate.length > 0;
        } catch (error) {
            this.logger.error(`Error checking for duplicate candidate: ${error}`);
            throw error;
        }
    }

    async getCandidateScore(candidateId: string): Promise<{ score: number | null }> {
        try {
            this.logger.info(`Getting score for candidate ${candidateId}`);

            const score = await this.db
                .select({ score: scoreField })
                .from(interviews)
                .where(eq(interviews.candidateId, candidateId));

            if (score.length === 0) {
                return { score: null };
            }

            const interviewsWithScore = score.filter((s) => s.score !== null);

            if (interviewsWithScore.length === 0) {
                return { score: null };
            }

            return { score: interviewsWithScore[0].score };
        } catch (error) {
            this.logger.error(`Error getting score for candidate ${candidateId}`, error);
            throw error;
        }
    }

    async getCandidateStatus(candidateId: string): Promise<{ status: InterviewStatus | null }> {
        try {
            this.logger.info(`Getting status for candidate ${candidateId}`);

            const res = await this.db
                .select({
                    status: interviews.status,
                    report: interviews.analyzerReport,
                })
                .from(interviews)
                .where(eq(interviews.candidateId, candidateId));

            if (!res.length) return { status: null };

            const isInvited = res.some((r) => r.status === "invited");

            if (isInvited) {
                return { status: "invited" };
            }

            const completetInterviews = res.filter((r) => r.status === "completed");

            if (completetInterviews.length > 0) {
                const reportWithApprovedVersion = completetInterviews.find((r) => {
                    const reportVersions = r.report?.report_versions;
                    const approvedReport = reportVersions?.find((v) => v.approved);
                    return approvedReport;
                });

                if (reportWithApprovedVersion) {
                    return { status: "completed" };
                }
            }

            const isRunning = res.some((r) => IN_PROGRESS_STATUSES.includes(r.status as InterviewStatus));

            if (isRunning) {
                return { status: "running" };
            }

            return { status: null };
        } catch (error) {
            this.logger.error(`Error getting status for candidate ${candidateId}`, error);
            throw error;
        }
    }

    async downloadCandidateReport(interviewId: string): Promise<{ base64Report: string }> {
        try {
            this.logger.info(`Generating report for candidate ${interviewId}`);
            const bucketName = getBucketName(this.env);
            const s3Client = new S3Client({ region: process.env.REGION || "us-east-1" });

            const command = new GetObjectCommand({
                Bucket: bucketName,
                Key: `interviews/${interviewId}/report.pdf`,
                ResponseContentType: "application/pdf",
            });

            const response = await s3Client.send(command);
            const report = await response.Body?.transformToString("base64");

            if (!report) {
                throw new Error("No report found");
            }

            return { base64Report: report };
        } catch (error) {
            this.logger.error(`Error generating report for candidate ${interviewId}`, error);
            throw error;
        }
    }

    async checkIfReportExists(candidateId: string): Promise<{ interviewId: string | null }> {
        try {
            const res = await this.db
                .select({ interviewId: interviews.id })
                .from(interviews)
                .where(
                    and(
                        eq(interviews.candidateId, candidateId),
                        eq(interviews.status, "completed"),
                        eq(reportExistsField, true)
                    )
                )
                .limit(1);

            if (res.length === 0) {
                return { interviewId: null };
            }

            return { interviewId: res[0].interviewId };
        } catch (error) {
            this.logger.error(`Error checking if report exists for candidate ${candidateId}`, error);
            throw error;
        }
    }

    async validateCandidatesForInvitation(candidateIds: string[]): Promise<ValidateCandidatesResponse> {
        try {
            this.logger.info(`Validating ${candidateIds.length} candidates for invitation`);

            const validCandidates: string[] = [];
            const invalidCandidates: { candidateId: string; reason: string }[] = [];

            for (const candidateId of candidateIds) {
                const statusResult = await this.getCandidateStatus(candidateId);

                if (!statusResult.status || statusResult.status === "completed" || statusResult.status === "invited") {
                    validCandidates.push(candidateId);
                } else {
                    let reason = "Cannot invite";

                    if (IN_PROGRESS_STATUSES.includes(statusResult.status)) reason = "Interview in progress";

                    invalidCandidates.push({
                        candidateId,
                        reason,
                    });
                }
            }

            this.logger.info(
                `Validation complete: ${validCandidates.length} valid, ${invalidCandidates.length} invalid`
            );

            return {
                validCandidates,
                invalidCandidates,
            };
        } catch (error) {
            this.logger.error(`Error validating candidates for invitation: ${error}`);
            throw error;
        }
    }

    async validateCandidateEmail(email: string, jobId: string): Promise<ValidateEmailResponse> {
        try {
            this.logger.info(`Validating email ${email} for job ${jobId}`);

            // Check if email is valid format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return {
                    isValid: false,
                    message: "Invalid email format",
                };
            }

            // Check if email already exists for this job
            const existingCandidate = await this.db
                .select({ id: candidates.id })
                .from(candidates)
                .where(and(eq(candidates.jobId, jobId), eq(candidates.email, email)))
                .limit(1);

            if (existingCandidate.length > 0) {
                return {
                    isValid: false,
                    message: "A candidate with this email already exists for this job",
                };
            }

            return { isValid: true };
        } catch (error) {
            this.logger.error(`Error validating email ${email} for job ${jobId}: ${error}`);
            throw error;
        }
    }

    async updateCandidate(candidateId: string, updateData: CMUpdateCandidateRequest): Promise<void> {
        try {
            this.logger.info(`Updating candidate ${candidateId}`);

            const updateValues: { [key: string]: string | null } = {};

            if (updateData.firstName || updateData.lastName) {
                const name = `${updateData.firstName || ""} ${updateData.lastName || ""}`.trim();
                if (name) {
                    updateValues.name = name;
                }
            }

            if (updateData.email !== undefined) {
                updateValues.email = updateData.email || null;
            }

            if (updateData.phoneNumber !== undefined) {
                updateValues.phoneNumber = updateData.phoneNumber || null;
            }

            if (updateData.socialId !== undefined) {
                updateValues.socialId = updateData.socialId || null;
            }

            if (Object.keys(updateValues).length === 0) {
                this.logger.warn("No valid update data provided");
                return;
            }

            await this.db.update(candidates).set(updateValues).where(eq(candidates.id, candidateId));

            this.logger.info(`Successfully updated candidate ${candidateId}`);
        } catch (error) {
            this.logger.error(`Error updating candidate ${candidateId}: ${error}`);
            throw error;
        }
    }
}
