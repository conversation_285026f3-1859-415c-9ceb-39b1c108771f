import { APIGatewayProxyResult } from "aws-lambda";
import {
  AnalyzerHandlerEvent,
  getSecret,
  OpenAISecret,
} from "@eva/shared-common";
import { setupLogging, Logger } from "@eva/logger";
import { AnalyzerService } from "./services/AnalyzerService";

export const lambdaHandler = async (
  event: any
): Promise<APIGatewayProxyResult> => {
  try {
    for (const record of event.Records) {
      const event = JSON.parse(record.body) as AnalyzerHandlerEvent;
      const logger: Logger = setupLogging(event?.run_id ?? undefined);
      const openAISecret = await getSecret<OpenAISecret>("openai/apikey");
      const analyzer = new AnalyzerService(logger, openAISecret.API_KEY);
      await analyzer.analyze(event);
    }
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "done",
      }),
    };
  } catch (err: any) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: err.stack }),
    };
  }
};
