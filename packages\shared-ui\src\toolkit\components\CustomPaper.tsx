import styled from "@emotion/styled";
import { Paper, Theme } from "@mui/material";

interface CustomPaperProps {
    theme?: Theme;
    direction?: "row" | "column";
    justify?: "center" | "space-between" | "space-around" | "space-evenly" | "flex-start" | "flex-end";
    align?: "center" | "flex-start" | "flex-end" | "baseline" | "stretch";
    maxHeight?: string ;
}

export const CustomPaper = styled(Paper)<CustomPaperProps>(({ theme, direction, justify, align }) => ({
    height: "100%",
    width: "100%",
    borderRadius: theme.spacing(3),
    display: "flex",
    justifyContent: justify || "center",
    alignItems: align || "center",
    flexDirection: direction || "column",
    padding: theme.spacing(6),
    gap: theme.spacing(4),
}));
