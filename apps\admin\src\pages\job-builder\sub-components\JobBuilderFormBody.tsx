import { <PERSON><PERSON>, Tabs, Tab, Box } from "@mui/material";
import { styled } from '@mui/material/styles';
import { FormikErrors, useField, useFormikContext } from "formik";
import { FC, useEffect, useState, SyntheticEvent } from "react";
import { toast } from "react-toastify";
import { PrimaryButton, SecondaryButton, useAppText } from "@eva/shared-ui";
import { FamilyField } from "./FamilyField";
import { JobBuilderFormBodyGeneralDetails } from "./JobBuilderFormBodyGeneralDetails";
import { JobBuilderOpenQuestions } from "./JobBuilderOpenQuestions";
import { FamilyForBuilder, JobStatus } from "@eva/shared-common";
import { JobBuilderWarmupQuestions } from "./JobBuilderWarmupQuestions";
import { JobBuilderSimulations } from "./JobBuilderSimulations";

const generalRequiredJobDetails = ["language", "title", "customer", "myInterviewJobId"] as const;

const createErrorMessageForFamily = (family: FamilyForBuilder[], id: string, message: string): string => {
    const foundFamily = family.find((f) => f.family_id === id)?.family;
    if (foundFamily) {
        return `${foundFamily.en}-${message}`;
    } else {
        return "";
    }
};

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

const TabsContainer = styled(Box)(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(3),
}));

const TabBarWrapper = styled(Box)(({ theme }) => ({
    borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTabPanelContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(3),
}));

const ActionsContainer = styled(Stack)(({ theme }) => ({
    flexDirection: 'row',
    gap: theme.spacing(3),
    justifyContent: 'flex-end',
    marginTop: theme.spacing(3),
}));

const SubmitButtonWrapper = styled(Stack)({
    width: '200px',
});

const TabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`builder-tabpanel-${index}`}
            aria-labelledby={`builder-tab-${index}`}
            {...other}
        >
            {value === index && <StyledTabPanelContainer>{children}</StyledTabPanelContainer>}
        </div>
    );
};


interface JobBuilderFromBodyProps {
    families: FamilyForBuilder[];
    isUpdatingJob: boolean;
    status: JobStatus;
}

export const JobBuilderFormBody: FC<JobBuilderFromBodyProps> = ({ families, isUpdatingJob, status }) => {
    const { getContentForDisplay } = useAppText();
    const { submitForm, resetForm, errors, setErrors, dirty } = useFormikContext();
    const [accountField] = useField("accountId");
    const [customerField] = useField("customerId");
    const [activeTab, setActiveTab] = useState(0);

    const handleTabChange = (event: SyntheticEvent, newValue: number) => {
        setActiveTab(newValue);
    };

    const familyErrorsKeys = errors
        ? Object.keys(errors).filter((key) => !generalRequiredJobDetails.includes(key as keyof FormikErrors<unknown>))
        : [];

    const otherErrors = errors
        ? Object.keys(errors).filter((key) => generalRequiredJobDetails.includes(key as keyof FormikErrors<unknown>))
        : [];

    useEffect(() => {
        if (familyErrorsKeys.length > 0) {
            const errorMessages = familyErrorsKeys.map((key) => {
                return createErrorMessageForFamily(families, key, errors[key as keyof FormikErrors<unknown>]);
            });

            errorMessages.forEach((message) => {
                toast.error(message);
            });
        }

        if (otherErrors.length > 0) {
            otherErrors.forEach((key) => {
                toast.error(errors[key as keyof FormikErrors<unknown>]);
            });
        }

        setErrors({});
    }, [familyErrorsKeys, otherErrors]);

    const isFormDisabled = !accountField.value || !customerField.value;
    const buttonContentForDisplay = getContentForDisplay("jobBuilderForm.updateJob");

    return (
        <>
            <JobBuilderFormBodyGeneralDetails status={status} />
            <TabsContainer>
                <TabBarWrapper>
                    <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        aria-label="job builder sections"
                        variant="scrollable"
                        scrollButtons="auto"
                    >
                        <Tab label={getContentForDisplay("jobBuilderForm.tabs.startJobQuestions")} />
                        <Tab label={getContentForDisplay("jobBuilderForm.tabs.warmupQuestions")} />
                        <Tab label={getContentForDisplay("jobBuilderForm.tabs.openQuestions")} />
                        <Tab label={getContentForDisplay("jobBuilderForm.tabs.simulations")} />
                    </Tabs>
                </TabBarWrapper>

                <TabPanel value={activeTab} index={0}>
                    <Stack>
                        {families.map((family, index) => (
                            <FamilyField
                                disabled={isFormDisabled}
                                key={family.family_id}
                                index={index}
                                family={family}
                            />
                        ))}
                    </Stack>
                </TabPanel>

                <TabPanel value={activeTab} index={1}>
                    <JobBuilderWarmupQuestions disabled={isFormDisabled} />
                </TabPanel>

                <TabPanel value={activeTab} index={2}>
                    <JobBuilderOpenQuestions disabled={isFormDisabled} />
                </TabPanel>

                <TabPanel value={activeTab} index={3}>
                    <JobBuilderSimulations disabled={isFormDisabled} />
                </TabPanel>
            </TabsContainer>

            <ActionsContainer>
                <Stack>
                    <SecondaryButton
                        content={getContentForDisplay("jobBuilderForm.reset")}
                        onClick={resetForm}
                        disabled={!dirty || isUpdatingJob}
                    />
                </Stack>
                <SubmitButtonWrapper>
                    <PrimaryButton
                        content={buttonContentForDisplay}
                        onClick={submitForm}
                        disabled={!dirty}
                        isLoading={isUpdatingJob}
                    />
                </SubmitButtonWrapper>
            </ActionsContainer>
        </>
    );
};
