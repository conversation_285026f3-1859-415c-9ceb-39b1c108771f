import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useMediaQueryContext } from "@eva/shared-ui";
import { Stack } from "@mui/material";
import { TalkingHeadAnimation } from "../../../../../../../../../components/TalkingHeadAnimation";
import { VideoPlayer } from "../../../../../../../../../components/video-player/VideoPlayer";
import { useInterviewer } from "../../../../../../../InterviewerProvider";
import { MobileQuestionPreview } from "../../../../../../camera-panel/sub-components/MobileQuestionPreview";

interface SimulationStepQuestionContentProps {
    videoUrl: string | null;
    content: string;
    notifyOnVideoEnd: () => void;
    isVideoQuestionPreviewDone: boolean;
}

export const SimulationStepQuestionContent = ({
    content,
    videoUrl,
    notifyOnVideoEnd,
    isVideoQuestionPreviewDone,
}: SimulationStepQuestionContentProps) => {
    const { handleCountDownChange } = useInterviewer();
    const { isMobile } = useMediaQueryContext();
    const onVideoEnd = () => {
        handleCountDownChange(true);
        notifyOnVideoEnd();
    };


    return (
        <>
            <Stack alignItems="center" justifyContent="center" gap="20px" position="relative" paddingInline={10}>
                <>
                    {videoUrl ? (
                        isMobile ? (
                            <MobileQuestionPreview isRecording={false} />
                        ) : (
                            <VideoPlayer
                                autoPlayDisabled={isVideoQuestionPreviewDone}
                                onVideoEnd={onVideoEnd}
                                data-qa="question-video-preview"
                                videoUrl={videoUrl}
                                controls={isVideoQuestionPreviewDone}
                            />
                        )
                    ) : (
                        <TalkingHeadAnimation onMount={isVideoQuestionPreviewDone ? undefined : onVideoEnd} />
                    )}
                </>
                <NoCopyWrapper>
                    <H4 data-qa="question-text-preview">
                        {content}
                    </H4>
                </NoCopyWrapper>
            </Stack>
        </>
    );
};
