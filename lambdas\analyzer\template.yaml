AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
    analyzer

    Sample SAM Template for analyzer

Parameters:
    EnvironmentType:
        Type: String
        Default: staging
        AllowedValues:
            - staging
            - prod
        Description: Environment type
    AnalyzerQueueName:
        Type: String
        Default: staging_analyzer.fifo
        AllowedValues:
            - staging_analyzer.fifo
            - prod_analyzer.fifo
        Description: The name of the SQS queue to which the analyzer will send the interview data
    TargetLanguage:
        Type: String
        Description: The target language for the interview
        Default: "en"
    Bucket:
        Type: String
        Description: The name of the S3 bucket
        Default: analzyer-staging
        AllowedValues:
            - analzyer-staging
            - analyzer-prod
    CloudFrontPrefix:
        Type: String
        Description: The CloudFront URL prefix
        Default: "https://d14be956zuup7c.cloudfront.net"
    InterviewerBucket:
        Type: String
        Description: The name of the S3 bucket
        Default: interviewer-uploads-fc055-staging

Resources:
    AnalyzerQueue:
        Type: AWS::SQS::Queue
        Properties:
            QueueName: !Sub "${EnvironmentType}_analyzer.fifo"
            FifoQueue: true
            VisibilityTimeout: 800
    AnalyzerErrorsTopic:
        Type: AWS::SNS::Topic
        Properties:
            TopicName: !Sub analyzer_${EnvironmentType}_errors
    AnalyzerFinishedInterviewsTopic:
        Type: AWS::SNS::Topic
        Properties:
            TopicName: !Sub analyzer_${EnvironmentType}_finished_interviews

    AnalyzerFunction:
        Type: AWS::Serverless::Function
        Properties:
            FunctionName: !Sub "${EnvironmentType}-analyzer"
            Timeout: 600
            MemorySize: 3008
            EphemeralStorage:
                Size: 2048
            Layers:
                - arn:aws:lambda:us-east-1:************:layer:ffmpeg_layer:7
            CodeUri: src/dist/
            Handler: app.lambdaHandler
            Runtime: nodejs20.x
            Architectures:
                - x86_64
            Events:
                AnalyzerQueue:
                    Type: SQS
                    Properties:
                        Queue: !Sub "arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:${EnvironmentType}_analyzer.fifo"
                        BatchSize: 1
            VpcConfig:
                SecurityGroupIds:
                - sg-0e5ec430bb34861cd
                SubnetIds:
                - subnet-0c7f1790620ba14cd
                - subnet-0dcbafdbc0f986f26
                - subnet-0d04bdd454254e45f
            Environment:
                Variables:
                    CLOUDFRONT_URL: !Ref CloudFrontPrefix
                    TARGET_LANGUAGE: !Ref TargetLanguage
                    VIDEO_FILE_SUFFIX: mp4
                    AUDIO_FILE_SUFFIX: mp3
                    S3_BUCKET: !Ref Bucket
                    ANALYZER_QUEUE_NAME: !Sub "https://sqs.${AWS::Region}.amazonaws.com/${AWS::AccountId}/${EnvironmentType}_analyzer.fifo"
                    ENV: !Ref EnvironmentType
                    ERROR_TOPIC_ARN: !Sub arn:aws:sns:us-east-1:************:analyzer_${EnvironmentType}_errors
                    CLIENT_BASE_URL: !Sub https://${EnvironmentType}.d2trb4neenygq6.amplifyapp.com
                    SUCCESS_TOPIC_ARN: !Sub arn:aws:sns:us-east-1:************:analyzer_${EnvironmentType}_finished_interviews
                    INTERVIEWER_UPLOADS_BUCKET: !Ref InterviewerBucket
                    SHOULD_GRADE_ANSWER_MODEL: ft:gpt-4o-mini-2024-07-18:iverse::AG2h1jFI
                    SHOULD_GRADE_ANSWER_TEMPERATURE: 0
                    SHOULD_GRADE_ANSWER_TOKENS: 5
                    SHOULD_GRADE_ANSWER_TEMPLATE: should_grade_answer_check
                    GRADE_ANSWER_MODEL: ft:gpt-4o-mini-2024-07-18:iverse::AG5lsO43
                    GRADE_ANSWER_TEMPERATURE: 0
                    GRADE_ANSWER_TOKENS: 5
                    GRADE_ANSWER_TEMPLATE: answer_grading
                    CALCULATE_TRAIT_SCORE_MODEL: ft:gpt-4o-mini-2024-07-18:iverse::AG9vXLYL
                    CALCULATE_TRAIT_SCORE_TEMPERATURE: 0
                    CALCULATE_TRAIT_SCORE_TOKENS: 5
                    CALCULATE_TRAIT_SCORE_TEMPLATE: trait_score
                    CALCULATE_TRAIT_SCORE_PRINCIPLE_TEMPLATE: trait_score_grading_principles
                    TRAIT_SUMMARY_EXPLANATION_TEMPLATE: trait_summary_grading_principles_explanation
                    TRAIT_SUMMARY_MODEL: gpt-4o
                    TRAIT_SUMMARY_TEMPERATURE: 0.3
                    TRAIT_SUMMARY_TOKENS: 600
                    TRAIT_SUMMARY_INTRO_COMPOSER_TEMPLATE: trait_summary_intro_composer
                    TRAIT_SUMMARY_INTRO_TEMPLATE: trait_summary_intro
                    TRAIT_SUMMARY_ENDING_TEMPLATE: trait_summary_ending
                    TRAIT_SUMMARY_HEBREW_TEMPLATE: trait_summary
                    TRAIT_SUMMARY_HEBREW_CORRECTION_SYSTEM_TEMPLATE: trait_summary_correction_system
                    FAMILY_SUMMARY_MODEL: gpt-4o
                    FAMILY_SUMMARY_TEMPLATE: families_summary
                    FAMILY_SUMMARY_TEMPERATURE: 0.3
                    FAMILY_SUMMARY_TOKENS: 600
                    FAMILY_SUMMARY_CORRECTION_TEMPLATE: summary_correction
                    FAMILY_SUMMARY_CORRECTION_SYSTEM_TEMPLATE: summary_correction_system
                    FAMILY_SUMMARY_CORRECTION_ENGLISH_TEMPERATURE: 0.5
                    FAMILY_SUMMARY_CORRECTION_ENGLISH_TOKENS: 600
                    FAMILY_SUMMARY_CORRECTION_HEBREW_TEMPERATURE: 0.2
                    FAMILY_SUMMARY_CORRECTION_HEBREW_TOKENS: 600
                    FAMILY_SUMMARY_LANGUAGE_GENDER_CORRECTION_TEMPLATE: language_gender_correction
                    FAMILY_SUMMARY_GENDER_CORRECTION_SYSTEM_CONTENT: You are a perfect hebrew gender corrector
                    FAMILY_SUMMARY_GENDER_CORRECTION_TEMPERATURE: 0
                    FAMILY_SUMMARY_GENDER_CORRECTION_TOKENS: 1000
                    VERBAL_ABILITY_TEMPLATE: verbal_ability
                    VERBAL_ABILITY_TRAIT_ID: A38
                    VERBAL_ABILITY_MODEL: o1
                    VERBAL_ABILITY_TEMPERATURE: 0
                    VERBAL_ABILITY_TOKENS: 2
                    JUDGE_TEMPLATE: judge
                    JUDGE_MODEL: o3-mini
            Policies:
                Version: '2012-10-17'
                Statement:
                    - Sid: AWSLambdaVPCAccessExecutionPermissions
                      Effect: Allow
                      Action:
                          - logs:CreateLogGroup
                          - logs:CreateLogStream
                          - logs:PutLogEvents
                          - ec2:CreateNetworkInterface
                          - ec2:DescribeNetworkInterfaces
                          - ec2:DescribeSubnets
                          - ec2:DeleteNetworkInterface
                          - ec2:AssignPrivateIpAddresses
                          - ec2:UnassignPrivateIpAddresses
                      Resource: '*'
                    - Effect: Allow
                      Action:
                          - sqs:DeleteMessage
                          - sqs:ReceiveMessage
                          - sqs:GetQueueAttributes
                          - sqs:GetQueueUrl
                      Resource: "*"
                    - Effect: Allow
                      Action:
                          - logs:CreateLogGroup
                          - logs:CreateLogStream
                          - logs:PutLogEvents
                      Resource: !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${EnvironmentType}-interview-handler:*"
                    - Effect: Allow
                      Action:
                          - secretsmanager:GetSecretValue
                      Resource:
                          - !Sub arn:aws:secretsmanager:us-east-1:************:secret:${EnvironmentType}/iverse-db/postgres-*
                          - !Sub arn:aws:secretsmanager:us-east-1:************:secret:${EnvironmentType}/iverse-db-*
                          - arn:aws:secretsmanager:us-east-1:************:secret:openai/apikey-jHMFY4
                    - Effect: Allow
                      Action:
                          - s3:*
                      Resource:
                          - "arn:aws:s3:::analyzer-*/*"
                          - "arn:aws:s3:::analzyer-staging/*"
                          - "arn:aws:s3:::interviewer-dev133751-dev/*"
                          - "arn:aws:s3:::interviewer-uploads-fc055-staging/*"
                    - Effect: Allow
                      Action: sns:Publish
                      Resource: !Ref AnalyzerErrorsTopic
                    - Effect: Allow
                      Action: sns:Publish
                      Resource: !Ref AnalyzerFinishedInterviewsTopic
                      

    ApplicationResourceGroup:
        Type: AWS::ResourceGroups::Group
        Properties:
            Name:
                Fn::Sub: ApplicationInsights-SAM-${AWS::StackName}
            ResourceQuery:
                Type: CLOUDFORMATION_STACK_1_0
    ApplicationInsightsMonitoring:
        Type: AWS::ApplicationInsights::Application
        Properties:
            ResourceGroupName:
                Ref: ApplicationResourceGroup
            AutoConfigurationEnabled: "true"

Outputs:
    AnalyzerFunction:
        Description: Analyzer Lambda Function ARN
        Value: !GetAtt AnalyzerFunction.Arn
    AnalyzerFunctionIamRole:
        Description: Implicit IAM Role created for Analyzer function
        Value: !GetAtt AnalyzerFunctionRole.Arn