import { CreateJobRequest, FamilyForBuilder, JobDetailsDataResponse, UpdateJobRequest } from "@eva/shared-common";
import { Formik } from "formik";
import { FC } from "react";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { useUpdateJobMutation } from "../../../app-state/apis";
import { BackdropLoading, Language } from "@eva/shared-ui";
import { JobBuilderFormBody } from "./JobBuilderFormBody";

const extractQuestionIds = (families: FamilyForBuilder, questionIds: string[]): string[] => {
    const familyQuestions = families.questions || [];

    return questionIds.filter((id) => familyQuestions.some((question) => question.question_id === id));
};

const createForInitialValues = (jobData: JobDetailsDataResponse, families: FamilyForBuilder[]): UpdateJobRequest => ({
    warmupQuestions: jobData.warmupQuestions || [],
    computedTraits: jobData.computedTraits || [],
    jobId: jobData.jobId,
    A46: extractQuestionIds(families.find((f) => f.family_id === "A46")!, jobData.questionIds),
    A47: extractQuestionIds(families.find((f) => f.family_id === "A47")!, jobData.questionIds),
    A48: extractQuestionIds(families.find((f) => f.family_id === "A48")!, jobData.questionIds),
    A49: extractQuestionIds(families.find((f) => f.family_id === "A49")!, jobData.questionIds),
    openQuestions: jobData.openQuestions,
    customerId: jobData.customerId,
    description: jobData.description,
    title: jobData.title,
    language: jobData.language as Language,
    accountId: jobData.accountId,
    simulations: jobData.simulations || [],
});

interface JobBuilderFormProps {
    families: FamilyForBuilder[];
    jobData: JobDetailsDataResponse;
}

export const JobBuilderForm: FC<JobBuilderFormProps> = ({ families, jobData }) => {
    const [updateJob, { isLoading: isUpdatingJob }] = useUpdateJobMutation();

    const initialValues = createForInitialValues(jobData, families);

    const familySchemas = families.reduce((acc, curr) => {
        acc[curr.family_id] = Yup.array()
            .of(Yup.string().required("Question is required"))
            .min(1, "Minimum of one question required")
            .required("Questions are required");
        return acc;
    }, {} as Record<string, any>);

    const validationSchema = Yup.object().shape({
        computedTraits: Yup.array().of(Yup.string()),
        A46: familySchemas["A46"],
        A47: familySchemas["A47"],
        A48: familySchemas["A48"],
        A49: familySchemas["A49"],
        warmupQuestions: Yup.array().of(Yup.string()),
        language: Yup.string().required("Language is required"),
        title: Yup.string().required("Title is required"),
        customerId: Yup.string().required("Customer is required"),
        openQuestions: Yup.array().of(Yup.string()),
        description: Yup.string(),
        accountId: Yup.string().required("Account is required"),
        introVideo: Yup.string(),
        simulations: Yup.array().of(Yup.string()),
    });

    const onSubmit = async (values: CreateJobRequest | UpdateJobRequest) => {
        if (values.jobId) {
            try {
                await updateJob(values as UpdateJobRequest).unwrap();
                toast.success("Job updated successfully");
            } catch (e) {
                toast.error("Error updating job");
            }
        }
    };

    return (
        <Formik
            validateOnBlur={false}
            validateOnMount={false}
            validateOnChange={false}
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
        >
            {isUpdatingJob ? (
                <BackdropLoading isLoading />
            ) : (
                <JobBuilderFormBody isUpdatingJob={isUpdatingJob} families={families} status={jobData.status} />
            )}
        </Formik>
    );
};
