import { Text } from "@react-pdf/renderer";
import { stylesPDF as styles } from "../../../../PDF.styles";

interface ZeroScoreExplanationProps {
    isRtl: boolean;
}

export const ZeroScoreExplanation = ({ isRtl }: ZeroScoreExplanationProps) => {
    const zeroScoreExplanation = isRtl
        ? "לא ניתן להעריך את התכונה על סמך התשובה שסופקה *"
        : "* The trait could not be assessed based on the provided answer.";

    return (
        <Text style={[isRtl ? styles.hebrewTextBoldSubTitle : styles.textBoldSubTitle, isRtl ? styles.alignTextRight : {}]}>
            {zeroScoreExplanation}
        </Text>
    );
};
