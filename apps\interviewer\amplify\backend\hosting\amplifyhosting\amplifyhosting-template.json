{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Linux\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.13.0\",\"stackType\":\"hosting-amplifyhosting\",\"metadata\":{\"whyContinueWithGen1\":\"I am a current Gen 1 user\"}}", "Parameters": {"env": {"Type": "String"}, "appId": {"Type": "String"}, "type": {"Type": "String"}}, "Conditions": {"isManual": {"Fn::Equals": [{"Ref": "type"}, "manual"]}}, "Resources": {"AmplifyBranch": {"Condition": "<PERSON><PERSON><PERSON><PERSON>", "Type": "AWS::Amplify::Branch", "Properties": {"BranchName": {"Ref": "env"}, "AppId": {"Ref": "appId"}}}}}