node_modules/
node_modules
**/node_modules/
.pnp
.pnp.js

# testing
coverage

# build outputs
.next/
out/
build
dist
.turbo

# Misc
.DS_Store
*.pem
.idea
.vscode/*
!.vscode/extensions.json

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Turbo
.turbo

# build artifacts
*.tsbuildinfo
.nx/cache
.nx/workspace-data

# Amplify
#current-cloud-backend/
**/\#current-cloud-backend/
