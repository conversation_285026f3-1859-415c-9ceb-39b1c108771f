import { Interviewer<PERSON>ob<PERSON><PERSON>, InterviewerJobStep, Language } from "@eva/shared-common";
import { BackdropLoading, fetchBlob, useAppText, useAppTheme } from "@eva/shared-ui";
import { uploadData } from "aws-amplify/storage";
import { createContext, ReactNode, useCallback, useContext, useEffect, useLayoutEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    useGetCurrentStepQuery,
    useGetInterviewSetupQuery,
    useStartInterviewMutation,
    useSubmitAnswerMutation,
} from "../../app-state/store/apis/interviewApi";
import { SimulationScreen } from "../interviewer/sub-components/interview-panel/sub-components/stepper/sub-components/simulation-step/SimulationStep";

const getQuestionIdFromStep = (stepData: InterviewerJobStep | null | undefined): string | undefined => {
    if (!stepData) {
        return undefined;
    }

    switch (stepData.type) {
        case "general":
        case "warmup":
        case "open":
            return stepData.question.id;
        case "simulation":
            return stepData.simulation?.question.id;
        default:
            console.error("Unknown step type in getQuestionIdFromStep:", stepData);
            return undefined;
    }
};

const parentAddresses = [
    "http://localhost:61564",
    "http://localhost:61564/Testee/TinCan/ToolList",
    "https://new.intercat.co.il",
];

declare global {
    interface Window {
        endVideo?: () => void;
        currentSimulationScreen: SimulationScreen;
        setCurrentSimulationScreen: (screen: SimulationScreen) => void;
    }
}

interface InterviewContextValue {
    interviewId: string | undefined;
    isWelcomeVideoDone: boolean;
    isCountingDown: boolean;
    isSubmittingInterview: boolean;
    isUploading: boolean;
    isError: boolean;
    isInterviewOn: boolean;
    isTrainingOn: boolean;
    hasSetup: boolean;
    isLoading: boolean;
    data: InterviewerJobData | undefined;
    companyName: string;
    logoLink: string | null;
    interviewWelcomeVideo: string | null;
    currentStep: InterviewerJobStep | undefined | null;
    trainingRecordingUrl: string;
    currentSimulationScreen: SimulationScreen;
    onWelcomeVideoEnd: () => void;
    handleRecordingStoppage: (mediaBlobUrl: string) => void;
    handleSetupChange: (connectionState: boolean) => void;
    handleInterviewStart: () => void;
    handleInterviewStartTraining: () => void;
    finishTraining: () => void;
    handleCountDownChange: (countDownState: boolean) => void;
    setCurrentSimulationScreen: (screen: SimulationScreen) => void;
}

const InterviewContext = createContext<InterviewContextValue | undefined>(undefined);

interface InterviewProviderProps {
    children: ReactNode;
}

export const InterviewerProvider: React.FC<InterviewProviderProps> = ({ children }) => {
    const { changeLanguage } = useAppText();
    const { changeDirection, changeMainColor } = useAppTheme();
    const [submitAnswer, { isLoading: isSubmittingInterview }] = useSubmitAnswerMutation();
    const { interviewId } = useParams();
    const [startInterview] = useStartInterviewMutation();

    const [isWelcomeVideoDone, setIsWelcomeVideoDone] = useState<boolean>(false);
    const [isInterviewOn, setIsInterviewOn] = useState<boolean>(false);
    const [isCountingDown, setIsCountingDown] = useState<boolean>(false);
    const [isTrainingOn, setIsTrainingOn] = useState<boolean>(false);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const [trainingRecordingUrl, setTrainingRecordingUrl] = useState<string>("");
    const [hasSetup, setHasSetup] = useState(false);
    const [previousSimulationId, setPreviousSimulationId] = useState<string | null>(null);
    const [currentSimulationScreen, setCurrentSimulationScreen] = useState<SimulationScreen>("explanation");

    const {
        data: stepData,
        isLoading: isStepLoading,
        refetch: refetchStep,
    } = useGetCurrentStepQuery(interviewId!, {
        skip: !interviewId,
    });
    const {
        data: interviewData,
        isError,
        isLoading: isInterviewLoading,
        error,
    } = useGetInterviewSetupQuery(interviewId!, {
        skip: !interviewId || stepData === null || !stepData,
    });

    const logoLink = interviewData ? interviewData.logoLink : "";
    const companyName = interviewData ? interviewData.customerName : "";
    const interviewWelcomeVideo = interviewData ? interviewData.welcomeVideoLink : "";

    const handleTrainingRecordingStoppage = async (mediaBlobUrl: string) => {
        setTrainingRecordingUrl(mediaBlobUrl);
    };

    const finishTraining = (): void => {
        setIsTrainingOn(false);
        setTrainingRecordingUrl("");
    };

    const onWelcomeVideoEnd = (): void => {
        setIsWelcomeVideoDone(true);
    };

    const handleUpload = async (mediaBlobUrl: string): Promise<string> => {
        let blob: Blob | null = null;
        if (!interviewData || !stepData) throw new Error("Interview data or step data not found");

        const questionId = getQuestionIdFromStep(stepData);

        if (questionId === undefined) {
            console.error("Could not determine question ID for upload key generation", stepData);
            throw new Error("Could not determine question ID");
        }

        const key = `${interviewData.interviewId}/${questionId}.mp4`;
        const maxRetries = 3;
        let attempt = 0;

        try {
            blob = await fetchBlob(mediaBlobUrl);

            while (attempt < maxRetries) {
                try {
                    await uploadData({
                        key,
                        data: blob,
                        options: {
                            contentType: "video/mp4",
                        },
                    }).result;
                    break;
                } catch (error) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        throw error;
                    }
                }
            }
        } catch (error) {
            console.error("Error uploading media", error);
            throw error;
        } finally {
            if (blob) {
                URL.revokeObjectURL(mediaBlobUrl);
                blob = null;
            }
        }
        return "public/" + key;
    };

    const handleRecordingStoppage = async (mediaBlobUrl: string): Promise<void> => {
        if (isTrainingOn) {
            handleTrainingRecordingStoppage(mediaBlobUrl);
            return;
        }

        setIsUploading(true);
        if (!interviewData || !interviewId || !stepData) return;

        try {
            const key = await handleUpload(mediaBlobUrl);

            await handleSubmitAnswer(key);
        } catch (error) {
            console.error("Error processing recording:", error);
        } finally {
            setIsUploading(false);
        }
    };

    useLayoutEffect(() => {
        if (interviewData) {
            changeLanguage(interviewData.language as Language);
            changeDirection(interviewData.language === "he" ? "rtl" : "ltr");
            if (interviewData.themeColor) changeMainColor(interviewData.themeColor);
        }
    }, [interviewData]);

    useEffect(() => {
        const currentStep = stepData;

        if (currentStep === null) {
            if (window.parent) {
                parentAddresses.forEach((address) => {
                    window.parent.postMessage("endInterview", address);
                });
            } else {
                console.warn("Unable to send message to the parent window.");
            }
        }

        const stepType = currentStep?.type;

        if (currentStep && stepType === "simulation") {
            if (currentStep.simulation?.id) {
                if (previousSimulationId === currentStep.simulation.id) {
                    setCurrentSimulationScreen("question");
                }
            } else {
                setPreviousSimulationId(null);
                setCurrentSimulationScreen("explanation");
            }
        } else {
            setPreviousSimulationId(null);
            setCurrentSimulationScreen("explanation");
        }
    }, [stepData, previousSimulationId]);

    const handleSetupChange = useCallback((connectionState: boolean): void => {
        setHasSetup(connectionState);
    }, []);

    const handleInterviewStart = async (): Promise<void> => {
        if (isTrainingOn) {
            finishTraining();
        }
        setIsInterviewOn(true);
        await startInterview(interviewId!).unwrap();
    };

    const handleInterviewStartTraining = (): void => {
        setIsTrainingOn(true);
    };

    const handleCountDownChange = (countDownState: boolean): void => {
        setIsCountingDown(countDownState);
    };

    const handleSubmitAnswer = async (key: string) => {
        if (!stepData || !interviewId) return;

        const questionId = getQuestionIdFromStep(stepData);

        if (questionId === undefined) {
            console.error("Could not determine question ID for submission", stepData);
            setIsUploading(false);
            return;
        }

        if (stepData.type === "simulation") {
            setPreviousSimulationId(stepData.simulation.id);
        }

        try {
            setIsUploading(true);
            await submitAnswer({
                interviewId,
                questionId,
                key: key,
            }).unwrap();
            await refetchStep();
        } catch (error) {
            console.error("Error submitting answer:", error);
        } finally {
            setIsUploading(false);
        }
    };

    const isLoading = isStepLoading || isInterviewLoading;

    return (
        <InterviewContext.Provider
            value={{
                currentStep: stepData!,
                interviewId,
                isWelcomeVideoDone,
                isCountingDown,
                data: interviewData,
                isError,
                logoLink,
                hasSetup,
                isLoading,
                companyName,
                isUploading,
                isTrainingOn,
                isInterviewOn,
                trainingRecordingUrl,
                interviewWelcomeVideo,
                isSubmittingInterview,
                finishTraining,
                onWelcomeVideoEnd,
                handleSetupChange,
                handleInterviewStart,
                handleCountDownChange,
                handleRecordingStoppage,
                handleInterviewStartTraining,
                currentSimulationScreen,
                setCurrentSimulationScreen,
            }}
        >
            {(isInterviewLoading || isStepLoading) && !interviewData ? <BackdropLoading isLoading={true} /> : children}
        </InterviewContext.Provider>
    );
};

export const useInterviewer = () => {
    const context = useContext(InterviewContext);
    if (context === undefined) {
        throw new Error("useInterview must be used within an InterviewProvider");
    }
    return context;
};
