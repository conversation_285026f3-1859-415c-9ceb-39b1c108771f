import React, { <PERSON> } from "react";
import { CloudsSVGIcon } from "./CloudsSVGIcon";
import { PerformanceSVGIcon } from "./PerformanceSVGIcon";
import { InterpersonalSVGIcon } from "./InterpersonalSVGIcon";
import { FrameworkSVGIcon } from "./FrameworkSVGIcon";

export interface FamilySVGIconProps {
    familyCode:string
    size: string
}

export const FamilySVGIcon: FC<FamilySVGIconProps> = ({ familyCode, size }) => {
    if (familyCode === "A46") return <CloudsSVGIcon size={size} />;
    if (familyCode === "A47") return <PerformanceSVGIcon size={size} />;
    if (familyCode === "A48")
        return <InterpersonalSVGIcon size={size} />;
    if (familyCode === "A49") return <FrameworkSVGIcon size={size} />;
    return <></>;
};
