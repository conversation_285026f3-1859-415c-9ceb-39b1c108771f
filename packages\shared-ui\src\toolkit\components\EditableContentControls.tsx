import React, { FC, useEffect } from "react";
import { useAppText } from "../context/TextContentProvider";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import { PrimaryButton, SecondaryButton } from "./buttons";
import { useFormikContext } from "formik";
import { Stack } from "@mui/material";
import { toast } from "react-toastify";

interface EditableContentControlsProps {
    isEditMode: boolean;
    handleEditButtonClick: () => void;
}

export const EditableContentControls: FC<EditableContentControlsProps> = ({ handleEditButtonClick, isEditMode }) => {
    const { currentLanguage } = useAppText();
    const { submitForm, errors, setErrors } = useFormikContext();

    useEffect(() => {
        if (errors) {
            for (const error in errors) {
                toast.error((errors as Record<string, string>)[error]);
            }
        }
        setErrors({});
    }, [errors]);

    return !isEditMode ? (
        <PrimaryButton
            width='fit-content'
            onClick={handleEditButtonClick}
            endIcon={<EditIcon />}
            content={currentLanguage === "he" ? "עריכה" : "Edit"}
        />
    ) : (
        <Stack direction='row' gap={2}>
            <PrimaryButton
                endIcon={<SaveIcon />}
                width='fit-content'
                content={currentLanguage === "he" ? "שמירה" : "Save"}
                onClick={submitForm}
            />
            <SecondaryButton endIcon={<CancelIcon />} onClick={handleEditButtonClick} content={currentLanguage === "he" ? "ביטול" : "Cancel"} />
        </Stack>
    );
};
