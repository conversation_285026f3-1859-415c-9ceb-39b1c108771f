import { Stack } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { Body1, BorderlessCard, H5 } from "@eva/shared-ui";

const BadRequestPage = () => {
    return (
        <Stack height="100vh" justifyContent="center" alignItems="center" data-qa="bad-request-message-container">
            <BorderlessCard>
                <Stack alignItems="center" spacing={2} padding={4}>
                    <ErrorOutlineIcon color="error" fontSize="large" />
                    <H5 textAlign="center">Page Not Found</H5>
                    <Body1 color="error" textAlign="center">
                        The requested page could not be found.
                    </Body1>
                </Stack>
            </BorderlessCard>
        </Stack>
    );
};

export default BadRequestPage;
