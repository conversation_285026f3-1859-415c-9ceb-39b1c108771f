import React, { <PERSON> } from "react";
import { Subtitle2 } from "../theme/Typography";
import { Stack } from "@mui/material";

interface ContentCellProps {
    icon?: React.ReactNode;
    text: string;
}

export const ContentCell: FC<ContentCellProps> = ({ text, icon }) => {
    return (
        <Stack
            flexDirection="row"
            alignItems="center"
            gap={(theme) => theme.spacing(2)}
            justifyContent="center"
        >
            <>
                {icon}
                <Subtitle2>{text}</Subtitle2>
            </>
        </Stack>
    );
};
