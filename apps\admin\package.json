{"name": "@eva/admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-amplify/ui-react": "^6.1.6", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.0.12", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.16.7", "@mui/x-data-grid": "^7.8.0", "@mui/x-date-pickers": "^7.27.3", "@react-pdf/renderer": "^3.4.4", "@reduxjs/toolkit": "^2.2.3", "aws-amplify": "^6.0.25", "crypto": "^1.0.1", "date-fns": "^4.1.0", "formik": "^2.4.6", "html2canvas": "^1.4.1", "lodash": "^4.17.16", "node-rsa": "^1.1.1", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-redux": "^9.1.0", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-toastify": "^10.0.5", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "yup": "^1.4.0", "@eva/shared-ui": "workspace:*", "@eva/shared-common": "workspace:*", "uuid": "^10.0.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/stylis": "^4.2.5", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "typescript": "^5.4.5", "vite": "^6.2.3", "@types/uuid": "^10.0.0"}}