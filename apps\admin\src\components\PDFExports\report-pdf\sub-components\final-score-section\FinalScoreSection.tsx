import { View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";
import { FinalScoreSectionHeader } from "./sub-components/FinalScoreSectionHeader";
import { FinalScoreSectionPerformanceTable } from "./sub-components/perfromance-table/FinalScoreSectionPerformanceTable";
import { FamiliesWithTraitsForDisplayMap, ReportVersion} from "@eva/shared-common";

interface FinalScoreSectionProps {
    isRtl: boolean;
    reportData: ReportVersion;
    language: string;
    familiesMapForDisplay: FamiliesWithTraitsForDisplayMap;
}

export const FinalScoreSection: FC<FinalScoreSectionProps> = ({ isRtl, reportData, language, familiesMapForDisplay }) => {
    return (
        <View style={[styles.sectionContainer]}>
            <FinalScoreSectionHeader isRtl={isRtl} finalScore={reportData.interview_score} />
            <FinalScoreSectionPerformanceTable
                isRtl={isRtl}
                familiesMapForDisplay={familiesMapForDisplay}
                language={language}
                families={reportData.families}
            />
        </View>
    );
};
