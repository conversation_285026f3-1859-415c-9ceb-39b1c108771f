import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import axios from "axios";
import {
    AdditionalQuestion,
    AdditionalQuestionsResponse,
    AnalyzerBucketUrlSecret,
    AnalyzerHandlerEvent,
    createLambdaResponseToClientAction,
    FamiliesWithTraitsForDisplayMap,
    getBucketName,
    getSecret,
    InterviewQuestionAnswer,
    InterviewReportData,
    InterviewResponse,
    InterviewsPageFilterOptionsResponse,
    InterviewsPageTableDataRequest,
    InterviewsPageTableDataResponse,
    isThereDiffsInReport,
    LambdaResponseToClientAction,
    LanguageVariation,
    MyInterviewEvent,
    MyInterviewHeaders,
    PDFReportRequest,
    PilatSecrets,
    ReportVersion,
    RerunInterviewSecret,
    ReviewStatus,
    WarmupQuestionsAnswersResponse,
} from "@eva/shared-common";
import {
    accounts,
    answers,
    candidates,
    customers,
    DB,
    interviewIdMappings,
    interviews,
    jobs,
    traits,
    traitsFamilies,
    and,
    asc,
    desc,
    eq,
    like,
    ne,
    sql,
    SQL<PERSON>rapper,
} from "@eva/drizzle";
import { generateCloudfrontSignedUrl, CloudfrontAccessPair } from "@eva/aws-utils";
import { ServiceBaseResources } from "./types";
import { Logger } from "@eva/logger";

export class InterviewService extends ServiceBaseResources {
    analyzerQueueUrl: string;
    region: string;
    cloudfrontSignedUrlSecret: CloudfrontAccessPair;
    env: string = process.env.ENV || "staging";
    constructor(db: DB, logger: Logger, cloudfrontSignedUrlSecret: CloudfrontAccessPair) {
        super(db, logger);
        this.analyzerQueueUrl = process.env.ANALYZER_QUEUE_URL as string;
        this.region = process.env.REGION as string;
        this.cloudfrontSignedUrlSecret = cloudfrontSignedUrlSecret;
    }

    async getAdditionalQuestions(interviewId: string): Promise<AdditionalQuestionsResponse> {
        const res = await this.db
            .select({
                answer: answers.answer,
                jobData: jobs.questions,
            })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .innerJoin(answers, eq(interviews.id, answers.interviewId))
            .where(eq(interviews.id, interviewId));

        if (res.length === 0) return { additionalQuestionsAnswers: [] };

        const openQuestions = res[0].jobData?.openQuestions || [];
        const customQuestionsAnswers: AdditionalQuestion[] = [];

        for (const item of res as { answer: InterviewQuestionAnswer }[]) {
            if (item.answer.question_id === null && openQuestions.includes(item.answer.question)) {
                const idx = openQuestions.indexOf(item.answer.question);
                customQuestionsAnswers[idx] = {
                    question: item.answer.question,
                    video_link: await generateCloudfrontSignedUrl(
                        item.answer.video_link,
                        3600,
                        this.cloudfrontSignedUrlSecret
                    ),
                };
            }
        }

        return {
            additionalQuestionsAnswers: customQuestionsAnswers,
        };
    }

    async getFamiliesWithTraitsMap(): Promise<FamiliesWithTraitsForDisplayMap> {
        try {
            const traitsWithFamilies = await this.db
                .select({
                    id: traits.id,
                    familyId: traits.familyId,
                    trait: traits.trait,
                    family: traitsFamilies.family,
                })
                .from(traits)
                .leftJoin(traitsFamilies, eq(traits.familyId, traitsFamilies.id));

            const familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap = {};

            for (const trait of traitsWithFamilies) {
                const familyId = trait.familyId;
                const family = trait.family as LanguageVariation;
                const traitData = trait.trait as LanguageVariation;

                if (!familyId || !family) continue;

                if (!familiesWithTraitsMap[familyId]) {
                    familiesWithTraitsMap[familyId] = {
                        family,
                        traitsMap: {},
                    };
                }

                familiesWithTraitsMap[familyId].traitsMap[trait.id] = {
                    trait: traitData,
                };
            }

            return familiesWithTraitsMap;
        } catch (error) {
            console.error(`Error fetching traits with families: ${error}`);
            throw error;
        }
    }

    async getInterviewById(interviewId: string): Promise<InterviewResponse> {
        this.logger.info("Getting interview", { interviewId });

        const [interviewDetails, familiesWithTraitsForDisplayMap] = await Promise.all([
            this.db
                .select({
                    interviewDate: interviews.createdAt,
                    candidateName: candidates.name,
                    email: candidates.email,
                    candidateGender: candidates.gender,
                    candidateImage: candidates.imageLink,
                    jobTitle: jobs.jobTitle,
                    socialId: candidates.socialId,
                    language: jobs.language,
                    customer: customers.companyName,
                    accountId: accounts.id,
                    report: interviews.analyzerReport,
                    companyName: customers.companyName,
                })
                .from(interviews)
                .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .innerJoin(customers, eq(jobs.customerId, customers.id))
                .innerJoin(accounts, eq(customers.accountId, accounts.id))
                .where(eq(interviews.id, interviewId)),
            this.getFamiliesWithTraitsMap(),
        ]);

        if (interviewDetails.length === 0) throw new Error("Interview not found");

        const interviewReportsData = interviewDetails[0].report as InterviewReportData;
        const latestReport = interviewReportsData.report_versions[interviewReportsData.report_versions.length - 1];
        const originalReport =
            interviewReportsData.report_versions.length > 1 ? interviewReportsData.report_versions[0] : undefined;
        const reportEditSession = interviewReportsData.report_edit_session;

        const { interviewDate, report: _, ...rest } = interviewDetails[0];

        return {
            interview: {
                ...rest,
                interviewDate: interviewDate.toISOString(),
                interviewId,
            },
            familiesWithTraitsMap: familiesWithTraitsForDisplayMap,
            report: latestReport,
            originalReport: originalReport || undefined,
            reportEditSession: reportEditSession || undefined,
        };
    }

    setReviewStatus(interviewReportData: InterviewReportData | null): ReviewStatus {
        if (!interviewReportData) return "not_available";
        if (interviewReportData.report_edit_session) return "in_progress";
        if (!interviewReportData.report_edit_session && interviewReportData.report_versions.length > 1)
            return "complete";
        else return "not_started";
    }

    async getInterviewsPageTableData(params: InterviewsPageTableDataRequest): Promise<InterviewsPageTableDataResponse> {
        this.logger.info("Getting interviews page table data", { params });
        const env = process.env.ENV || "dev";
        const bucketSecret = await getSecret<AnalyzerBucketUrlSecret>(`${env}/bucketUrlPrefixAndSuffix`);

        this.logger.info("Bucket secret", { bucketSecret });
        this.logger.info(`Params: ${JSON.stringify(params)}`);

        const reviewStatusField = sql<string>`
        CASE 
          WHEN ${interviews.analyzerReport} IS NULL THEN 'not_available'
          WHEN ${interviews.analyzerReport}->>'report_edit_session' IS NOT NULL THEN 'in_progress'
          WHEN ${interviews.analyzerReport}->>'report_edit_session' IS NULL AND 
               json_array_length(${interviews.analyzerReport}->'report_versions') > 1 THEN 'complete'
          ELSE 'not_started'
          END
          `;

        const scoreField = sql<number>`COALESCE((${interviews.analyzerReport}->'report_versions'->-1->>'interview_score')::numeric, 0)`;

        const conditions = [];
        if (params?.filter) {
            const { searchText, jobTitle, company, account, reviewStatus } = params.filter;

            if (searchText) {
                conditions.push(like(candidates.name, `%${searchText}%`));
            }

            if (jobTitle) {
                conditions.push(eq(jobs.jobTitle, jobTitle));
            }

            if (company) {
                conditions.push(eq(customers.companyName, company));
            }

            if (account) {
                conditions.push(eq(accounts.name, account));
            }

            if (reviewStatus) {
                conditions.push(eq(reviewStatusField, reviewStatus));
            }
        }

        const { field, sort } = params.sort;

        const sortFieldsMap = {
            candidateName: candidates.name,
            email: candidates.email,
            socialId: candidates.socialId,
            jobTitle: jobs.jobTitle,
            companyName: customers.companyName,
            accountName: accounts.name,
            interviewDate: interviews.createdAt,
            language: jobs.language,
            globalScore: scoreField,
        } as Record<string, SQLWrapper>;

        this.logger.info(`Sort fields map: ${sortFieldsMap[field]}`);
        this.logger.info(`Sort: ${sort}`);

        const { page, pageSize } = params.pagination;

        const [res, countRes] = await Promise.all([
            this.db
                .select({
                    candidateId: candidates.id,
                    candidateName: candidates.name,
                    email: candidates.email,
                    socialId: candidates.socialId,
                    jobId: jobs.id,
                    jobTitle: jobs.jobTitle,
                    jobLanguage: jobs.language,
                    companyId: customers.id,
                    companyName: customers.companyName,
                    companyLogo: customers.logoLink,
                    accountName: accounts.name,
                    interviewId: interviews.id,
                    interviewCreatedAt: interviews.createdAt,
                    interviewRunId: interviews.runId,
                    interviewStatus: interviews.status,
                    interviewEvent: interviews.event,
                    interviewReport: interviews.analyzerReport,
                    reviewStatus: reviewStatusField,
                    score: scoreField,
                })
                .from(candidates)
                .innerJoin(interviews, eq(candidates.id, interviews.candidateId))
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .innerJoin(customers, eq(jobs.customerId, customers.id))
                .innerJoin(accounts, eq(customers.accountId, accounts.id))
                .where(conditions.length > 0 ? and(...conditions) : undefined)
                .orderBy(sort === "asc" ? asc(sortFieldsMap[field]) : desc(sortFieldsMap[field]))
                .limit(pageSize)
                .offset(page * pageSize),
            this.db
                .select({ count: sql<number>`count(*)` })
                .from(candidates)
                .innerJoin(interviews, eq(candidates.id, interviews.candidateId))
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .innerJoin(customers, eq(jobs.customerId, customers.id))
                .innerJoin(accounts, eq(customers.accountId, accounts.id))
                .where(conditions.length > 0 ? and(...conditions) : undefined),
        ]);

        const totalCount = Number(countRes[0].count);

        let interviewData = res.map((interview: any) => {
            if (interview.interviewReport) {
                const reportData = interview.interviewReport;
                const originalReport =
                    reportData.report_versions.length > 0 ? reportData.report_versions[0] : undefined;
                const reviewedReport =
                    reportData.report_versions.length > 1
                        ? reportData.report_versions[reportData.report_versions.length - 1]
                        : undefined;
                const isThereDiffs =
                    originalReport !== undefined && reviewedReport !== undefined
                        ? isThereDiffsInReport(originalReport, reviewedReport)
                        : false;

                return {
                    bucketUrl: `${bucketSecret.prefix}${interview.interviewId}${bucketSecret.suffix}`,
                    runId: interview.interviewRunId,
                    candidateName: interview.candidateName,
                    id: interview.interviewId,
                    companyName: interview.companyName,
                    jobTitle: interview.jobTitle,
                    email: interview.email,
                    socialId: interview.socialId,
                    language: interview.jobLanguage,
                    interviewDate: interview.interviewCreatedAt.toISOString(),
                    reviewStatus: interview.reviewStatus as ReviewStatus,
                    accountName: interview.accountName,
                    evaluationStatus: interview.interviewStatus,
                    isThereDiffsInReport: isThereDiffs,
                    globalScore: Number(interview.score),
                    retryDisabled: interview.interviewEvent === null,
                };
            } else {
                return {
                    retryDisabled: interview.interviewEvent === null,
                    bucketUrl: `${bucketSecret.prefix}${interview.interviewId}${bucketSecret.suffix}`,
                    runId: interview.interviewRunId,
                    candidateName: interview.candidateName,
                    id: interview.interviewId,
                    companyName: interview.companyName,
                    jobTitle: interview.jobTitle,
                    email: interview.email,
                    socialId: interview.socialId,
                    accountName: interview.accountName,
                    evaluationStatus: interview.interviewStatus,
                    globalScore: 0,
                    interviewDate: interview.interviewCreatedAt.toISOString(),
                    reviewStatus: "not_available" as ReviewStatus,
                    isThereDiffsInReport: false,
                    language: interview.jobLanguage,
                };
            }
        });

        return {
            interviews: interviewData,
            totalCount,
        };
    }

    async saveReviewSessionProgress(interviewId: string, report: ReportVersion): Promise<void> {
        this.logger.info(`Saving review session progress for interviewId:${interviewId}`);
        this.logger.info(`Report:${JSON.stringify(report)}`);
        await this.db.transaction(async (t) => {
            const interview = await t
                .select({
                    analyzerReport: interviews.analyzerReport,
                })
                .from(interviews)
                .where(eq(interviews.id, interviewId));

            if (interview.length === 0) throw new Error("Interview not found");

            const interviewReportData = interview[0].analyzerReport as InterviewReportData;

            const updatedInterviewReportData = {
                ...interviewReportData,
                report_edit_session: report,
            };

            this.logger.info(`Saving review session progress report:${JSON.stringify(updatedInterviewReportData)}`);

            await t
                .update(interviews)
                .set({ analyzerReport: updatedInterviewReportData })
                .where(eq(interviews.id, interviewId));
        });
        this.logger.info(`Review session progress saved for interviewId:${interviewId}`);
    }

    async submitReviewedReport(interviewId: string, report: ReportVersion): Promise<void> {
        this.logger.info(`Submitting reviewed report for interviewId:${interviewId}`);
        this.logger.info(`Report:${JSON.stringify(report)}`);

        await this.db.transaction(async (t) => {
            const interview = await t
                .select({
                    analyzerReport: interviews.analyzerReport,
                })
                .from(interviews)
                .where(eq(interviews.id, interviewId));

            if (interview.length === 0) throw new Error("Interview not found");

            const interviewReportData = interview[0].analyzerReport as InterviewReportData;
            const { approved, approved_by, approved_date, ...rest } = report;

            const newReportVersion = {
                ...rest,
            };

            const updatedInterviewReportData = {
                report_versions: [...interviewReportData.report_versions, newReportVersion],
            };

            this.logger.info(`Submitting reviewed report:${JSON.stringify(updatedInterviewReportData)}`);

            await t
                .update(interviews)
                .set({ analyzerReport: updatedInterviewReportData })
                .where(eq(interviews.id, interviewId));
        });
        this.logger.info(`Reviewed report submitted for interviewId:${interviewId}`);
    }

    async resetEditSession(interviewId: string): Promise<void> {
        this.logger.info(`Resetting edit session for interviewId:${interviewId}`);
        await this.db.transaction(async (t) => {
            const interview = await t
                .select({
                    analyzerReport: interviews.analyzerReport,
                })
                .from(interviews)
                .where(eq(interviews.id, interviewId));

            if (interview.length === 0) throw new Error("Interview not found");

            const interviewReportData = interview[0].analyzerReport as InterviewReportData;

            const updatedInterviewReportData: InterviewReportData = {
                report_versions: [...interviewReportData.report_versions],
            };

            this.logger.info(`Resetting edit session:${JSON.stringify(updatedInterviewReportData)}`);

            await t
                .update(interviews)
                .set({ analyzerReport: updatedInterviewReportData })
                .where(eq(interviews.id, interviewId));
        });
        this.logger.info(`Edit session reset for interviewId:${interviewId}`);
    }

    async sendPdfReportToPilat(interviewId: string, base64PDF: string): Promise<void> {
        this.logger.info(`Sending pdf report to Pilat for interviewId:${interviewId}`);
        try {

            this.logger.info(`Getting pilat secrets from secret manager before sending pdf report to Pilat`);
            const pilatSecrets = await getSecret<PilatSecrets>('pilat/secrets');

            const headers = {
                "Content-Type": "application/json",
                "x-api-key": pilatSecrets.PILAT_API_KEY,
            };

            const interview = await this.db
                .select({
                    analyzerReport: interviews.analyzerReport,
                    pilatInterviewId: interviewIdMappings.externalId,
                })
                .from(interviews)
                .innerJoin(interviewIdMappings, eq(interviews.id, interviewIdMappings.interviewId))
                .where(eq(interviews.id, interviewId))
                .limit(1);

            const reportsData = interview[0].analyzerReport;

            if (!reportsData) throw new Error("No reports found");

            const report = reportsData.report_versions.find((report) => report.approved);

            if (!report) throw new Error("No approved report found");

            const body = {
                jsonData: {
                    traits: report.traits.map((trait) => ({ code: trait.code, score: trait.score })),
                    families: report.families.map((family) => ({ code: family.code, score: family.score })),
                },
                encTesteeId: interview[0].pilatInterviewId,
                base64File: base64PDF,
            };

            const res = await axios.post(pilatSecrets.PILAT_API_URL, body, { headers });
            this.logger.info(`Sent pdf report to Pilat: ${res.status}`);
            this.logger.info(`Pilat response: ${res.data}`);
        } catch (e) {
            this.logger.error("Failed to send pdf report to Pilat");
            this.logger.error(e);
            throw e;
        }
    }

    async sendGlobalScoreToMyInterview(
        videoId: string,
        globalScore: number,
        headers: MyInterviewHeaders
    ): Promise<void> {
        try {
            const url = `https://api-ga.myinterview.com/api/v1/videos/${videoId}/upload-iverse-score`;
            const body = { score: globalScore };
            const res = await axios.post(url, body, { headers });
            this.logger.info(`Sent global score to MyInterview: ${res.status}`);
        } catch (err) {
            this.logger.error("Failed to send global score to MyInterview");
            throw err;
        }
    }

    async checkIfEvaluationIsRunning(interviewId: string): Promise<boolean> {
        this.logger.info("Checking if evaluation is running", { interviewId });

        const interview = await this.db
            .select({
                status: interviews.status,
            })
            .from(interviews)
            .where(eq(interviews.id, interviewId));

        if (interview.length === 0) throw new Error("Interview not found");

        return interview[0].status === "running";
    }

    async updateInterviewStatusToPending(interviewId: string): Promise<void> {
        this.logger.info("Updating interview status");
        await this.db
            .update(interviews)
            .set({ status: "pending", analyzerReport: null })
            .where(eq(interviews.id, interviewId));
    }

    async reAnalyzeInterview(interviewId: string): Promise<LambdaResponseToClientAction> {
        try {
            if (await this.checkIfEvaluationIsRunning(interviewId)) {
                throw new Error("Evaluation is already running");
            }

            const interview = await this.db
                .select({ event: interviews.event })
                .from(interviews)
                .where(eq(interviews.id, interviewId));

            const event = interview[0].event;

            if (!event) throw new Error("No event found, cannot re-analyze interview");

            await this.updateInterviewStatusToPending(interviewId);

            await this.sendToAnalyzerQueue(event as AnalyzerHandlerEvent);

            return createLambdaResponseToClientAction(200, {
                message: "Started analyzing interview",
                type: "success",
            });
        } catch (err) {
            this.logger.error("Failed to re-analyze interview");
            throw err;
        }
    }

    async sendToAnalyzerMyInterviewIntegration(
        event: MyInterviewEvent,
        rerunInterviewSecret: RerunInterviewSecret
    ): Promise<LambdaResponseToClientAction> {
        const headers = {
            "Content-Type": "application/json",
            "x-api-key": rerunInterviewSecret.API_KEY,
        };

        const res = await axios.post(rerunInterviewSecret.RERUN_EVALUATION_URL, event as MyInterviewEvent, { headers });

        this.logger.info(`Re-analyzed interview: ${res.status}`);

        return createLambdaResponseToClientAction(200, {
            message: "Started analyzing interview",
            type: "success",
        });
    }

    async sendToAnalyzerQueue(analyzerHandlerEvent: AnalyzerHandlerEvent): Promise<void> {
        try {
            this.logger.info("Sending to analyzer queue");
            this.logger.info(JSON.stringify(analyzerHandlerEvent));
            const client = new SQSClient({ region: this.region });
            const command = new SendMessageCommand({
                QueueUrl: this.analyzerQueueUrl,
                MessageBody: JSON.stringify(analyzerHandlerEvent),
                MessageDeduplicationId: analyzerHandlerEvent.interview_id + Date.now().toString(),
                MessageGroupId: Date.now().toString(),
            });

            await client.send(command);
        } catch (error) {
            this.logger.error(`Failed to send to analyzer queue | Error: ${error}`);
            throw error;
        }
    }

    async getInterviewsPageFilterOptions(): Promise<InterviewsPageFilterOptionsResponse> {
        this.logger.info("Getting interviews page filter options");
        const [jobRes, companyRes, accountRes] = await Promise.all([
            this.db
                .selectDistinct({
                    jobTitles: jobs.jobTitle,
                })
                .from(jobs),

            this.db
                .selectDistinct({
                    companies: customers.companyName,
                })
                .from(customers),

            this.db
                .selectDistinct({
                    accounts: accounts.name,
                })
                .from(accounts),
        ]);

        return {
            jobTitles: jobRes.map((r) => r.jobTitles),
            companies: companyRes.map((r) => r.companies),
            accounts: accountRes.map((r) => r.accounts),
        };
    }

    async checkIfPilatReport(interviewId: string): Promise<boolean> {
        const interview = await this.db
            .select({ accountName: accounts.name })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .innerJoin(accounts, eq(customers.accountId, accounts.id))
            .where(eq(interviews.id, interviewId));

        return interview[0].accountName === "Pilat";
    }

    async approveReport(interviewId: string, approvedBy: string, base64PDF: string): Promise<void> {
        this.logger.info(`Approving report for interviewId: ${interviewId}`);

        const pdfUrl = await this.uploadReportPDF(interviewId, base64PDF);

        await this.updateReportApprovalStatus(interviewId, approvedBy, pdfUrl);

        if (await this.checkIfPilatReport(interviewId)) {
            await this.sendPdfReportToPilat(interviewId, base64PDF);
        }

        this.logger.info(`Report approval completed for interviewId: ${interviewId}`);
    }

    private async uploadReportPDF(interviewId: string, base64PDF: string): Promise<string> {
        const s3Client = new S3Client({ region: this.region });
        const bucketName = getBucketName(this.env);
        const key = `interviews/${interviewId}/report.pdf`;

        const command = new PutObjectCommand({
            Bucket: bucketName,
            Key: key,
            Body: Buffer.from(base64PDF, "base64"),
            ContentType: "application/pdf",
        });

        await s3Client.send(command);
        return `https://${bucketName}.s3.amazonaws.com/${key}`;
    }

    private async updateReportApprovalStatus(interviewId: string, approvedBy: string, pdfUrl: string): Promise<void> {
        await this.db.transaction(async (t) => {
            const interview = await this.getInterviewData(t, interviewId);
            const interviewReportData = interview.analyzerReport as InterviewReportData;
            const candidateId = interview.candidateId;

            const updatedReportData = this.updateReportVersions(interviewReportData, approvedBy, pdfUrl);

            await t.update(interviews).set({ analyzerReport: updatedReportData }).where(eq(interviews.id, interviewId));

            await this.updateRelatedInterviews(t, interviewId, candidateId);
        });
    }

    private async getInterviewData(t: any, interviewId: string): Promise<{ analyzerReport: any; candidateId: string }> {
        const interview = await t
            .select({
                analyzerReport: interviews.analyzerReport,
                candidateId: interviews.candidateId,
            })
            .from(interviews)
            .where(eq(interviews.id, interviewId));

        if (interview.length === 0) throw new Error("Interview not found");
        return interview[0];
    }

    private updateReportVersions(
        interviewReportData: InterviewReportData,
        approvedBy: string,
        pdfUrl: string
    ): InterviewReportData {
        const reportVersions = [...interviewReportData.report_versions];
        if (reportVersions.length === 0) throw new Error("No report versions found");

        const latestReportIndex = reportVersions.length - 1;

        reportVersions[latestReportIndex] = {
            ...reportVersions[latestReportIndex],
            approved: true,
            approved_by: approvedBy,
            approved_date: new Date().toISOString(),
            pdf_url: pdfUrl,
        };

        for (let i = 0; i < latestReportIndex; i++) {
            if (reportVersions[i].approved) {
                const { approved, approved_by, approved_date, ...rest } = reportVersions[i];
                reportVersions[i] = rest;
            }
        }

        return {
            ...interviewReportData,
            report_versions: reportVersions,
        };
    }

    private async updateRelatedInterviews(t: any, interviewId: string, candidateId: string): Promise<void> {
        const candidateData = await t
            .select({
                jobId: candidates.jobId,
            })
            .from(candidates)
            .where(eq(candidates.id, candidateId));

        if (candidateData.length === 0) throw new Error("Candidate not found");
        const jobId = candidateData[0].jobId;

        const otherInterviews = await t
            .select({
                id: interviews.id,
                analyzerReport: interviews.analyzerReport,
            })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .where(and(eq(candidates.jobId, jobId), eq(candidates.id, candidateId), ne(interviews.id, interviewId)));

        for (const otherInterview of otherInterviews) {
            const otherReportData = otherInterview.analyzerReport as InterviewReportData;
            if (!otherReportData || !otherReportData.report_versions) continue;

            let needsUpdate = false;
            const updatedVersions = otherReportData.report_versions.map((version) => {
                if (version.approved) {
                    needsUpdate = true;
                    const { approved, approved_by, approved_date, ...rest } = version;
                    return rest;
                }
                return version;
            });

            if (needsUpdate) {
                await t
                    .update(interviews)
                    .set({
                        analyzerReport: {
                            ...otherReportData,
                            report_versions: updatedVersions,
                        },
                    })
                    .where(eq(interviews.id, otherInterview.id));
            }
        }
    }

    async getWarmupQuestionsAnswers(interviewId: string): Promise<WarmupQuestionsAnswersResponse> {
        const res = await this.db
            .select({
                answer: answers.answer,
                jobData: jobs.questions,
            })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .innerJoin(answers, eq(interviews.id, answers.interviewId))
            .where(eq(interviews.id, interviewId));

        if (res.length === 0) return { warmupQuestionsAnswers: [] };

        const warmupQuestions = res[0].jobData?.warmupQuestions || [];
        const warmupQuestionsAnswers: AdditionalQuestion[] = [];

        for (const item of res as { answer: InterviewQuestionAnswer }[]) {
            if (item.answer.question_id === null && warmupQuestions.includes(item.answer.question)) {
                const idx = warmupQuestions.indexOf(item.answer.question);
                warmupQuestionsAnswers[idx] = {
                    question: item.answer.question,
                    video_link: await generateCloudfrontSignedUrl(
                        item.answer.video_link,
                        3600,
                        this.cloudfrontSignedUrlSecret
                    ),
                };
            }
        }

        return {
            warmupQuestionsAnswers,
        };
    }
}
