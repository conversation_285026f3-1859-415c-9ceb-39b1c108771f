import { Checkbox, Stack } from "@mui/material";
import { FC } from "react";
import { Body1, H5 } from "@eva/shared-ui";

interface QuestionRowProps {
    questionId: string;
    trait: string;
    questionText: string;
    checked: boolean;
    handleSelect: (id: string) => void;
    disabled: boolean;
}

export const FamilyFieldQuestionRow: FC<QuestionRowProps> = ({ questionId, trait, questionText, checked, handleSelect, disabled }) => {

    return (
        <Stack key={questionId} direction='row' alignItems='center' gap={2} justifyContent='space-between'>
            <Stack direction='row'>
                <Checkbox disabled={disabled} color='primary' checked={checked} onChange={() => handleSelect(questionId)} size='large' />
                <Stack alignItems='start' gap={3}>
                    <H5 color='secondary'>{trait}</H5>
                    <Body1>{questionText}</Body1>
                </Stack>
            </Stack>
        </Stack>
    );
};
