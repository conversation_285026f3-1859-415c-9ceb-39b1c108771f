import { useParams } from "react-router";
import { useGetFamiliesWithTraitsAndQuestionsQuery, useGetJobQuery } from "../../app-state/apis";
import { BackButton, LoadingScreen, PageContainer, PageLayout } from "@eva/shared-ui";
import { JobBuilderForm } from "./sub-components/JobBuilderForm";

const JobBuilderPage = () => {
    const { data, isLoading: isForDataLoading } = useGetFamiliesWithTraitsAndQuestionsQuery();

    const { id } = useParams<{ id: string }>();
    const { data: jobData, isLoading: isJobDataLoading } = useGetJobQuery(id, {
        skip: !id,
    });

    const shouldRender: boolean = !isForDataLoading && !isJobDataLoading;

    return (
        <PageContainer>
            {shouldRender && data ? (
                <>
                    <BackButton />
                    <PageLayout>{jobData && <JobBuilderForm jobData={jobData} families={data.families} />}</PageLayout>
                </>
            ) : (
                <LoadingScreen />
            )}
        </PageContainer>
    );
};

export default JobBuilderPage;
