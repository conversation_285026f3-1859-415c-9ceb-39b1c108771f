import { accounts, customers, DB } from "@eva/drizzle";
import { CustomersResponse, CustomersPageTableDataResponse, CreateCustomerRequest } from "@eva/shared-common";
import { eq } from "@eva/drizzle";
import { Logger } from "@eva/logger";

export class CustomerService {
    db: DB;
    cloudFrontDist: string;
    logger: Logger;

    constructor(db: DB, logger: Logger, cloudFrontDist: string) {
        this.db = db;
        this.logger = logger;
        this.cloudFrontDist = cloudFrontDist;
    }

    async getCustomersByAccountId(accountId: string): Promise<CustomersResponse> {
        this.logger.info(`Getting all customers for account ${accountId}`);
        const res = await this.db
            .select({
                companyName: customers.companyName,
                id: customers.id,
            })
            .from(customers)
            .where(eq(customers.accountId, accountId));

        const allCustomers = res.map((item) => ({
            id: item.id,
            companyName: item.companyName,
        }));
        this.logger.info(`Found ${allCustomers.length} customers for account ${accountId}`);

        return { customers: allCustomers };
    }

    async getCustomersTableData(): Promise<CustomersPageTableDataResponse> {
        this.logger.info(`Getting all customers table data`);
        const res = await this.db
            .select({
                id: customers.id,
                companyName: customers.companyName,
                accountName: accounts.name,
                accountId: customers.accountId,
                createdAt: customers.createdAt,
                integration: customers.integration,
                themeColor: customers.themeColor,
            })
            .from(customers)
            .innerJoin(accounts, eq(customers.accountId, accounts.id));

        const allCustomers = res.map((item) => ({
            id: item.id,
            themeColor: item.themeColor,
            companyName: item.companyName,
            accountName: item.accountName,
            accountId: item.accountId,
            integration: item.integration ? item.integration : "",
            createdAt: item.createdAt.toISOString(),
        }));
        this.logger.info(`Found ${allCustomers.length} customers`);

        return { customers: allCustomers };
    }

    async createCustomer(createCustomerRequest: CreateCustomerRequest): Promise<void> {
        try {
            this.logger.info(`Creating customer: ${JSON.stringify(createCustomerRequest)}`);

            const customerData = {
            companyName: createCustomerRequest.customerName,
            accountId: createCustomerRequest.accountId,
            logoLink: createCustomerRequest.logoFilePath ? this.cloudFrontDist + createCustomerRequest.logoFilePath : null,
            integration: createCustomerRequest.integration || null,
            themeColor: createCustomerRequest.themeColor,
            };

            if (!createCustomerRequest.customerId) {
            const existingCustomer = await this.db
                .select({ id: customers.id })
                .from(customers)
                .where(eq(customers.companyName, createCustomerRequest.customerName))
                .limit(1);

            if (existingCustomer.length > 0) throw new Error(`Customer already exists: ${createCustomerRequest.customerName}`);

            await this.db.insert(customers).values(customerData);
            this.logger.info(`Customer created successfully: ${createCustomerRequest.customerName}`);
            } else {
            await this.db
                .update(customers)
                .set(customerData)
                .where(eq(customers.id, createCustomerRequest.customerId));
            this.logger.info(`Customer updated successfully: ${createCustomerRequest.customerName}`);
            }
        } catch (error) {
            this.logger.error("Failed to create/update customer", error);
            throw error;
        }
    }

    async getCustomerLogo(customerId: string): Promise<string | null> {
        try {
            this.logger.info(`Getting logo for customer: ${customerId}`);
            const res = await this.db.select({ logoLink: customers.logoLink }).from(customers).where(eq(customers.id, customerId)).limit(1);
            return res.length > 0 ? res[0].logoLink : null;
        } catch (error) {
            this.logger.error(`Failed to get logo for customer: ${customerId}`);
            throw error;
        }
    }
}
