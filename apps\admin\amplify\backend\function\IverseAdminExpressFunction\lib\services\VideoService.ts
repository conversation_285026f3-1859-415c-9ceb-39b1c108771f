import {
    BulkUploadVideosRequest,
    IntroVideosResponse,
    LanguageVariation,
    QuestionOptions,
    QuestionVideo,
    QuestionVideosForBuilderResponse,
    QuestionVideosResponse,
    UploadVideoRequest,
} from "@eva/shared-common";
import { customers, DB, questions, questionVideos, traits, welcomeVideos, and, eq, or, isNull } from "@eva/drizzle";
import { Logger } from "@eva/logger";

export class VideosService {
    private readonly db: DB;
    private readonly logger: Logger;
    private readonly cloudFrontDist: string;

    constructor(db: DB, logger: Logger, cloudFrontDist: string) {
        this.db = db;
        this.logger = logger;
        this.cloudFrontDist = cloudFrontDist;
    }

    async addQuestionVideo(request: UploadVideoRequest): Promise<void> {
        const videoLink = `${this.cloudFrontDist}${request.key}`;
        this.logger.info("Uploading question video");
        await this.db.insert(questionVideos).values({
            questionId: request.questionId,
            customerId: request.customerId === "all" ? undefined : request.customerId,
            videoLink: videoLink,
            language: request.language,
            gender: request.gender === "all" ? null : request.gender,
        });
        this.logger.info("Video uploaded successfully");
    }

    async addIntroVideo(request: UploadVideoRequest): Promise<void> {
        const videoLink = `${this.cloudFrontDist}${request.key}`;
        this.logger.info("Uploading welcome video");
        await this.db.insert(welcomeVideos).values({
            customerId: request.customerId === "all" ? null : request.customerId,
            videoLink: videoLink,
            gender: request.gender === "all" ? null : request.gender,
            language: request.language,
        });
        this.logger.info("Video uploaded successfully");
    }

    async uploadVideo(request: UploadVideoRequest): Promise<void> {
        this.logger.info("Uploading video");
        try {
            this.logger.info(
                `Uploading video to S3: ${request.key} | ${request.type} | ${request.questionId} | ${request.customerId}`
            );

            if (request.type === "question") await this.addQuestionVideo(request);
            else await this.addIntroVideo(request);
        } catch (error) {
            this.logger.error("Error uploading video");
            throw error;
        }
    }

    async bulkUploadVideos(request: BulkUploadVideosRequest): Promise<{ successful: number; failed: number }> {
        this.logger.info(`Bulk uploading ${request.videos.length} videos`);
        let successful = 0;
        let failed = 0;

        for (const video of request.videos) {
            try {
                await this.uploadVideo(video);
                successful++;
            } catch (error) {
                this.logger.error(`Error uploading video: ${video.key}`, error);
                failed++;
            }
        }

        this.logger.info(`Bulk upload completed. Successful: ${successful}, Failed: ${failed}`);
        return { successful, failed };
    }

    async getAllQuestionVideos(): Promise<QuestionVideosResponse> {
        this.logger.info("Fetching videos for question ID");

        try {
            const videos = await this.db
                .select({
                    videoLink: questionVideos.videoLink,
                    language: questionVideos.language,
                    customerId: questionVideos.customerId,
                    gender: questionVideos.gender,
                    customerName: customers.companyName,
                    trait: traits.trait,
                    questionOptions: questions.options,
                    questionVideoId: questionVideos.id,
                })
                .from(questionVideos)
                .leftJoin(customers, eq(customers.id, questionVideos.customerId))
                .leftJoin(questions, eq(questions.id, questionVideos.questionId))
                .leftJoin(traits, eq(traits.id, questions.traitId));

            if (!videos.length) {
                this.logger.info("No videos found for question ID");
                return { videos: [] };
            }

            const formattedVideos: QuestionVideo[] = videos.map((video) => {
                const questionOptionsVariationForDisplay = (video.questionOptions as QuestionOptions).options;
                const customerQuestionId = video.customerId ? video.customerId : "default";

                return {
                    questionVideoId: video.questionVideoId,
                    videoLink: video.videoLink,
                    language: video.language,
                    gender: video.gender ? video.gender : "All",
                    customerName: video.customerName ? video.customerName : "N/A",
                    trait: (video.trait as LanguageVariation).en,
                    question: questionOptionsVariationForDisplay[customerQuestionId]
                        ? questionOptionsVariationForDisplay[customerQuestionId].en.male.text
                        : questionOptionsVariationForDisplay.default.en.male.text,
                };
            });

            this.logger.info(`Found ${videos.length} videos`);
            return { videos: formattedVideos };
        } catch (error) {
            this.logger.error("Error fetching question videos", { error });
            throw new Error("Internal server error");
        }
    }

    async getAllIntroVideos(): Promise<IntroVideosResponse> {
        this.logger.info("Fetching intro videos");

        try {
            const videos = await this.db
                .select({
                    id: welcomeVideos.id,
                    gender: welcomeVideos.gender,
                    videoLink: welcomeVideos.videoLink,
                    language: welcomeVideos.language,
                    customerName: customers.companyName,
                })
                .from(welcomeVideos)
                .leftJoin(customers, eq(customers.id, welcomeVideos.customerId));

            return {
                videos: videos.map((video) => ({
                    id: video.id,
                    videoLink: video.videoLink,
                    customerName: video.customerName ? video.customerName : "N/A",
                    gender: video.gender ? video.gender : "All",
                    language: video.language,
                })),
            };
        } catch (error) {
            this.logger.error("Error fetching intro videos", { error });
            throw new Error("Internal server error");
        }
    }

    async getIntroVideosByCustomerId(customerId: string): Promise<IntroVideosResponse> {
        this.logger.info(`Fetching intro videos for customer ID: ${customerId}`);
        try {
            const videos = await this.db
                .select({
                    id: welcomeVideos.id,
                    gender: welcomeVideos.gender,
                    videoLink: welcomeVideos.videoLink,
                    language: welcomeVideos.language,
                    customerName: customers.companyName,
                })
                .from(welcomeVideos)
                .leftJoin(customers, eq(customers.id, welcomeVideos.customerId))
                .where(or(eq(welcomeVideos.customerId, customerId), isNull(welcomeVideos.customerId)));

            return {
                videos: videos.map((video) => ({
                    id: video.id,
                    videoLink: video.videoLink,
                    customerName: video.customerName ? video.customerName : "N/A",
                    gender: video.gender ? video.gender : "All",
                    language: video.language,
                })),
            };
        } catch (error) {
            this.logger.error("Error fetching intro videos by customer ID", { error });
            throw new Error("Internal server error");
        }
    }

    async getQuestionVideos(customerId: string, questionId: string): Promise<QuestionVideosForBuilderResponse> {
        this.logger.info(`Fetching video for question ID: ${questionId}`);

        try {
            const videos = await this.db
                .select({
                    videoLink: questionVideos.videoLink,
                    language: questionVideos.language,
                    gender: questionVideos.gender,
                    customerName: customers.companyName,
                })
                .from(questionVideos)
                .leftJoin(customers, eq(customers.id, questionVideos.customerId))
                .where(
                    and(
                        eq(questionVideos.questionId, questionId),
                        or(eq(questionVideos.customerId, customerId), isNull(questionVideos.customerId))
                    )
                );

            return {
                videos: videos.map((video) => ({
                    videoLink: video.videoLink,
                    gender: video.gender ? video.gender : "All",
                    language: video.language,
                    customerName: video.customerName ? video.customerName : "N/A",
                })),
            };
        } catch (error) {
            this.logger.error("Error fetching question video", { error });
            throw error;
        }
    }

    async checkIfQuestionVideoExists(request: UploadVideoRequest): Promise<boolean> {
        try {
            const { customerId, gender, language } = request;

            const customerClause =
                customerId === "all" ? isNull(questionVideos.customerId) : eq(questionVideos.customerId, customerId);
            const genderClause = gender === "all" ? isNull(questionVideos.gender) : eq(questionVideos.gender, gender);
            const languageClause = eq(questionVideos.language, language);

            const video = await this.db
                .select()
                .from(questionVideos)
                .where(
                    and(eq(questionVideos.questionId, request.questionId), customerClause, genderClause, languageClause)
                );

            return !(video.length > 0);
        } catch (error) {
            this.logger.error("Error checking if question video exists", { error });
            throw error;
        }
    }

    async checkIfIntroVideoExists(request: UploadVideoRequest): Promise<boolean> {
        try {
            const { customerId, gender } = request;
            const customerClause =
                customerId === "all" ? isNull(welcomeVideos.customerId) : eq(welcomeVideos.customerId, customerId);
            const genderClause = gender === "all" ? isNull(welcomeVideos.gender) : eq(welcomeVideos.gender, gender);
            const languageClause = eq(welcomeVideos.language, request.language);

            const video = await this.db
                .select()
                .from(welcomeVideos)
                .where(and(customerClause, genderClause, languageClause));

            return !(video.length > 0);
        } catch (error) {
            this.logger.error("Error checking if intro video exists", { error });
            throw error;
        }
    }

    async checkIfAlreadyExists(request: UploadVideoRequest): Promise<boolean> {
        this.logger.info("Validating video");

        if (request.type === "question") return await this.checkIfQuestionVideoExists(request);
        else return await this.checkIfIntroVideoExists(request);
    }

    async deleteIntroVideo(videoId: string): Promise<void> {
        this.logger.info(`Deleting intro video with ID: ${videoId}`);
        try {
            await this.db
                .delete(welcomeVideos)
                .where(eq(welcomeVideos.id, videoId))
                .returning({ videoLink: welcomeVideos.videoLink });
        } catch (error) {
            this.logger.error(`Error deleting intro video:${videoId}`, { error });
            throw error;
        }
    }

    async deleteQuestionVideo(videoId: string): Promise<void> {
        this.logger.info(`Deleting question video with ID: ${videoId}`);
        try {
            await this.db
                .delete(questionVideos)
                .where(eq(questionVideos.id, videoId))
                .returning({ videoLink: questionVideos.videoLink });
        } catch (error) {
            this.logger.error(`Error deleting question video:${videoId}`, { error });
            throw error;
        }
    }
}
