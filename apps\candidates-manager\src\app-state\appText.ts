import { AppText, Language } from "@eva/shared-ui";

export type LanguageMapper = Record<Language, string>;

export const languageMapper = (language: string): string => {
    const languagesMap: LanguageMapper = {
        en: "English",
        he: "Hebrew",
    };
    return languagesMap[language as Language];
};

export const languages: Language[] = ["en", "he"];

export const appText: AppText = {
    en: {
        direction: "ltr",
        home: {
            title: "Home",
        },
    },
    he: {
        direction: "rtl",
        home: {
            title: "בית",
        },
    },
};
