import { LanguageVariation } from "./FamiliesWithTraitsForDisplayMap";
import { FamilyId } from "./FamilyId";
import { QuestionOptions } from "./QuestionOptions";

export interface CalculatedTrait {
    trait: LanguageVariation;
    id: string;
}


export interface QuestionForBuilder {
    question_id: string;
    question: QuestionOptions;
    trait: LanguageVariation;
    trait_id: string;
}

export interface FamilyForBuilder {
    family: LanguageVariation;
    family_id: FamilyId;
    questions: QuestionForBuilder[];
    computedTraits: CalculatedTrait[];
}

export interface FamiliesWithQuestionsForBuilderResponse {
    families: FamilyForBuilder[];
}
