import { useMediaQueryContext } from "@eva/shared-ui";
import { InterviewCameraSideLayout } from "../../Interviewer.styles";
import { useInterviewer } from "../../InterviewerProvider";
import { CompanyBanner } from "../interview-panel/sub-components/company-banner/CompanyBanner";
import { MobileWelcomeVideo } from "../mobile/MobileWelcomeVideo";
import { AnimatedComponentWrapper, AnimatedVideoContainer, VideoPreviewContainer } from "./CamerPanel.styles";
import { CameraPanelScreen } from "./sub-components/CameraPanelScreen";
import { MobileTrainingRecordingPreview } from "./sub-components/MobileTrainingRecordingPreview";
import { AnimatePresence, motion } from "framer-motion";

const containerAnimation = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
};

const childAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.3 } }
};

const staggeredChildrenAnimation = {
    animate: { transition: { staggerChildren: 0.1 } }
};

export const CameraPanel = () => {
    const { isMobile } = useMediaQueryContext();
    const { isError, data, isWelcomeVideoDone } = useInterviewer();

    return !isError ? (
        <InterviewCameraSideLayout backgroundColor={data?.themeColor} item xs={7}>
            <AnimatedVideoContainer
                initial="initial"
                animate="animate"
                exit="exit"
                variants={containerAnimation}
            >
                <VideoPreviewContainer>
                    <AnimatePresence mode="wait">
                       
                        
                        {isMobile && !isWelcomeVideoDone && (
                            <AnimatedComponentWrapper key="welcome-video" variants={childAnimation}>
                                <MobileWelcomeVideo />
                            </AnimatedComponentWrapper>
                        )}
                        <AnimatedComponentWrapper key="mobile-training" variants={childAnimation}>
                            <MobileTrainingRecordingPreview />
                        </AnimatedComponentWrapper>
                        
                        <motion.div 
                            key="camera-panel" 
                            variants={staggeredChildrenAnimation}
                            initial="initial"
                            animate="animate"
                            exit="exit"
                            style={{ flexGrow: 1, width: '100%', display: 'flex' }}
                        >
                            <CameraPanelScreen />
                        </motion.div>
                    </AnimatePresence>
                </VideoPreviewContainer>
            </AnimatedVideoContainer>
        </InterviewCameraSideLayout>
    ) : (
        <></>
    );
};


export default CameraPanel;