import SyncIcon from "@mui/icons-material/Sync";
import { Icon<PERSON>utton, Stack, Tab, Tabs } from "@mui/material";
import React, { FC, useState } from "react";
import { useGetAnalyzerLogsQuery, useLazyGetAnalyzerLogsQuery } from "../../../app-state/apis";
import { ClickableBody2, CenteredModal, ModalLoadingSpinner } from "@eva/shared-ui";

interface InterviewLogsModalProps {
    runId: string;
    interviewDate: string;
    onClose: () => void;
    isOpen: boolean;
}

export const InterviewLogsModal: FC<InterviewLogsModalProps> = ({ interviewDate, isOpen, onClose, runId }) => {
    const { data, isLoading } = useGetAnalyzerLogsQuery({ interviewDate: interviewDate, runId: runId });
    const [getAnalyzerLogs, { isFetching }] = useLazyGetAnalyzerLogsQuery();

    const handleRefreshClick = () => {
        getAnalyzerLogs({ interviewDate: interviewDate, runId: runId });
    };

    return (
        <CenteredModal isOpen={isOpen} onClose={onClose} height={200} width={1200}>
            {isLoading || isFetching || !data ? (
                <ModalLoadingSpinner />
            ) : (
                <>
                    <Stack direction="row" justifyContent="space-between">
                        <IconButton onClick={handleRefreshClick}>
                            <SyncIcon />
                        </IconButton>
                    </Stack>

                    <Stack>
                        {data.analyzerLinks.map((logLink) => (
                            <ClickableBody2 href={logLink} key={logLink}>
                                {logLink}
                            </ClickableBody2>
                        ))}
                    </Stack>
                </>
            )}
        </CenteredModal>
    );
};
