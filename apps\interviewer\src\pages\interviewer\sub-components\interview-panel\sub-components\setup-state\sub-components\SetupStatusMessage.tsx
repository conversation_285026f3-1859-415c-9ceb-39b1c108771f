import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import { Stack } from "@mui/material";
import { Body1, H4, useMediaQueryContext } from "@eva/shared-ui";


interface SetupStatusMessageProps {
    externalInputType: string;
    disabledMessage: string;
    readyMessage: string;
    status: boolean;
}

export const SetupStatusMessage = ({ externalInputType, status, disabledMessage, readyMessage }: SetupStatusMessageProps) => {
    const { isMobile } = useMediaQueryContext();
    return (
        <Stack direction='row' gap={6} alignItems='center'>
            {status ? (
                <CheckCircleIcon
                    fontSize={isMobile ? "large" : "medium"}
                    color='success'
                    data-qa={`external-input-ready-icon-${externalInputType}`}
                />
            ) : (
                <ErrorIcon fontSize={isMobile ? "large" : "medium"} color='error' data-qa={`external-input-error-icon-${externalInputType}`} />
            )}
            {isMobile ? (
                <H4 data-qa={`external-input-ready-message-${externalInputType}`}>
                    {externalInputType}: {status ? readyMessage : disabledMessage}
                </H4>
            ) : (
                <Body1 data-qa={`external-input-ready-message-${externalInputType}`}>
                    {externalInputType}: {status ? readyMessage : disabledMessage}
                </Body1>
            )}
        </Stack>
    );
};
