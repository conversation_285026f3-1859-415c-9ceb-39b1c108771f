import awsServerlessExpressMiddleware from "aws-serverless-express/middleware";
import bodyParser from "body-parser";
import express, { NextFunction, Request, Response } from "express";
import { Logger } from "@eva/logger";
import {
    AccountsResponse,
    AdditionalQuestionsResponse,
    ApproveReportVersionRequest,
    BuilderSimulationQuestionsResponse,
    BulkUploadVideosRequest,
    CreateCustomerRequest,
    CustomerLogoLinkResponse,
    CustomersResponse,
    FamiliesWithQuestionsForBuilderResponse,
    getSecret,
    InterviewsPageFilterOptionsResponse,
    InterviewsPageTableDataRequest,
    InterviewsPageTableDataResponse,
    IntroVideosResponse,
    LogsLinksRequest,
    LogsLinksResponse,
    PDFReportRequest,
    PilatSecrets,
    PromptsPageFamiliesResponse,
    PromptsPageTraitsResponse,
    QuestionVideosForBuilderResponse,
    QuestionVideosResponse,
    ReportVersion,
    RerunInterviewSecret,
    UpdateFamilyPromptMetadataRequest,
    UpdateJobRequest,
    UpdateTraitPromptMetadataRequest,
    UploadVideoRequest,
    WarmupQuestionsAnswersResponse,
    IverseAdminCloudfrontDistSecret,
} from "@eva/shared-common";
import { initDB } from "@eva/drizzle";
import { DB } from "@eva/drizzle/src/types";
import { setupLogging } from "@eva/logger";
import { AccountService } from "./services/AccountService";
import { CustomerService } from "./services/CustomerService";
import { InterviewService } from "./services/InterviewService";
import { JobService } from "./services/JobService";
import { LogsService } from "./services/LogsService";
import { PromptService } from "./services/PromptService";
import { VideosService } from "./services/VideoService";
import { generateCloudfrontSignedUrl, CloudfrontAccessPair } from "@eva/aws-utils";

const app = express();

app.use(bodyParser.json({ limit: "10mb" }));

app.use(awsServerlessExpressMiddleware.eventContext());

app.use((req: Request, res: Response, next: NextFunction) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Headers", "*");
    next();
});

const env = process.env.ENV || "dev";
let db: DB | null = null;
let logger: Logger = setupLogging();
let cloudfrontSignedUrlSecret: CloudfrontAccessPair | null = null;
let pilatSecrets: PilatSecrets | null = null;
let rerunInterviewSecret: RerunInterviewSecret | null = null;
let cloudfrontDist: IverseAdminCloudfrontDistSecret | null = null;
let videoService: VideosService;
let accountService: AccountService;
let customerService: CustomerService;
let jobService: JobService;
let interviewService: InterviewService;
let logService: LogsService;
let promptService: PromptService;

const initializeResources = async () => {
    if (!db) {
        logger.info("Initializing DB connection");
        db = await initDB();
        logger.info("DB connection initialized");
    }

    if (!cloudfrontSignedUrlSecret) {
        logger.info("Initializing cloudfront signed url secret");
        cloudfrontSignedUrlSecret = await getSecret<CloudfrontAccessPair>("cloudfront_access_pair");
        logger.info("Cloudfront signed url secret: ", cloudfrontSignedUrlSecret);
    }

    if (!pilatSecrets) {
        logger.info("Initializing pilat secrets");
        pilatSecrets = await getSecret<PilatSecrets>("pilat/secrets");
        logger.info("Pilat secrets: ", pilatSecrets);
    }

    if (!rerunInterviewSecret) {
        logger.info("Initializing rerun interview secret");
        rerunInterviewSecret = await getSecret<RerunInterviewSecret>(`${env}/rerunURL`);
    }

    if (!cloudfrontDist) {
        logger.info("Initializing cloudfront dist secret");
        cloudfrontDist = await getSecret<IverseAdminCloudfrontDistSecret>(`${env}/iverseAdminCloudfronDist`);
        logger.info("Cloudfront dist secret: ", cloudfrontDist);
    }

    if (!videoService) {
        logger.info("Initializing video service");
        videoService = new VideosService(db, logger, cloudfrontDist.CLOUDFRONT_DISTRIBUTION);
    }

    if (!accountService) {
        logger.info("Initializing accounts service");
        accountService = new AccountService(db, logger);
    }

    if (!jobService) {
        logger.info("Initializing job service");
        jobService = new JobService(db, logger, pilatSecrets);
    }

    if (!customerService) {
        logger.info("Initializing customer service");
        customerService = new CustomerService(db, logger, cloudfrontDist.CLOUDFRONT_DISTRIBUTION);
    }

    if (!interviewService) {
        logger.info("Initializing interview service");
        interviewService = new InterviewService(db, logger, cloudfrontSignedUrlSecret);
    }

    if (!logService) {
        logger.info("Initializing logs service");
        logService = new LogsService(logger);
    }

    if (!promptService) {
        logger.info("Initializing prompt service");
        promptService = new PromptService(db, logger);
    }

    if (
        !db ||
        !logger ||
        !cloudfrontSignedUrlSecret ||
        !cloudfrontDist ||
        !videoService ||
        !accountService ||
        !customerService ||
        !jobService ||
        !interviewService ||
        !logService ||
        !promptService
    ) {
        if (!db) logger.error("DB not initialized");
        if (!cloudfrontSignedUrlSecret) logger.error("Cloudfront signed url secret not initialized");
        if (!cloudfrontDist) logger.error("Cloudfront dist secret not initialized");
        if (!videoService) logger.error("Video service not initialized");
        if (!accountService) logger.error("Accounts service not initialized");
        if (!jobService) logger.error("Job service not initialized");
        if (!customerService) logger.error("Customer service not initialized");
        if (!interviewService) logger.error("Interview service not initialized");
        if (!logService) logger.error("Logs service not initialized");
        if (!promptService) logger.error("Prompt service not initialized");
        throw new Error("Failed to initialize resources");
    }
};

app.use(async (req: Request, res: Response, next: NextFunction) => {
    try {
        await initializeResources();
        next();
    } catch (error) {
        logger.error("Error initializing resources", error);
        next(error);
    }
});

app.use((err: any, req: Request, res: Response, next: NextFunction) => {
    logger.error("An error occurred", {
        message: err.message,
        stack: err.stack,
    });
    logger.error(err.stack);

    res.status(500).json({
        error: {
            message: err.message,
            stack: process.env.NODE_ENV === "prod" ? undefined : err.stack,
        },
    });
});

app.get("/accounts", async (req: Request, res: Response<AccountsResponse>, next: NextFunction) => {
    try {
        const accountResponse = await accountService.getAccounts();

        res.json(accountResponse);
    } catch (error) {
        logger.error("Error getting accounts", error);
        next(error);
    }
});

app.get(
    "/customers-by-account-id/:accountId",
    async (req: Request<{ accountId: string }, {}, {}, {}>, res: Response<CustomersResponse>, next: NextFunction) => {
        try {
            logger.info("Getting customers by account id");

            const { accountId } = req.params;

            logger.info(`Account id: ${accountId}`);

            const customerResponse = await customerService.getCustomersByAccountId(accountId);

            res.json(customerResponse);
        } catch (error) {
            logger.error("Error getting customers by account id", error);
            next(error);
        }
    }
);

app.get("/customers-page-table-data", async (req: Request, res: Response, next: NextFunction) => {
    try {
        const customersTableData = await customerService.getCustomersTableData();

        res.json(customersTableData);
    } catch (error) {
        logger.error("Error getting customers page table data", error);
        next(error);
    }
});

app.get("/job-page-table-data", async (req: Request, res: Response, next: NextFunction) => {
    try {
        const customersTableData = await jobService.getJobsPageTableData();

        res.json(customersTableData);
    } catch (error) {
        logger.error("Error getting customers page table data", error);
        next(error);
    }
});

app.get(
    "/interview/:interviewId",
    async (req: Request<{ interviewId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { interviewId } = req.params;

            const interview = await interviewService.getInterviewById(interviewId);

            res.json(interview);
        } catch (error) {
            logger.error("Error getting interview", error);
            next(error);
        }
    }
);

app.get(
    "/interviews-page-table-data",
    async (req: Request, res: Response<InterviewsPageTableDataResponse>, next: NextFunction) => {
        try {
            const {
                page = "0",
                pageSize = "10",
                sortField,
                sortOrder,
                searchText,
                jobTitle,
                company,
                account,
                reviewStatus,
            } = req.query as {
                page?: string;
                pageSize?: string;
                sortField: string;
                sortOrder?: string;
                searchText?: string;
                jobTitle?: string;
                company?: string;
                account?: string;
                reviewStatus?: string;
            };

            const params: InterviewsPageTableDataRequest = {
                pagination: {
                    page: parseInt(page),
                    pageSize: parseInt(pageSize),
                },
                sort: {
                    field: sortField,
                    sort: sortOrder as "asc" | "desc",
                },
            };

            if (searchText || jobTitle || company || account || reviewStatus) {
                params.filter = {};

                if (searchText) {
                    params.filter.searchText = searchText;
                }

                if (jobTitle) {
                    params.filter.jobTitle = jobTitle;
                }

                if (company) {
                    params.filter.company = company;
                }

                if (account) {
                    params.filter.account = account;
                }

                if (reviewStatus) {
                    params.filter.reviewStatus = reviewStatus;
                }
            }

            const interviews = await interviewService.getInterviewsPageTableData(params);

            res.json(interviews);
        } catch (error) {
            logger.error("Error getting interviews", error);
            next(error);
        }
    }
);

app.post(
    "/interviews/save-review-session-progress/:interviewId",
    async (req: Request<{ interviewId: string }, {}, ReportVersion, {}>, res: Response, next: NextFunction) => {
        try {
            const { interviewId } = req.params;
            const report = req.body;

            await interviewService.saveReviewSessionProgress(interviewId, report);

            res.json({});
        } catch (error) {
            logger.error("Error updating report", error);
            next(error);
        }
    }
);

app.post(
    "/rerun-interview/:interviewId",
    async (req: Request<{ interviewId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { interviewId } = req.params;

            const requestResult = await interviewService.reAnalyzeInterview(interviewId);

            res.json(requestResult);
        } catch (error) {
            logger.error("Error re-analyzing interview", error);
            next(error);
        }
    }
);

app.get("/jobs/:jobId", async (req: Request<{ jobId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
    try {
        const { jobId } = req.params;

        const job = await jobService.getJob(jobId);

        res.json(job);
    } catch (error) {
        logger.error("Error getting job", error);
        next(error);
    }
});

app.post("/update-job", async (req: Request<{}, {}, UpdateJobRequest, {}>, res: Response, next: NextFunction) => {
    try {
        const updateResponse = await jobService.updateJob(req.body);

        res.json(updateResponse);
    } catch (error) {
        logger.error("Error updating job", error);
        next(error);
    }
});

app.post(
    "/create-customer",
    async (req: Request<{}, {}, CreateCustomerRequest, {}>, res: Response, next: NextFunction) => {
        try {
            await customerService.createCustomer(req.body);

            res.json({});
        } catch (error) {
            logger.error("Error creating customer", error);
            next(error);
        }
    }
);

app.post(
    "/approve-report/:interviewId",
    async (
        req: Request<{ interviewId: string }, {}, ApproveReportVersionRequest, {}>,
        res: Response,
        next: NextFunction
    ) => {
        try {
            const { interviewId } = req.params;
            const { approvedBy, base64PDF } = req.body;

            await interviewService.approveReport(interviewId, approvedBy, base64PDF);

            res.json({ message: "Report approved and PDF sent successfully" });
        } catch (error) {
            logger.error("Error approving report", error);
            next(error);
        }
    }
);

app.get(
    "/families-traits-questions",
    async (req: Request, res: Response<FamiliesWithQuestionsForBuilderResponse>, next: NextFunction) => {
        try {
            const familiesWithTraitsAndQuestions = await jobService.getFamiliesWithTraitsAndQuestionsForBuilder();

            res.json(familiesWithTraitsAndQuestions);
        } catch (error) {
            logger.error("Error getting families with traits and questions", error);
            next(error);
        }
    }
);

app.get(
    "/additional-questions/:interviewId",
    async (
        req: Request<{ interviewId: string }, {}, {}, {}>,
        res: Response<AdditionalQuestionsResponse>,
        next: NextFunction
    ) => {
        try {
            const { interviewId } = req.params;

            const additionalQuestions = await interviewService.getAdditionalQuestions(interviewId);

            res.json(additionalQuestions);
        } catch (error) {
            logger.error("Error getting additional questions", error);
            next(error);
        }
    }
);

app.get(
    "/export-job-questions/:jobId",
    async (req: Request<{ jobId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { jobId } = req.params;

            const jobQuestions = await jobService.getJobQuestionsForExport(jobId);

            res.json(jobQuestions);
        } catch (error) {
            logger.error("Error getting job questions for export", error);
            next(error);
        }
    }
);

app.post(
    "/submit-reviewed-report/:interviewId",
    async (req: Request<{ interviewId: string }, {}, ReportVersion, {}>, res: Response, next: NextFunction) => {
        try {
            const { interviewId } = req.params;
            const report = req.body;

            await interviewService.submitReviewedReport(interviewId, report);

            res.json({});
        } catch (error) {
            logger.error("Error submitting reviewed report", error);
            next(error);
        }
    }
);
app.get(
    "/reset-review-session/:interviewId",
    async (req: Request<{ interviewId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { interviewId } = req.params;

            await interviewService.resetEditSession(interviewId);

            res.json({});
        } catch (error) {
            logger.error("Error resetting edit session", error);
            next(error);
        }
    }
);

app.post(
    "/signed-url",
    async (
        req: Request<{}, {}, { url: string; expiration: number }, {}>,
        res: Response<{ url: string }>,
        next: NextFunction
    ) => {
        try {
            const { url, expiration } = req.body;

            logger.info(`Getting signed url ${JSON.stringify({ url, expiration })}`);

            const signedUrl = await generateCloudfrontSignedUrl(url, expiration, cloudfrontSignedUrlSecret!);

            logger.info(`Signed url: ${signedUrl}`);

            res.json({ url: signedUrl });
        } catch (error) {
            logger.error("Error getting signed url", error);
            next(error);
        }
    }
);

app.post(
    "/analyzer-logs",
    async (req: Request<{}, {}, LogsLinksRequest, {}>, res: Response<LogsLinksResponse>, next: NextFunction) => {
        try {
            const interviewLogs = await logService.getLogsLinks(req.body);

            res.json(interviewLogs);
        } catch (error) {
            logger.error("Error sending analyzer logs to S3", error);
            next(error);
        }
    }
);

app.post("/upload-video", async (req: Request<{}, {}, UploadVideoRequest, {}>, res: Response, next: NextFunction) => {
    try {
        logger.info(`Uploading video: ${JSON.stringify(req.body)}`);

        await videoService.uploadVideo(req.body);

        res.json({});
    } catch (error) {
        logger.error("Error uploading video");
        next(error);
    }
});

app.post(
    "/bulk-upload-videos",
    async (req: Request<{}, {}, BulkUploadVideosRequest, {}>, res: Response, next: NextFunction) => {
        try {
            logger.info(`Bulk uploading videos: ${req.body.videos.length} videos`);

            const result = await videoService.bulkUploadVideos(req.body);

            res.json(result);
        } catch (error) {
            logger.error("Error bulk uploading videos");
            next(error);
        }
    }
);

app.get("/question-videos", async (req: Request, res: Response<QuestionVideosResponse>, next: NextFunction) => {
    try {
        const questionVideos = await videoService.getAllQuestionVideos();

        res.json(questionVideos);
    } catch (error) {
        logger.error("Error getting question videos", error);
        next(error);
    }
});

app.get("/intro-videos", async (req: Request, res: Response<IntroVideosResponse>, next: NextFunction) => {
    try {
        const introVideos = await videoService.getAllIntroVideos();

        res.json(introVideos);
    } catch (error) {
        logger.error("Error getting intro videos", error);
        next(error);
    }
});

app.get(
    "/intro-videos/:customerId",
    async (
        req: Request<{ customerId: string }, {}, {}, {}>,
        res: Response<IntroVideosResponse>,
        next: NextFunction
    ) => {
        try {
            const { customerId } = req.params;

            const introVideos = await videoService.getIntroVideosByCustomerId(customerId);

            res.json(introVideos);
        } catch (error) {
            logger.error("Error getting intro videos by customer id", error);
            next(error);
        }
    }
);

app.get(
    "/question-videos/:customerId/:questionId",
    async (
        req: Request<{ customerId: string; questionId: string }, {}, {}, {}>,
        res: Response<QuestionVideosForBuilderResponse>,
        next: NextFunction
    ) => {
        try {
            const { customerId, questionId } = req.params;

            const video = await videoService.getQuestionVideos(customerId, questionId);

            res.json(video);
        } catch (error) {
            logger.error("Error getting question video", error);
            next(error);
        }
    }
);

app.post(
    "/validate-video",
    async (req: Request<{}, {}, UploadVideoRequest, {}>, res: Response<{ isValid: boolean }>, next: NextFunction) => {
        try {
            const isValid = await videoService.checkIfAlreadyExists(req.body);

            res.json({ isValid });
        } catch (error) {
            logger.error("Error validating video", error);
            next(error);
        }
    }
);

app.delete(
    "/intro-videos/:videoId",
    async (req: Request<{ videoId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { videoId } = req.params;

            await videoService.deleteIntroVideo(videoId);

            res.json({});
        } catch (error) {
            logger.error("Error deleting intro video", error);
            next(error);
        }
    }
);

app.delete(
    "/question-videos/:videoId",
    async (req: Request<{ videoId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const { videoId } = req.params;

            await videoService.deleteQuestionVideo(videoId);

            res.json({});
        } catch (error) {
            logger.error("Error deleting question video", error);
            next(error);
        }
    }
);

app.get(
    `/customer-logo/:customerId`,
    async (
        req: Request<{ customerId: string }, {}, {}, {}>,
        res: Response<CustomerLogoLinkResponse>,
        next: NextFunction
    ) => {
        try {
            const { customerId } = req.params;

            const logo = await customerService.getCustomerLogo(customerId);

            res.json({ logoLink: logo });
        } catch (error) {
            logger.error("Error getting customer logo", error);
            next(error);
        }
    }
);

app.get("/prompts/families", async (req: Request, res: Response<PromptsPageFamiliesResponse>, next: NextFunction) => {
    try {
        res.json(await promptService.getFamiliesWithPromptMetadata());
    } catch (error) {
        logger.error("Error getting families with prompt metadata", error);
        next(error);
    }
});

app.get("/prompts/traits", async (req: Request, res: Response<PromptsPageTraitsResponse>, next: NextFunction) => {
    try {
        res.json(await promptService.getTraitsWithPromptMetadata());
    } catch (error) {
        logger.error("Error getting traits with prompt metadata", error);
        next(error);
    }
});

app.post(
    "/prompts/families",
    async (req: Request<{}, {}, UpdateFamilyPromptMetadataRequest, {}>, res: Response, next: NextFunction) => {
        try {
            await promptService.updateFamilyPromptMetadata(req.body);
            res.json({});
        } catch (err) {
            logger.error("Failed to update family prompts metadata");
            next(err);
        }
    }
);

app.post(
    "/prompts/traits",
    async (req: Request<{}, {}, UpdateTraitPromptMetadataRequest, {}>, res: Response, next: NextFunction) => {
        try {
            await promptService.updateTraitPromptMetadata(req.body);
            res.json({});
        } catch (err) {
            logger.error("Failed to update trait prompts metadata");
            next(err);
        }
    }
);

app.get(
    "/interviews-page-filter-options",
    async (req: Request, res: Response<InterviewsPageFilterOptionsResponse>, next: NextFunction) => {
        try {
            const filterOptions = await interviewService.getInterviewsPageFilterOptions();

            res.json(filterOptions);
        } catch (error) {
            logger.error("Error getting interviews page filter options", error);
            next(error);
        }
    }
);

app.get(
    "/warmup-questions/:interviewId",
    async (
        req: Request<{ interviewId: string }, {}, {}, {}>,
        res: Response<WarmupQuestionsAnswersResponse>,
        next: NextFunction
    ) => {
        try {
            const { interviewId } = req.params;

            const warmupQuestionsAnswers = await interviewService.getWarmupQuestionsAnswers(interviewId);
            res.json(warmupQuestionsAnswers);
        } catch (error) {
            logger.error("Error getting warmup questions answers", error);
            next(error);
        }
    }
);

app.get(
    "/builder-simulations",
    async (req: Request, res: Response<BuilderSimulationQuestionsResponse>, next: NextFunction) => {
        try {
            const simulations = await jobService.getSimulations();
            res.json(simulations);
        } catch (error) {
            logger.error("Error getting simulations", error);
            next(error);
        }
    }
);

app.listen(3000, () => {
    logger.info("App started");
});

export default app;
