import { Stack } from "@mui/material";
import { Subtitle1 } from "../theme/Typography";
import { FC } from "react";

interface DataTableFilterWrapperProps {
    resultsCount: number;
    children?: React.ReactNode;
}

export const DataTableFilterWrapper: FC<DataTableFilterWrapperProps> = ({ resultsCount, children }) => {
    return (
        <Stack direction='row' alignItems='center' justifyContent='space-between'>
            <Stack direction='row' gap={4} alignItems='center'>
                {children}
                <Subtitle1>{`Results: ${resultsCount}`}</Subtitle1>
            </Stack>
        </Stack>
    );
};
