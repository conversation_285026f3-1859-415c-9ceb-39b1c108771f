import { ReviewStatus } from "@eva/shared-common";
import { Chip, Stack, Tooltip } from "@mui/material";
import DifferenceIcon from "@mui/icons-material/Difference";

const statusColors: Record<ReviewStatus, { label: string; color: "default" | "primary" | "secondary" | "error" | "success" | "warning" }> = {
    in_progress: { label: "In Progress", color: "primary" },
    complete: { label: "Completed", color: "success" },
    not_started: { label: "Not Started", color: "warning" },
    not_available: { label: "N/A", color: "default" },
};

interface StatusIndicatorProps {
    status: ReviewStatus;
    isThereDiffs: boolean;
}

export const ReviewStatusIndicator: React.FC<StatusIndicatorProps> = ({ status, isThereDiffs }) => {
    const { label, color } = statusColors[status];

    return (
        <Stack direction='row' gap={2} alignItems='center' justifyContent='center' height='100%'>
            {isThereDiffs && (
                <Tooltip title='There were changes to the original report.'>
                    <DifferenceIcon />
                </Tooltip>
            )}
            <Chip label={label} color={color} />
        </Stack>
    );
};
