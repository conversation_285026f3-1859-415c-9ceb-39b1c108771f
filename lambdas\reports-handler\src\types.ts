import { ReportVersion, InterviewQuestionAnswer } from '@eva/shared-common';

export interface GetReportsRequest {
    jobId: string;
    startDate: Date;
    endDate: Date | null;
}

export interface GetReportsResponse {
    reports: {
        socialId: string | null;
        interviewId: string;
        createdAt: Date;
        jobName: string;
        jobId: string;
        candidateName: string;
        candidateId: string;
        versions: ReportVersion[];
        answers?: (InterviewQuestionAnswer & { trait_id: string })[];
        customAdditionalQuestions: Omit<InterviewQuestionAnswer, 'question_id'>[];
        gender: string | null;
    }[];
}
