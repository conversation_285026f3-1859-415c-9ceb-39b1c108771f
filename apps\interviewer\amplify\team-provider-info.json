{"staging": {"awscloudformation": {"AuthRoleName": "amplify-interviewclient-staging-fc055-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-interviewclient-staging-fc055-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-interviewclient-staging-fc055-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-interviewclient-staging-fc055-deployment", "UnauthRoleName": "amplify-interviewclient-staging-fc055-unauthRole", "StackName": "amplify-interviewclient-staging-fc055", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-interviewclient-staging-fc055/f00a70a0-b7be-11ef-a524-0affed83847b", "AmplifyAppId": "d347b5q9wh9bm", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-interviewclient-staging-fc055-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"interviewclient": {}}, "function": {"InterviewerApiFunction": {"deploymentBucketName": "amplify-interviewclient-staging-fc055-deployment", "s3Key": "amplify-builds/InterviewerApiFunction-2f6b676a2b30692f5550-build.zip"}}, "api": {"InterviewerApi": {}}, "hosting": {"amplifyhosting": {"appId": "d347b5q9wh9bm", "type": "manual"}}, "storage": {"uploads": {}}}}, "dev": {"awscloudformation": {"AuthRoleName": "amplify-interviewclient-dev-ac535-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-interviewclient-dev-ac535-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-interviewclient-dev-ac535-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-interviewclient-dev-ac535-deployment", "UnauthRoleName": "amplify-interviewclient-dev-ac535-unauthRole", "StackName": "amplify-interviewclient-dev-ac535", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-interviewclient-dev-ac535/95506b30-b627-11ef-a999-0ec6584f42a9", "AmplifyAppId": "d347b5q9wh9bm", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-interviewclient-dev-ac535-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"function": {"InterviewerApiFunction": {"deploymentBucketName": "amplify-interviewclient-dev-ac535-deployment", "s3Key": "amplify-builds/InterviewerApiFunction-304f46337873725a524a-build.zip"}}, "auth": {"interviewclient": {}}, "api": {"InterviewerApi": {}}, "storage": {"uploads": {}}, "hosting": {"amplifyhosting": {"appId": "d347b5q9wh9bm", "type": "manual"}}}}}