import { View } from "@react-pdf/renderer";
import { FC } from "react";
import { ProgressBarColor } from "../../PDF.utils";
import { stylesPDF as styles } from "../../PDF.styles";

interface TraitProgressBarFilledSegmentProps {
    idx: number;
    segmentWidth: number;
    filledSegments: number;
    isRtl: boolean;
    colorToRender: ProgressBarColor
    segments: number;
}

export const TraitProgressBarFilledSegment: FC<TraitProgressBarFilledSegmentProps> = ({
    filledSegments,
    idx,
    segmentWidth,
    isRtl,
    colorToRender,
    segments
}) => {
    return (
        <View
            key={`filled-${idx}`}
            style={[
                styles.segment,
                {
                    width: segmentWidth,
                    backgroundColor: colorToRender,
                },
                idx === 0 ? (isRtl ? styles.rightRounded : styles.leftRounded) : {},
                idx === filledSegments - 1 && filledSegments === segments
                    ? isRtl
                        ? styles.leftRounded
                        : styles.rightRounded
                    : {},
            ]}
        />
    );
};
