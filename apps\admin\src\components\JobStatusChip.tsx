import { JobStatus } from "@eva/shared-common";
import { styled } from "@mui/material";
import { Chip } from "@mui/material";
import { FC } from "react";

interface JobStatusChipProps {
    status: JobStatus;
}

const StatusChip = styled(Chip)(({ theme }) => ({
    borderRadius: 16,
    fontWeight: "bold",
}));

export const JobStatusChip: FC<JobStatusChipProps> = ({ status }) => {
    console.log(status);
    const statusLabel = status === "ready" ? "Ready" : status === "pending-definition" ? "Pending Definition" : "N/A";
    const statusColor = status === "ready" ? "success" : status === "pending-definition" ? "warning" : "default";

    return <StatusChip label={statusLabel} color={statusColor} />;
};
