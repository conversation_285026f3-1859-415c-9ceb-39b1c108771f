import { InterviewerQuestion } from "./InteviewerQuestion";
import { SimulationDetails } from "./SimulationDetails";

export type InterviewerJobStep = 
  | { type: "general" ; question: InterviewerQuestion; simulation?: never }
  | { type: "simulation"; simulation: SimulationDetails; question?: never }
  | { type: "warmup"; question: InterviewerQuest<PERSON>; simulation?: never }
  | { type: "open"; question: InterviewerQuestion; simulation?: never };
