{"Description": "API Gateway policy stack created using Amplify CLI", "AWSTemplateFormatVersion": "2010-09-09", "Parameters": {"authRoleName": {"Type": "String"}, "unauthRoleName": {"Type": "String"}, "env": {"Type": "String"}, "InterviewerApi": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"PolicyAPIGWAuth1": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["execute-api:Invoke"], "Resource": [{"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/"]]}]}]}, "Roles": [{"Ref": "authRoleName"}]}}, "PolicyAPIGWUnauth1": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["execute-api:Invoke"], "Resource": [{"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/POST/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/POST/"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "Interview<PERSON><PERSON><PERSON>"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/"]]}]}]}, "Roles": [{"Ref": "unauthRoleName"}]}}}}