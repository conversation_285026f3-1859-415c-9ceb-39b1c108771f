import React from "react";
import { CenteredModal } from "@eva/shared-ui";
import { FormikBulkImportForm } from "./FormikBulkImportForm";

interface BulkImportCandidatesModalProps {
    isOpen: boolean;
    onClose: () => void;
    jobId: string;
    isHebrew: boolean;
    onSuccess: () => void;
}

export const BulkImportCandidatesModal: React.FC<BulkImportCandidatesModalProps> = ({
    isOpen,
    onClose,
    jobId,
    isHebrew,
    onSuccess,
}) => {

    const handleClose = () => {
        onClose();
    };


    return (
        <CenteredModal isOpen={isOpen} onClose={handleClose} width='fit-content' height="fit-content">
            <FormikBulkImportForm
                jobId={jobId}
                isHebrew={isHebrew}
                onSuccess={onSuccess}
                onCancel={handleClose}
            />
        </CenteredModal>
    );
};
