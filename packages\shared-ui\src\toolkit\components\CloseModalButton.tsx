import CloseIcon from "@mui/icons-material/Close";
import { IconButton } from "@mui/material";
import { FC } from "react";

interface CloseModalButtonProps {
    onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export const CloseModalButton: FC<CloseModalButtonProps> = ({ onClick }) => {
    return (
        <IconButton onClick={onClick}>
            <CloseIcon />
        </IconButton>
    );
};
