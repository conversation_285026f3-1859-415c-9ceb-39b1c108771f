import PeopleIcon from "@mui/icons-material/People";
import WorkIcon from "@mui/icons-material/Work";
import { Box, Drawer, List, styled, Toolbar } from "@mui/material";
import { NavigationItem } from "./sub-components/NavigationItem";
import { CustomerLogo } from "./sub-components/CustomerLogo";
import logo from "./logo.png";
import { Subtitle2 } from "@eva/shared-ui";

const drawerWidth = 240;

const StyledDrawer = styled(Drawer)({
    width: drawerWidth,
    flexShrink: 0,
    "& .MuiDrawer-paper": {
        width: drawerWidth,
        boxSizing: "border-box",
        display: "flex",
        flexDirection: "column",
    },
});

const LogoImage = styled("img")({
    width: "auto",
    maxWidth: "35%",
    height: "auto",
    objectFit: "contain",
});

const FooterContainer = styled(Box)(({ theme }) => ({
    marginTop: "auto",
    padding: "16px",
    textAlign: "center",
    display: "flex",
    flexDirection: "row",
    alignItems: "baseline",
    justifyContent: "center",
    gap: "8px",
}));

const NavigationDrawer = () => {
    return (
        <StyledDrawer variant="permanent">
            <CustomerLogo />
            <Toolbar />
            <List>
                <NavigationItem to="/jobs" icon={<WorkIcon />} text="Jobs" />
                <NavigationItem to="/candidates" icon={<PeopleIcon />} text="Candidates" />
            </List>
            <FooterContainer>
                <Subtitle2>Powered by</Subtitle2>
                <LogoImage src={logo} alt="Logo" />
            </FooterContainer>
        </StyledDrawer>
    );
};

export default NavigationDrawer;
