import { Stack } from "@mui/material";
import { FC } from "react";
import { useInterviewer } from "../../../../../../../InterviewerProvider";
import { PrimaryButton, MobilePrimaryButton, useMediaQueryContext } from "@eva/shared-ui";
import { SimulationScreen } from "../SimulationStep";


interface SimulationControlButtonProps {
    currentScreen: SimulationScreen;
    goToScreen: (targetScreen: SimulationScreen) => void;
    isStoryVideoDone: boolean;
    hasStoryVideo: boolean; 
}

export const SimulationControlButton: FC<SimulationControlButtonProps> = ({
    currentScreen,
    goToScreen,
    isStoryVideoDone,
    hasStoryVideo,
}) => {
    const { data } = useInterviewer();
    const { isMobile } = useMediaQueryContext();
    
    const Button = isMobile ? MobilePrimaryButton : PrimaryButton;

    if (!data) {
        return null;
    }

    const lang = data.language;

    const labels = {
        he: {
            startSimulation: "התחל סימולציה",
            showStory: "הצג מקרה",
            showQuestion: "הצג שאלה",
            showExplanation: "הצג הסבר",
        },
        en: {
            startSimulation: "Start Simulation",
            showStory: "Show Story",
            showQuestion: "Show Question",
            showExplanation: "Show Explanation",
        },
    };

    const currentLabels = lang === "he" ? labels.he : labels.en;

    const isProceedFromStoryDisabled = hasStoryVideo && !isStoryVideoDone;

    return (
        <Stack direction="row" justifyContent="center" gap={2} mt={3}>
            {currentScreen === "explanation" && (
                <Button
                    variant="contained"
                    width="200px"
                    content={currentLabels.showStory}
                    onClick={() => goToScreen("story")}
                />
            )}
            {currentScreen === "story" && (
                <>
                    <Button
                        variant="outlined"
                        width="200px"
                        content={currentLabels.showExplanation}
                        onClick={() => goToScreen("explanation")}
                    />
                    <Button
                        disabled={isProceedFromStoryDisabled}
                        variant="contained"
                        width="200px"
                        content={currentLabels.showQuestion}
                        onClick={() => goToScreen("question")}
                    />
                </>
            )}
            {currentScreen === "question" && (
                <>
                    <Button
                        variant="outlined"
                        width="200px"
                        content={currentLabels.showStory}
                        onClick={() => goToScreen("story")}
                    />
                </>
            )}
        </Stack>
    );
};
