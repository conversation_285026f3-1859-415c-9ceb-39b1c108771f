import { NextFunction, Request, Response, Router } from "express";
import { getCustomerService, getLogger } from "../config/initialize";
import { CMCustomerThemeResponse } from "@eva/shared-common";

const router = Router();
const logger = getLogger();

router.get(
    "/logo/:customerId",
    async (req: Request<{ customerId: string }, {}, {}, {}>, res: Response, next: NextFunction) => {
        try {
            const customerService = getCustomerService();
            const { customerId } = req.params;
            const customerLogo = await customerService.getCustomerLogo(customerId);
            res.json(customerLogo);
        } catch (error) {
            logger.error("Error getting customer logo");
            next(error);
        }
    }
);

router.get(
    "/:customerId/theme",
    async (
        req: Request<{ customerId: string }, {}, {}, {}>,
        res: Response<CMCustomerThemeResponse, {}>,
        next
    ) => {
        try {
            const { customerId } = req.params;
            
            if (!customerId) {
                return res.status(400).json({ themeColor: null });
            }
            
            const customerService = getCustomerService();
            const result = await customerService.getCustomerTheme(customerId);
            
            res.json(result);
        } catch (error) {
            logger.error(`Error getting customer theme: ${error}`);
            next(error);
        }
    }
);

export default router;
