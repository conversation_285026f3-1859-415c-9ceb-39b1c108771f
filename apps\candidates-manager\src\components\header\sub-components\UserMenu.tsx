import { useAuthenticator } from "@aws-amplify/ui-react";
import { Body1 } from "@eva/shared-ui";
import { Avatar } from "@mui/material";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Tooltip from "@mui/material/Tooltip";
import React from "react";

export const UserMenu = () => {
    const { user, signOut } = useAuthenticator();
    const [anchorElUser, setAnchorElUser] = React.useState<null | HTMLElement>(null);

    const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleLogoutClick = () => {
        setAnchorElUser(null);
        signOut();
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const avatarSrc =`https://robohash.org/${user?.username}`;

    return (
        <Box sx={{ flexGrow: 0 }}>
            <Tooltip title='Open settings'>
                <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                    <Avatar alt={user?.username}  src={avatarSrc} sx={{bgcolor:'Background'}}  />
                </IconButton>
            </Tooltip>
            <Menu
                sx={{ mt: "45px" }}
                id='menu-appbar'
                anchorEl={anchorElUser}
                anchorOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
                keepMounted
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
            >
                <MenuItem key='logout' onClick={handleLogoutClick}>
                    <Body1>Logout</Body1>
                </MenuItem>
            </Menu>
        </Box>
    );
}; 