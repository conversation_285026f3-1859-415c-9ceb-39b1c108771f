import React from 'react';
import { Box, Typography, Button, styled } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useTheme } from '@mui/material/styles';

interface ErrorMessageProps {
  message?: string;
  onRetry?: () => void;
}

const ErrorContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(4),
  textAlign: 'center',
  height: '100%',
  minHeight: '200px',
  width: '100%',
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  color: theme.palette.error.main,
}));

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message = "Something went wrong. Please try again later.", 
  onRetry 
}) => {
  const theme = useTheme();

  return (
    <ErrorContainer>
      <IconWrapper>
        <ErrorOutlineIcon sx={{ fontSize: 64 }} />
      </IconWrapper>
      <Typography variant="h5" gutterBottom sx={{ color: theme.palette.error.main }}>
        Error
      </Typography>
      <Typography variant="body1" color="textSecondary" sx={{ maxWidth: '500px', mb: 3 }}>
        {message}
      </Typography>
      {onRetry && (
        <Button 
          variant="contained" 
          color="primary" 
          onClick={onRetry}
        >
          Try Again
        </Button>
      )}
    </ErrorContainer>
  );
}; 