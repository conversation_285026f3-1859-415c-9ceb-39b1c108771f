import { Grid, Typography, Box } from "@mui/material";
import { useMediaQueryContext, BackdropLoading } from "@eva/shared-ui";
import { InterviewerProvider } from "./InterviewerProvider";
import { styled } from "@mui/material/styles";
import { lazy, Suspense, useState } from "react";
import { TermsAndConditionsModal } from "../../components/TermsAndConditionsModal";

const CameraPanel = lazy(() => import("./sub-components/camera-panel/CameraPanel"));
const InterviewPanel = lazy(() => import("./sub-components/interview-panel/InterviewPanel"));

const StyledGrid = styled(Grid)(() => ({
    overflow: "hidden",
    display: "flex",
    height: "100dvh",
    width: "100dvw",
    margin: 0,
    padding: 0,
}));

const AnimationSafeContainer = styled("div")({
    position: "relative",
    overflow: "hidden",
    width: "100dvw",
    height: "100dvh",
    display: "flex",
    flexDirection: "column",
});

const InterviewPage = () => {
    const { isMobile } = useMediaQueryContext();

    

    return (
        <AnimationSafeContainer>
            <InterviewerProvider>
                <StyledGrid
                    container
                    direction={isMobile ? "column" : "row"}
                    flexDirection={isMobile ? "column-reverse" : "initial"}
                >
                    <Suspense fallback={<BackdropLoading isLoading />}>
                        <InterviewPanel />
                        <CameraPanel />
                    </Suspense>
                </StyledGrid>
            </InterviewerProvider>
        </AnimationSafeContainer>
    );
};

export default InterviewPage;
