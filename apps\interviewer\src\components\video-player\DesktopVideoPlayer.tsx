import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { StyledVideo } from "./VideoPlayer.styles";
import { VideoPlayerProps } from "./VideoPlayer";

export const DesktopVideoPlayer = ({
    controls,
    videoUrl,
    width,
    "data-qa": dataQa,
    onVideoEnd,
    onVideoStart,
    hidden,
    autoPlayDisabled,
}: VideoPlayerProps) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isControlsVisible, setIsControlsVisible] = useState(controls ? true : false);

    useLayoutEffect(() => {
        if (videoRef.current) {
            videoRef.current.src = videoUrl;
            videoRef.current.load();
        }
    }, [videoUrl]);

    const handleVideoEnd = () => {
        if (onVideoEnd) onVideoEnd();
        setIsControlsVisible(true);
    };

    useEffect(() => {
        const videoElement = videoRef.current;

        if (videoElement) {
            videoElement.addEventListener("ended", handleVideoEnd);
        }

        return () => {
            if (videoElement) {
                videoElement.removeEventListener("ended", handleVideoEnd);
            }
        };
    }, [onVideoEnd, onVideoStart, videoUrl]);

    return (
        <StyledVideo
            controlsList="nodownload"
            data-qa={dataQa}
            ref={videoRef}
            autoPlay={autoPlayDisabled ? false : true}
            controls={isControlsVisible}
            width={width}
            height={width}
            hidden={hidden}
        />
    );
}; 