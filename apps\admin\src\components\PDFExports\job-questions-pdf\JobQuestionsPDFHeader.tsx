import { FC } from "react";
import { stylesPDF as styles } from "../PDF.styles";
import { Text, View } from "@react-pdf/renderer";
import { CustomerSVGIcon } from "../report-pdf/sub-components/icons/CustomerSVGIcon";
import { DateSVGIcon } from "../report-pdf/sub-components/icons/DateSVGIcon";

interface JobQuestionsPDFHeaderProps {
    isRtl: boolean;
    jobTitle: string;
    companyName: string;
    createdAt: string;
}

export const JobQuestionsPDFHeader: FC<JobQuestionsPDFHeaderProps> = ({ companyName, createdAt, isRtl, jobTitle }) => {
    return (
        <View style={[styles.sectionContainer]}>
            <View
                style={[
                    styles.displayFlex,
                    styles.flewDirectionRow,
                    styles.spaceBetween,
                    styles.alignItemsCenter,
                    isRtl ? styles.flexReverse : {},
                ]}
            >
                <View style={[styles.displayFlex, styles.flexGap4, styles.flexDirectionColumn, isRtl ? styles.alignEnd : {}]}>
                    <Text style={isRtl ? styles.hebrewTextBoldHeader : styles.textBoldMainHeader}>{jobTitle}</Text>
                    <View style={[styles.flewDirectionRow, styles.displayFlex, isRtl ? styles.flexReverse : {}]}>
                        <View
                            style={[
                                styles.flewDirectionRow,
                                styles.displayFlex,
                                styles.flexGap2,
                                styles.alignItemsCenter,
                                !isRtl ? styles.rightBorderLine : {},
                                isRtl ? styles.paddingLeft8 : {},
                            ]}
                        >
                            <CustomerSVGIcon />
                            <Text style={isRtl ? styles.hebrewTextSubTitle : styles.textSubTitle}>{companyName}</Text>
                        </View>
                        <View
                            style={[
                                isRtl ? styles.paddingRight8 : styles.paddingLeft8,
                                styles.flewDirectionRow,
                                styles.displayFlex,
                                styles.flexGap2,
                                styles.alignItemsCenter,
                            ]}
                        >
                            <DateSVGIcon />
                            <Text style={isRtl ? styles.hebrewTextSubTitle : styles.textSubTitle}>{createdAt}</Text>
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};
