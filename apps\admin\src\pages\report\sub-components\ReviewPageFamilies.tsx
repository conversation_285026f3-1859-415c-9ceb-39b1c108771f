import { Paper, Stack } from "@mui/material";
import { useAppText } from "@eva/shared-ui";
import { useReviewPageContext } from "../context/ReviewPageProvider";
import { ReviewPageEditMode } from "./ReviewPageEditMode";
import { ReviewPageFamilyDetails } from "./ReviewPageFamilyDetails";
import { ReviewPageTraitScores } from "./ReviewPageTraitScore";
import { FamilyId, Trait } from "@eva/shared-common";

export const ReviewPageFamilies = () => {
    const { currentLanguage } = useAppText();
    const { isEditMode, editRequest, interviewData, familiesWithTraitsMap, handleTraitChange, handleFamiliesChange } = useReviewPageContext();
    const families = isEditMode && editRequest ? editRequest.families : interviewData ? interviewData.report.families : [];

    const handleTraitChangeWrapper = (trait: Trait, familyCode: string) => {
        handleTraitChange(trait, familyCode);
    };

    const sortedFamilies = [...families].sort((a, b) => a.code.localeCompare(b.code));
    const sortedEditRequestFamilies = editRequest ? [...editRequest?.families].sort((a, b) => a.code.localeCompare(b.code)) : [];
    return (
        <Stack padding={3} gap={3}>
            {sortedFamilies.map((family, idx) => (
                <Paper key={family.code}>
                    {interviewData && (
                        <Stack direction='row' justifyContent='space-between'>
                            {familiesWithTraitsMap && interviewData && (
                                <ReviewPageFamilyDetails
                                    language={interviewData.interview.language}
                                    familyName={familiesWithTraitsMap[family.code as FamilyId].family[currentLanguage]}
                                    score={family.score}
                                    summary={sortedFamilies[idx].summary}
                                />
                            )}
                            {isEditMode && editRequest && (
                                <ReviewPageEditMode
                                    language={interviewData.interview.language}
                                    handleFamilyChange={handleFamiliesChange}
                                    family={sortedEditRequestFamilies[idx]}
                                />
                            )}
                        </Stack>
                    )}
                    {familiesWithTraitsMap && interviewData && (
                        <ReviewPageTraitScores
                            traitNamesToRender={familiesWithTraitsMap[family.code].traitsMap}
                            isEditMode={isEditMode}
                            traits={interviewData.report.traits}
                            editRequest={editRequest}
                            handleTraitChange={(trait) => handleTraitChangeWrapper(trait, family.code)}
                        />
                    )}
                </Paper>
            ))}
        </Stack>
    );
};
