import { AnalyzerTextCorrection } from "./AnalyzerTextCorrection";
import { ComputedTrait } from "./ComputedTrait";
import { QuestionAnswerPairForAnalyzer, TemplatesMap } from "./QuestionAnswerPairForAnalyzer";

export interface AnalyzerDataForEvaluation {
    questionAnswerPairs: QuestionAnswerPairForAnalyzer[];
    translationCorrections: AnalyzerTextCorrection[];
    interviewsSummariesMap: Record<number, string[]>;
    promptTemplates: TemplatesMap;
    language: string;
    computedTraits:ComputedTrait[]
}
