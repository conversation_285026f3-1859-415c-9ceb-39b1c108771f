import { CircularProgress } from "@mui/material";
import { FC } from "react";
import { useGetCandidateScoreQuery } from "../../../app-state/apis/candidatesManagerApi";
interface CandidateScoreProps {
    candidateId: string;
}

export const CandidateScore: FC<CandidateScoreProps> = ({ candidateId }) => {
    const { data, isLoading, error } = useGetCandidateScoreQuery(candidateId);


    if (isLoading) {
        return <CircularProgress size={20} />;
    }

    if (error) {
        return <span>Error</span>;
    }

    if (!data || !data.score) {
        return <span>N/A</span>;
    }

    return <span>{data?.score}</span>;
};
