import Countdown, { CountdownRenderProps } from "react-countdown";
import { CountdownContainer, TimeText } from "./StopWatchCountdown.styles";


const renderer = ({ completed, seconds, minutes }: CountdownRenderProps) => {
  if (!completed) {
    return (
      <CountdownContainer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.05 }}
      >
        <TimeText>{minutes}:{seconds < 10 ? `0${seconds}` : seconds}</TimeText>
      </CountdownContainer>
    );
  } else {
    return (
      <CountdownContainer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        whileHover={{ scale: 1.05 }}
      >
        <TimeText>0</TimeText>
      </CountdownContainer>
    );
  }
};

interface StopRecordingCountdown {
  onComplete: () => void
  duration: number
}

export const StopWatchCountdown = ({ onComplete, duration }: StopRecordingCountdown) => {
  return (
    <Countdown
      onComplete={onComplete}
      date={Date.now() + duration}
      renderer={renderer}
    />
  )
}