import { TableJobData } from "@eva/shared-common";
import { Stack } from "@mui/material";
import { useLayoutEffect, useState } from "react";
import { useGetJobsPageTableDataQuery } from "../../app-state/apis";
import { LoadingScreen, PageContainer, PageLayout, useLocalStorageState } from "@eva/shared-ui";
import { JobsDataTable } from "./sub-components/JobsDataTable";
import { JobsDataTableFilter } from "./sub-components/JobsDataTableFilter";

export interface JobsFilterFields {
    searchText: string;
    company: string;
    account: string;
}

const JobsPage = () => {
    const { data, isLoading } = useGetJobsPageTableDataQuery();
    const [filterFields, setFilterFields] = useLocalStorageState<JobsFilterFields>("jobs-filter", {
        searchText: "",
        company: "",
        account: "",
    });
    const [filteredJobs, setFilteredJobs] = useState<TableJobData[]>(data?.jobs ? data.jobs : []);

    const handleFilterFieldsChange = (filterFields: JobsFilterFields) => {
        setFilterFields(filterFields);
    };

    useLayoutEffect(() => {
        if (data) {
            const jobs = data.jobs;
            const filtered = jobs.filter(
                (job) =>
                    job.jobTitle.toLowerCase().includes(filterFields.searchText.toLowerCase()) &&
                    job.companyName.toLowerCase().includes(filterFields.company.toLowerCase()) &&
                    job.accountName.toLowerCase().includes(filterFields.account.toLowerCase())
            );
            setFilteredJobs(filtered);
        }
    }, [data, filterFields]);

    return (
        <PageContainer>
            {isLoading ? (
                <LoadingScreen />
            ) : (
                <PageLayout>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                        {data && (
                            <JobsDataTableFilter
                                filterFields={filterFields}
                                handleFilterChange={handleFilterFieldsChange}
                                jobs={data.jobs}
                                resultsCount={filteredJobs.length}
                            />
                        )}
                    </Stack>
                    <JobsDataTable jobs={filteredJobs} />
                </PageLayout>
            )}
        </PageContainer>
    );
};

export default JobsPage;
