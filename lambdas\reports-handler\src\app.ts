import { closeDBConnection, DB, initDB } from '@eva/drizzle';
import { setupLogging } from '@eva/logger';
import { CloudfrontAccessPair, getSecret } from '@eva/aws-utils';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ReportHandler } from './ReportHandler';
import { GetReportsRequest } from './types';

let db: DB | null = null;
let cloudfrontSignedUrlSecret: CloudfrontAccessPair | null = null;

export const lambdaHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const logger = setupLogging();
    if (!db) {
        db = await initDB();
    }
    if (!cloudfrontSignedUrlSecret) {
        logger.info('Initializing cloudfront signed url secret');
        cloudfrontSignedUrlSecret = await getSecret<CloudfrontAccessPair>('cloudfront_access_pair');
        logger.info('Cloudfront signed url secret: ', cloudfrontSignedUrlSecret);
    }

    try {
        const reportHandler = new ReportHandler(db, logger, cloudfrontSignedUrlSecret);
        const response = await reportHandler.getReports(JSON.parse(event.body!) as GetReportsRequest);
        return {
            statusCode: 200,
            body: JSON.stringify(response),
        };
    } catch (err) {
        const error = err as Error;
        logger.error('Error processing interview event');
        logger.error(error.stack);
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'error' }),
        };
    } finally {
        closeDBConnection();
    }
};
