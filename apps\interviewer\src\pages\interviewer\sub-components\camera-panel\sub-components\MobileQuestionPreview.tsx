import { Dialog } from "@mui/material";
import { FC, useEffect } from "react";
import { useMediaQueryContext } from "@eva/shared-ui";
import { MobileVideoPlayer } from "../../../../../components/video-player/MobileVideoPlayer";
import { useInterviewer } from "../../../InterviewerProvider";

interface MobileQuestionPreviewProps {
    isRecording: boolean;
}

export const MobileQuestionPreview: FC<MobileQuestionPreviewProps> = ({ isRecording }) => {
    const { isMobile } = useMediaQueryContext();
    const { data, isInterviewOn, currentStep, handleCountDownChange, isCountingDown, isUploading } = useInterviewer();

    const onVideoEnd = () => {
        handleCountDownChange(true);
    };

    const shouldRenderVideo =
        isMobile && isInterviewOn && data && currentStep && !isCountingDown && !isRecording && !isUploading;

    useEffect(() => {
        if (
            currentStep &&
            isInterviewOn &&
            !currentStep?.question?.videoLink &&
            !isCountingDown &&
            !isRecording &&
            !isUploading
        ) {
            onVideoEnd();
        }
    }, [currentStep]);

    return (
        <>
            {currentStep && currentStep?.question?.videoLink && (
                <Dialog open={shouldRenderVideo ? shouldRenderVideo : false} fullWidth={true} fullScreen={true}>
                    <MobileVideoPlayer
                        onVideoEnd={onVideoEnd}
                        data-qa="question-video-preview-mobile"
                        videoUrl={currentStep?.question?.videoLink!}
                    />
                </Dialog>
            )}
        </>
    );
};
