import { styled } from "@mui/material";
import React from "react";

interface AbsolutePositionWrapperContainerProps {
    top?: string;
    left?: string;
    right?: string;
    bottom?: string;
    zIndex?: number;
    fromEdge?: boolean;
}

const AbsolutePositionWrapperContainer = styled("div")<AbsolutePositionWrapperContainerProps>(
    ({ left, top, zIndex, bottom, right, fromEdge }) => ({
        position: "absolute",
        transform: fromEdge ? undefined : "translate(-50%, -50%)",
        zIndex: zIndex || 1,
        left,
        top,
        right,
        bottom,
    })
);

interface AbsolutePositionWrapperProps extends AbsolutePositionWrapperContainerProps {
    children: React.ReactNode;
    fromEdge?: boolean;
}

export const AbsolutePositionWrapper = (props: AbsolutePositionWrapperProps) => {
    const { children, ...rest } = props;
    return <AbsolutePositionWrapperContainer {...rest}>{children}</AbsolutePositionWrapperContainer>;
};
