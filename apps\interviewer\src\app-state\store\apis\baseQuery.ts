import { get, post, del  } from "aws-amplify/api";
import { BaseQueryArgs } from "./types";

export const baseQuery = async ({ url, method, body }: BaseQueryArgs) => {
    try {
        let data;
        let status;
        switch (method) {
            case "GET":
                const restAction = get({
                    apiName: "InterviewerApi",
                    path: url,
                });
                const response = await restAction.response;
                data = await response.body.json();
                status = response.statusCode;
                break;
            case "POST":
                const postAction = post({
                    apiName: "InterviewerA<PERSON>",
                    path: url,
                    options: { body },
                });
                const postResponse = await postAction.response;
                data = await postResponse.body.json();
                status = postResponse.statusCode;
                break;
            case "DELETE":
                const deleteAction = del({
                    apiName: "InterviewerApi",
                    path: url,
                });
                const deleteResponse = await deleteAction.response;
                data = deleteResponse.statusCode;
                status = deleteResponse.statusCode;
                break;
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
        return { data, meta: { status } };
    } catch (error: any) {
        let status = 500;
        let errorMessage = "An unknown error occurred";

        if (error?.response) {
            status = error.response.statusCode || error.response.status || 500;
            if (error.response.data) {
                errorMessage = error.response.data;
            }
        } else if (error?.statusCode) {
            status = error.statusCode;
            errorMessage = error.message || "Error occurred";
        } else if (error?.message) {
            errorMessage = error.message;
        }

        return {
            error: {
                status,
                data: errorMessage,
            },
        };
    }
};
