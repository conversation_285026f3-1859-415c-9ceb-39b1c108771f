import { getSignedUrl } from "@aws-sdk/cloudfront-signer";

export interface CloudfrontAccessPair {
    CLOUD_FRONT_PRIVATE_KEY: string;
    CLOUD_FRONT_PAIR_ID: string;
}


export const generateCloudfrontSignedUrl = async (url: string, expirationHours: number, secret: CloudfrontAccessPair): Promise<string> => {
    let privateKey = secret.CLOUD_FRONT_PRIVATE_KEY.trim();
    privateKey = privateKey.replace("-----BEGIN RSA PRIVATE KEY----- ", "-----BEGIN RSA PRIVATE KEY-----\n");
    privateKey = privateKey.replace(" -----END RSA PRIVATE KEY-----", "\n-----END RSA PRIVATE KEY-----");

    const expiration = new Date();
    expiration.setHours(expiration.getHours() + expirationHours);

    const signedUrl = getSignedUrl({
        url,
        keyPairId: secret.CLOUD_FRONT_PAIR_ID,
        dateLessThan: expiration.toISOString(),
        privateKey,
    });

    return signedUrl;
};
