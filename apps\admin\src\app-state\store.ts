import { configureStore } from "@reduxjs/toolkit";
import { 
    accountsApi, 
    customersApi, 
    jobsApi, 
    interviewsApi,
    videosApi,
    promptsApi
} from "./apis";

export const store = configureStore({
    reducer: {
        [accountsApi.reducerPath]: accountsApi.reducer,
        [customersApi.reducerPath]: customersApi.reducer,
        [jobsApi.reducerPath]: jobsApi.reducer,
        [interviewsApi.reducerPath]: interviewsApi.reducer,
        [videosApi.reducerPath]: videosApi.reducer,
        [promptsApi.reducerPath]: promptsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware()
            .concat(accountsApi.middleware)
            .concat(customersApi.middleware)
            .concat(jobsApi.middleware)
            .concat(interviewsApi.middleware)
            .concat(videosApi.middleware)
            .concat(promptsApi.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
