{"dev": {"awscloudformation": {"Profile": "default", "AuthRoleName": "amplify-iverseadminclient-dev-69e19-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-dev-69e19-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-dev-69e19-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-iverseadminclient-dev-69e19-deployment", "UnauthRoleName": "amplify-iverseadminclient-dev-69e19-unauthRole", "StackName": "amplify-iverseadminclient-dev-69e19", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-iverseadminclient-dev-69e19/3d8a3660-3936-11ef-8486-0afff1f14c4f", "AmplifyAppId": "d2trb4<PERSON>nygq6", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-iverseadminclient-dev-69e19-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"iverseadminclient": {}, "userPoolGroups": {}}, "function": {"IverseAdminExpressFunction": {"deploymentBucketName": "amplify-iverseadminclient-dev-69e19-deployment", "s3Key": "amplify-builds/IverseAdminExpressFunction-4e67683332702b533971-build.zip"}}, "api": {"IverseAdminExpressApi": {}}, "hosting": {"amplifyhosting": {"appId": "d2trb4<PERSON>nygq6", "type": "manual"}}, "storage": {"adminUploads": {}, "uploads": {}}}}, "staging": {"awscloudformation": {"AuthRoleName": "amplify-iverseadminclient-staging-e62dc-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-staging-e62dc-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-staging-e62dc-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-iverseadminclient-staging-e62dc-deployment", "UnauthRoleName": "amplify-iverseadminclient-staging-e62dc-unauthRole", "StackName": "amplify-iverseadminclient-staging-e62dc", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-iverseadminclient-staging-e62dc/8cdb6330-4546-11ef-b611-0e0a5af16d8f", "AmplifyAppId": "d2trb4<PERSON>nygq6", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-iverseadminclient-staging-e62dc-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"iverseadminclient": {}, "userPoolGroups": {}}, "function": {"IverseAdminExpressFunction": {"deploymentBucketName": "amplify-iverseadminclient-staging-e62dc-deployment", "s3Key": "amplify-builds/IverseAdminExpressFunction-7a41593959444e314475-build.zip"}}, "hosting": {"amplifyhosting": {"appId": "d2trb4<PERSON>nygq6", "type": "manual"}}, "api": {"iverseAdminApi": {}, "IverseAdminExpressApi": {}}, "storage": {"iverseAdminUploadedVideos": {}, "uploads": {}}}}, "prod": {"awscloudformation": {"AuthRoleName": "amplify-iverseadminclient-prod-22cd0-authRole", "UnauthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-prod-22cd0-unauthRole", "AuthRoleArn": "arn:aws:iam::405424696292:role/amplify-iverseadminclient-prod-22cd0-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-iverseadminclient-prod-22cd0-deployment", "UnauthRoleName": "amplify-iverseadminclient-prod-22cd0-unauthRole", "StackName": "amplify-iverseadminclient-prod-22cd0", "StackId": "arn:aws:cloudformation:us-east-1:405424696292:stack/amplify-iverseadminclient-prod-22cd0/b619f3c0-74ed-11ef-a71e-0e07dea7e4d3", "AmplifyAppId": "d2trb4<PERSON>nygq6", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-iverseadminclient-prod-22cd0-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"iverseadminclient": {}, "userPoolGroups": {}}, "hosting": {"amplifyhosting": {"appId": "d2trb4<PERSON>nygq6", "type": "manual"}}, "api": {"iverseAdminApi": {}, "IverseAdminExpressApi": {}}, "storage": {"iverseAdminUploadedVideos": {}, "uploads": {}}, "function": {"IverseAdminExpressFunction": {"deploymentBucketName": "amplify-iverseadminclient-prod-22cd0-deployment", "s3Key": "amplify-builds/IverseAdminExpressFunction-2b32317943585654426d-build.zip"}}}}}