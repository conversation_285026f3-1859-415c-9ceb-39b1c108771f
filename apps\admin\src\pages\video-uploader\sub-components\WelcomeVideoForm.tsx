import { UploadVideoRequest, FamiliesWithQuestionsForBuilderResponse, CustomersResponse } from "@eva/shared-common";
import { FC, useState } from "react";
import { v4 as uuidv4 } from 'uuid';
import { uploadData } from "aws-amplify/storage";
import { toast } from "react-toastify";
import { useUploadVideoMutation, useLazyValidateVideoQuery } from "../../../app-state/apis";
import { Formik, FormikProps, FormikHelpers } from "formik";
import * as Yup from "yup";
import { WelcomeVideoFormBody } from "./WelcomeVideoFormBody";

export interface WelcomeVideoFormValues {
    language: string;
    customerId: string;
    gender: string;
    videoFile: File | null;
}

interface WelcomeVideoFormProps {
    questionsData: FamiliesWithQuestionsForBuilderResponse;
    customersData: CustomersResponse;
}

const WelcomeVideoValidationSchema = Yup.object().shape({
    language: Yup.string().required("Language is required"),
    customerId: Yup.string().required("Customer is required"),
    gender: Yup.string().when("language", {
        is: "he",
        then: (schema) => schema.required("Gender is required when Hebrew is selected"),
        otherwise: (schema) => schema
    }),
    videoFile: Yup.mixed()
        .required("Video file is required")
        .test("is-file", "Must be a valid video file", (value) => {
            return value instanceof File;
        })
});

export const WelcomeVideoForm: FC<WelcomeVideoFormProps> = ({ questionsData, customersData }) => {
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [uploadVideo] = useUploadVideoMutation();
    const [validateVideo] = useLazyValidateVideoQuery();

    const initialValues: WelcomeVideoFormValues = {
        language: "",
        customerId: "",
        gender: "all",
        videoFile: null
    };

    const handleSubmit = async (
        values: WelcomeVideoFormValues, 
        { resetForm, setSubmitting }: FormikHelpers<WelcomeVideoFormValues>
    ) => {
        try {
            const videoKey = `public/uploads/intros/${values.customerId}-${values.language}-intro-${uuidv4()}.mp4`;
            
            const uploadRequest: UploadVideoRequest = {
                type: "intro",
                customerId: values.customerId,
                key: videoKey,
                language: values.language,
                questionId: "",
                gender: values.gender,
            };
            
            const { isValid } = await validateVideo(uploadRequest).unwrap();
            if (!isValid) {
                toast.error(`Welcome video for this language and customer already exists`);
                setSubmitting(false);
                return;
            }
            
            if (values.videoFile && isValid) {
                await uploadData({
                    path: videoKey,
                    data: values.videoFile,
                    options: {
                        contentType: "video/mp4",
                        onProgress: (progress) => {
                            if (progress.transferredBytes && progress.totalBytes) {
                                setUploadProgress((progress.transferredBytes / progress.totalBytes) * 100);
                            } else {
                                setUploadProgress(0);
                            }
                        },
                    },
                }).result;
                
                await uploadVideo(uploadRequest).unwrap();
                
                resetForm();
                setUploadProgress(0);
                toast.success("Welcome video uploaded successfully");
            }
        } catch (error) {
            console.error("Upload failed:", error);
            toast.error("Failed to upload welcome video. Please try again.");
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={WelcomeVideoValidationSchema}
            onSubmit={handleSubmit}
        >
            {(formikProps: FormikProps<WelcomeVideoFormValues>) => (
                <WelcomeVideoFormBody 
                    questionsData={questionsData} 
                    customersData={customersData} 
                    isSubmitting={formikProps.isSubmitting}
                    uploadProgress={uploadProgress}
                />
            )}
        </Formik>
    );
}; 