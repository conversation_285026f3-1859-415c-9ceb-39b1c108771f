import { FC } from "react";
import { JobBuilderQuestionFields } from "./JobBuilderQuestionFields";

interface JobBuilderOpenQuestionsProps {
    disabled: boolean;
}

export const JobBuilderOpenQuestions: FC<JobBuilderOpenQuestionsProps> = ({ disabled }) => {
    return (
        <JobBuilderQuestionFields 
            disabled={disabled}
            fieldName="openQuestions"
            titleKey="jobBuilderForm.openQuestions.title"
            addButtonKey="jobBuilderForm.openQuestions.addQuestion"
            removeButtonKey="jobBuilderForm.openQuestions.removeQuestion"
        />
    );
};
