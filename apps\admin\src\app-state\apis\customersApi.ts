import { createApi } from "@reduxjs/toolkit/query/react";
import { 
    CustomersResponse, 
    CustomersPageTableDataResponse, 
    CreateCustomerRequest, 
    ActionStatus,
    CustomerLogoLinkResponse
} from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const customersApi = createApi({
    reducerPath: "customersApi",
    baseQuery,
    tagTypes: ["CustomersResponse", "CustomersTableData", "CustomerLogo"],
    endpoints: (builder) => ({
        getCustomersByAccountId: builder.query<CustomersResponse, string>({
            query: (accountId: string) => ({ url: `/customers-by-account-id/${accountId}`, method: "GET" }),
            providesTags: ["CustomersResponse"],
        }),
        getCustomersPageTableData: builder.query<CustomersPageTableDataResponse, void>({
            query: () => ({ url: "/customers-page-table-data", method: "GET" }),
            providesTags: ["CustomersTableData"],
        }),
        createCustomer: builder.mutation<ActionStatus, CreateCustomerRequest>({
            query: (customerData) => ({
                url: "/create-customer",
                method: "POST",
                body: customerData,
            }),
            invalidatesTags: ["CustomersTableData", "CustomerLogo"],
        }),
        getCustomerLogo: builder.query<CustomerLogoLinkResponse, string>({
            query: (customerId) => ({
                url: `/customer-logo/${customerId}`,
                method: "GET",
            }),
            providesTags: ["CustomerLogo"],
        }),
    }),
});

export const {
    useGetCustomersByAccountIdQuery,
    useGetCustomersPageTableDataQuery,
    useCreateCustomerMutation,
    useGetCustomerLogoQuery,
} = customersApi; 