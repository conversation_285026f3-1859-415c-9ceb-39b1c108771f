import { Stack } from "@mui/material";
import { useParams } from "react-router-dom";
import { useGetCandidateByIdQuery } from "../../app-state/apis/candidatesManagerApi";
import { BackButton, ErrorPage, LoadingScreen, PrimaryButton, PageContainer, PageLayout } from "@eva/shared-ui";
import { useEffect, useState } from "react";
import { CandidateDetails } from "./sub-components/CandidateDetails";
import { EditCandidateModal } from "./sub-components/EditCandidateModal";
import { usePageTitle } from "@eva/shared-ui";

const CandidateDetailsPage = () => {
    const { candidateId } = useParams<{ candidateId: string }>();
    const { setSubtitle } = usePageTitle();
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);

    const {
        data: candidate,
        isLoading,
        error,
        refetch,
    } = useGetCandidateByIdQuery(candidateId ?? "", {
        skip: !candidateId,
    });

    useEffect(() => {
        if (candidate?.name) {
            setSubtitle(candidate.name);
        }

        return () => {
            setSubtitle(null);
        };
    }, [candidate?.name, setSubtitle]);

    const handleEditCandidate = () => {
        setIsEditModalOpen(true);
    };

    const handleCloseEditModal = () => {
        setIsEditModalOpen(false);
    };

    if (!candidateId) {
        return <ErrorPage message="Candidate ID is missing" onRetry={refetch} />;
    }

    if (isLoading) {
        return <LoadingScreen />;
    }

    if (error) {
        return <ErrorPage message="Failed to load candidate details" onRetry={refetch} />;
    }

    if (!candidate) {
        return <ErrorPage message="Candidate not found" onRetry={refetch} />;
    }

    return (
        <PageContainer>
            <PageLayout transparent>
                <Stack spacing={3}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <BackButton />
                        <PrimaryButton
                            content="Edit Candidate"
                            onClick={handleEditCandidate}
                            variant="contained"
                            width="auto"
                        />
                    </Stack>
                    <CandidateDetails candidateId={candidateId} />
                </Stack>
            </PageLayout>
            <EditCandidateModal isOpen={isEditModalOpen} onClose={handleCloseEditModal} candidate={candidate} />
        </PageContainer>
    );
};

export default CandidateDetailsPage;
