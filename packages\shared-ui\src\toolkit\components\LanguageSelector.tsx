import { MenuItem, Select, FormControl } from "@mui/material";
import { Language, useAppText } from "../context/TextContentProvider";
import { useAppTheme } from "../theme/AppThemeProvider";

export const LanguageSelector = () => {
    const { currentLanguage, changeLanguage } = useAppText();

    const handleLanguageChange = (language: Language) => {
        changeLanguage(language);
    };

    return (
        <FormControl variant='outlined' sx={{ minWidth: 120 }}>
            <Select
                value={currentLanguage}
                onChange={(event) => handleLanguageChange(event.target.value as Language)}
                displayEmpty
                sx={{ backgroundColor: "white", height: "40px" }}
            >
                <MenuItem value='en'>English</MenuItem>
                <MenuItem value='he'>עברית</MenuItem>
            </Select>
        </FormControl>
    );
};
