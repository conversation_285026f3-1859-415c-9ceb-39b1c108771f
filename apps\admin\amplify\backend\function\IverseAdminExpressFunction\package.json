{"name": "@eva/admin-express-function", "version": "1.0.0", "description": "Lambda function generated by Amplify", "main": "index.js", "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.699.0", "@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/client-sqs": "^3.726.1", "aws-serverless-express": "^3.3.5", "axios": "^1.7.7", "body-parser": "^1.17.1", "dotenv": "^16.4.5", "esbuild": "^0.14.14", "express": "^4.15.2", "@eva/shared-common": "workspace:*", "@eva/logger": "workspace:*", "@eva/drizzle": "workspace:*", "@eva/aws-utils": "workspace:*"}, "devDependencies": {"@types/aws-lambda": "^8.10.92", "@types/aws-serverless-express": "^3.3.10", "@types/body-parser": "^1.19.2", "@types/express": "^4.17.17", "@types/node": "^20.6.1", "@types/pg": "^8.11.10", "esbuild": "^0.24.0", "typescript": "^5.2.2"}, "scripts": {"build": "esbuild lib/index.ts --bundle --platform=node --target=node20 --external:pg-native --external:@aws-sdk/* --outfile=src/index.js --minify --sourcemap", "start": "node dist/index.js", "package": "zip -r dist.zip dist"}}