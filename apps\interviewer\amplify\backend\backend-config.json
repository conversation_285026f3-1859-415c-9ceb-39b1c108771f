{"api": {"InterviewerApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "Interviewer<PERSON><PERSON>Function"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "auth": {"interviewclient": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed"}}, "function": {"InterviewerApiFunction": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "hosting": {"amplifyhosting": {"providerPlugin": "awscloudformation", "service": "amplifyhosting", "type": "manual"}}, "parameters": {"AMPLIFY_function_InterviewerApiFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "Interviewer<PERSON><PERSON>Function"}]}, "AMPLIFY_function_InterviewerApiFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "Interviewer<PERSON><PERSON>Function"}]}, "AMPLIFY_hosting_amplifyhosting_appId": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}, "AMPLIFY_hosting_amplifyhosting_type": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}}, "storage": {"uploads": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3"}}}