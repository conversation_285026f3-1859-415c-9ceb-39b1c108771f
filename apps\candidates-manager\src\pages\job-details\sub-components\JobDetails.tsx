import { Body2, formatDate, H3, H4, PageLayout } from "@eva/shared-ui";
import { CalendarMonth, Language } from "@mui/icons-material";
import { Box, Grid, Skeleton, Stack, styled, Typography } from "@mui/material";
import { FC } from "react";
import { useGetJobByIdQuery } from "../../../app-state/apis";
import { JobStatusChip } from "../../../components/JobStatusChip";
interface JobDetailsProps {
    jobId: string;
}

const HeaderSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    borderBottom: `1px solid ${theme.palette.divider}`,
}));

const DetailsSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
}));

const MetadataItem = styled(Box)(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    marginBottom: theme.spacing(1.5),
    "& > svg": {
        marginRight: theme.spacing(1),
        color: theme.palette.primary.main,
    },
}));

export const JobDetails: FC<JobDetailsProps> = ({ jobId }) => {
    const { data: job, isLoading } = useGetJobByIdQuery(jobId, {
        skip: !jobId,
    });

    if (isLoading) {
        return (
            <PageLayout>
                <HeaderSection>
                    <Skeleton variant="text" height={60} width="50%" />
                </HeaderSection>
                <DetailsSection>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={8}>
                            <Skeleton variant="text" height={20} width="50%" sx={{ mb: 1 }} />
                            <Skeleton variant="rectangular" height={50} width="100%" sx={{ mb: 2 }} />
                        </Grid>
                    </Grid>
                </DetailsSection>
            </PageLayout>
        );
    }

    if (!job) {
        return <Typography color="error">Job not found</Typography>;
    }

    return (
        <PageLayout>
            <HeaderSection>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <H3>{job.title}</H3>
                    <JobStatusChip status={job.status} />
                </Stack>
            </HeaderSection>

            <DetailsSection>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Stack spacing={1.5}>
                            <H4>Details</H4>
                            <Stack spacing={1.5}>
                                <MetadataItem>
                                    <CalendarMonth fontSize="small" />
                                    <Body2>
                                        <strong>Created:</strong> {formatDate(job.createdAt)}
                                    </Body2>
                                </MetadataItem>

                                <MetadataItem>
                                    <Language fontSize="small" />
                                    <Body2>
                                        <strong>Language:</strong> {job.language.toUpperCase()}
                                    </Body2>
                                </MetadataItem>
                            </Stack>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Stack spacing={1.5}>
                            <H4>Description</H4>
                            <Body2>{job.description}</Body2>
                        </Stack>
                    </Grid>
                </Grid>
            </DetailsSection>
        </PageLayout>
    );
};
