import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import { and, eq, inArray, isNull, or } from "@eva/drizzle";
import { Logger } from "@eva/logger";
import {
    AnalyzerHandlerEvent,
    AppError,
    Gender,
    getCandidateDataFromPilat,
    getSecret,
    InterviewerJobData,
    InterviewerJobQuestion,
    InterviewerJobStep,
    InterviewProgressStatus,
    InterviewState,
    InterviewStatus,
    IverseAdminCloudfrontDistSecret,
    JobQuestionsData,
    Language,
    MICandidateDetailsData,
    PilatSecrets,
    QuestionOptions,
    SimulationStory,
    SubmitAnswerRequest,
    UploadCandidateImageRequest,
} from "@eva/shared-common";
import {
    candidates,
    customers,
    DB,
    interviewIdMappings,
    interviews,
    jobIdMapping,
    jobs,
    questions,
    questionVideos,
    simulations,
    welcomeVideos,
} from "@eva/drizzle";
import { PilatCandidateData } from "@eva/shared-common/types/PilateCandidateData";
import { CloudfrontAccessPair, generateCloudfrontSignedUrl } from "@eva/aws-utils";

type QuestionType = "simulation" | "general" | "warmup" | "open";
interface SimulationQuestion {
    type: "simulation";
    id: string;
    question?: undefined;
}

interface GeneralQuestion {
    type: "general";
    id: string;
    question?: undefined;
}

interface WarmupQuestion {
    type: "warmup";
    id?: undefined;
    question: string;
}

interface OpenQuestion {
    type: "open";
    id?: undefined;
    question: string;
}

type QuestionDetails = SimulationQuestion | GeneralQuestion | WarmupQuestion | OpenQuestion;

interface InterviewJobDataDBRes {
    questions: JobQuestionsData;
    language: string;
    gender: string | null;
    customerId: string;
    state: InterviewState | null;
    status: InterviewStatus;
    event: AnalyzerHandlerEvent | null;
    candidateName: string;
    runId: string;
    id: string;
}

interface InterviewQueryResult {
    id: string;
    questions: string[];
    status: InterviewStatus;
    language: string;
    customerId: string;
    customerName: string;
    logoLink: string | null;
    welcomeVideoLink: string | null;
    myInterviewJobId: string;
    themeColor: string | null;
    candidateEmail: string;
    jobTitle: string;
    interviewId: string;
    gender: Gender | null;
    candidateName: string;
    email: string;
}

interface JobQueryResult {
    questions: JobQuestionsData;
    language: string;
    customerId: string;
    jobTitle: string;
    customerName: string;
    logoLink: string | null;
    welcomeVideoLink: string | null;
    myInterviewJobId: string;
    themeColor: string | null;
}

interface QuestionQueryResult {
    id: string;
    options: QuestionOptions;
    videoLink: string | null;
}

export class InterviewService {
    db: DB;
    logger: Logger;
    pilatSecrets: PilatSecrets;
    region: string;
    analyzerQueueUrl: string;
    uploadsBucket: string;
    env: string;
    interviewDoneStatuses: InterviewStatus[] = ["completed", "pending", "transferring", "failed"];
    externalAccountApiKey: string;
    myInterviewCandidateDetailsUrl: string;
    termsUrl: string = "";
    cloudfrontSignedUrlSecret: CloudfrontAccessPair;
    constructor(db: DB, logger: Logger, pilatSecrets: PilatSecrets, cloudfrontSignedUrlSecret: CloudfrontAccessPair) {
        this.env = process.env.ENV || "staging";
        this.db = db;
        this.logger = logger;
        this.pilatSecrets = pilatSecrets;
        this.cloudfrontSignedUrlSecret = cloudfrontSignedUrlSecret;
        this.analyzerQueueUrl = process.env.ANALYZER_QUEUE_URL!;
        this.region = process.env.REGION!;
        this.uploadsBucket = process.env.UPLOADS_BUCKET!;
        this.externalAccountApiKey = process.env.EXTERNAL_ACCOUNT_API_KEY!;
        this.myInterviewCandidateDetailsUrl = process.env.MY_INTERVIEW_CANDIDATE_DETAILS_URL!;
    }

    async validateJobId(jobId: string): Promise<boolean> {
        const res = await this.db.select({ jobId: jobs.id }).from(jobs).where(eq(jobs.id, jobId));
        return res.length === 1;
    }

    async getInterviewIdMapping(pilatInterviewId: string): Promise<string | null> {
        const res = await this.db
            .select({ interviewId: interviewIdMappings.interviewId })
            .from(interviewIdMappings)
            .where(eq(interviewIdMappings.externalId, pilatInterviewId));

        return res.length > 0 ? res[0].interviewId : null;
    }

    async setupInterviewForPilatCandidate(
        jobId: string,
        pilatInterviewId: string
    ): Promise<{ candidateName: string; gender: Gender; newInterviewId: string }> {
        this.logger.info(
            `Setting up interview for pilat candidate: ${pilatInterviewId} |
             URL: ${this.pilatSecrets.PILAT_CANDIDATE_DATA_URL}`
        );

        const pilatCandidateData = await getCandidateDataFromPilat({
            pilatInterviewId: pilatInterviewId,
            maxRetries: 3,
            logger: this.logger,
            pilatCandidateDataUrl: this.pilatSecrets.PILAT_CANDIDATE_DATA_URL,
            pilateApiKey: this.pilatSecrets.PILAT_CANDIDATE_DATA_URL_API_KEY,
        });

        this.logger.info(`Fetched candidate data from Pilat: ${JSON.stringify(pilatCandidateData)}`);

        let newInterviewId: string = "";
        const candidateName = pilatCandidateData.firstName + " " + pilatCandidateData.lastName;

        const jobRes = await this.db
            .select({ jobId: jobs.id, questions: jobs.questions })
            .from(jobs)
            .where(eq(jobs.id, jobId));

        if (jobRes.length === 0) throw new AppError("JOB_NOT_FOUND", 404);
        const questions = jobRes[0].questions;

        await this.db.transaction(async (trx) => {
            const createdCandidate = await trx
                .insert(candidates)
                .values({
                    jobId,
                    name: candidateName,
                    email: pilatCandidateData.email,
                    gender: pilatCandidateData.gender,
                    socialId: pilatCandidateData.socialId,
                })
                .returning({ id: candidates.id });

            const newCandidateId = createdCandidate[0].id;

            const createdInterview = await trx
                .insert(interviews)
                .values({
                    candidateId: newCandidateId,
                    status: "invited",
                    runId: crypto.randomUUID(),
                    event: null,
                    questions: questions,
                })
                .returning({ id: interviews.id });

            newInterviewId = createdInterview[0].id;

            await trx.insert(interviewIdMappings).values({
                interviewId: newInterviewId,
                externalId: pilatInterviewId,
            });
        });

        return { candidateName, gender: pilatCandidateData.gender as Gender, newInterviewId };
    }

    extractQuestionTextFromOptions = (
        options: QuestionOptions,
        customerId: string,
        language: Language,
        gender: Gender | null
    ): string => {
        const questionVariations = options.options[customerId] || options.options.default;
        if (language === "en") return questionVariations[language].male.text;
        if (gender === null) return questionVariations[language].male.text;
        if (questionVariations[language][gender] === undefined) return questionVariations[language].male.text;
        else return questionVariations[language][gender].text;
    };

    async getQuestionsByIds(
        questionIds: string[],
        gender: Gender | null,
        language: Language,
        customerId: string
    ): Promise<InterviewerJobQuestion[]> {
        const genderWhereClause = gender ? eq(questionVideos.gender, gender) : isNull(questionVideos.gender);

        const res = await this.db
            .select({
                questionId: questions.id,
                questionVariations: questions.options,
                videoLink: questionVideos.videoLink,
            })
            .from(questions)
            .leftJoin(
                questionVideos,
                and(
                    eq(questions.id, questionVideos.questionId),
                    genderWhereClause,
                    eq(questionVideos.language, language)
                )
            )
            .where(inArray(questions.id, questionIds));

        return res
            .sort((a, b) => a.questionVariations.order - b.questionVariations.order)
            .map((q) => {
                const questionVariations = q.questionVariations as QuestionOptions;
                return {
                    questionId: q.questionId,
                    text: this.extractQuestionTextFromOptions(questionVariations, customerId, language, gender),
                    videoLink: q.videoLink,
                } as InterviewerJobQuestion;
            });
    }

    async prepareJobInterviewData(
        jobId: string,
        interviewId: string,
        gender: Gender | null,
        candidateName: string
    ): Promise<InterviewerJobData> {
        const [isImageExistsRes, res] = await Promise.all([
            this.db
                .select({ imageLink: candidates.imageLink, state: interviews.state })
                .from(interviews)
                .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
                .where(eq(interviews.id, interviewId)),
            this.db
                .select({
                    language: jobs.language,
                    customerId: jobs.customerId,
                    jobTitle: jobs.jobTitle,
                    customerName: customers.companyName,
                    logoLink: customers.logoLink,
                    welcomeVideoLink: welcomeVideos.videoLink,
                    themeColor: customers.themeColor,
                })
                .from(jobs)
                .innerJoin(customers, eq(jobs.customerId, customers.id))
                .leftJoin(welcomeVideos, eq(customers.id, welcomeVideos.customerId))
                .where(eq(jobs.id, jobId)),
        ]);

        if (res.length === 0) {
            throw new Error("Job not found");
        }
        const language = res[0].language as Language;
        const customerId = res[0].customerId;
        const jobTitle = res[0].jobTitle;
        const customerName = res[0].customerName;
        const logoLink = res[0].logoLink;
        const welcomeVideoLink = res[0].welcomeVideoLink;

        return {
            themeColor: res[0].themeColor,
            gender,
            candidateName,
            customerId,
            jobTitle,
            customerName,
            logoLink,
            welcomeVideoLink,
            language,
            interviewId,
            isImageExists: isImageExistsRes[0].imageLink !== null,
        };
    }

    async getCandidateDataByInterviewId(
        interviewId: string
    ): Promise<{ candidateName: string; gender: Gender | null }> {
        const res = await this.db
            .select({ candidateName: candidates.name, gender: candidates.gender })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .where(eq(interviews.id, interviewId));

        return {
            candidateName: res[0].candidateName,
            gender: res[0].gender ? (res[0].gender as Gender) : null,
        };
    }

    async checkInterviewStatus(interviewId: string): Promise<boolean> {
        const res = await this.db
            .select({ status: interviews.status })
            .from(interviews)
            .where(eq(interviews.id, interviewId));
        return res[0].status !== "interviewing" && res[0].status !== "invited";
    }

    async prepareExistingInterview(jobId: string, interviewId: string): Promise<InterviewerJobData> {
        const { candidateName, gender } = await this.getCandidateDataByInterviewId(interviewId);

        return await this.prepareJobInterviewData(jobId, interviewId, gender, candidateName);
    }

    async prepareNewInterview(jobId: string, pilatInterviewId: string): Promise<string> {
        const { candidateName, gender, newInterviewId } = await this.setupInterviewForPilatCandidate(
            jobId,
            pilatInterviewId
        );

        const interviewData = await this.prepareJobInterviewData(jobId, newInterviewId, gender, candidateName);
        return interviewData.interviewId;
    }

    async checkIfInterviewRelatedToJob(interviewId: string, jobId: string): Promise<boolean> {
        this.logger.info(`Checking if interview: ${interviewId} is related to job: ${jobId}`);
        const res = await this.db
            .select({ jobId: jobs.id })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .where(and(eq(interviews.id, interviewId)));

        this.logger.info(`Related job: ${res.length > 0 && res[0].jobId === jobId}`);

        return res.length > 0 && res[0].jobId === jobId;
    }

    async setupPilatInterview(jobId: string, pilatInterviewId: string): Promise<{ interviewId: string }> {
        try {
            this.logger.info(`Setting up interview for job: ${jobId} and interview: ${pilatInterviewId}`);

            const [isJobFound, interviewId] = await Promise.all([
                this.validateJobId(jobId),
                this.getInterviewIdMapping(pilatInterviewId),
            ]);

            this.logger.info(`Job found: ${isJobFound} | Interview ID: ${interviewId}`);

            if (!isJobFound) throw new AppError("JOB_NOT_FOUND", 404);

            if (interviewId) {
                const isRelated = await this.checkIfInterviewRelatedToJob(interviewId, jobId);

                if (!isRelated) throw new AppError("INTERVIEW_NOT_RELATED_TO_JOB", 404);

                const isInterviewDone = await this.checkInterviewStatus(interviewId);

                if (isInterviewDone) throw new AppError("INTERVIEW_DONE", 400);

                const interviewData = await this.prepareExistingInterview(jobId, interviewId);
                return { interviewId: interviewData.interviewId };
            }

            const newInterviewId = await this.prepareNewInterview(jobId, pilatInterviewId);
            return { interviewId: newInterviewId };
        } catch (error) {
            this.logger.error("Error setting up interview", error);
            throw error;
        }
    }

    async getQuestionsTextsByIds(questionIds: string[]): Promise<{ questionId: string; text: string }[]> {
        const res = await this.db
            .select({ questionId: questions.id, questionVariations: questions.options })
            .from(questions)
            .where(inArray(questions.id, questionIds));

        return res.map((q) => {
            const questionVariations = q.questionVariations as QuestionOptions;
            return {
                questionId: q.questionId,
                text: this.extractQuestionTextFromOptions(questionVariations, "default", "en", "male"),
            };
        });
    }

    async updateInterview(interviewId: string, analyzerEvent: AnalyzerHandlerEvent): Promise<void> {
        try {
            await this.db
                .update(interviews)
                .set({ status: "pending", event: analyzerEvent })
                .where(eq(interviews.id, interviewId));
        } catch (error) {
            this.logger.error("Error updating interview status to pending");
            throw error;
        }
    }

    async getAnalyzerEventAndRunId(
        interviewId: string
    ): Promise<{ event: AnalyzerHandlerEvent | null; runId: string }> {
        try {
            const res = await this.db
                .select({ event: interviews.event, runId: interviews.runId })
                .from(interviews)
                .where(eq(interviews.id, interviewId));
            return res[0].event
                ? { event: res[0].event as AnalyzerHandlerEvent, runId: res[0].runId }
                : { event: null, runId: res[0].runId };
        } catch (error) {
            this.logger.error("Error getting analyzer event");
            throw error;
        }
    }

    async sendToAnalyzerQueue(analyzerHandlerEvent: AnalyzerHandlerEvent): Promise<void> {
        try {
            this.logger.info(`Sending to analyzer queue: ${JSON.stringify(analyzerHandlerEvent)}`);
            const client = new SQSClient({ region: this.region });
            const command = new SendMessageCommand({
                QueueUrl: this.analyzerQueueUrl,
                MessageBody: JSON.stringify(analyzerHandlerEvent),
                MessageDeduplicationId: analyzerHandlerEvent.interview_id + Date.now().toString(),
                MessageGroupId: Date.now().toString(),
            });

            await client.send(command);
            this.logger.info(`Successfully sent to analyzer queue: ${JSON.stringify(analyzerHandlerEvent)}`);
        } catch (error) {
            this.logger.error(`Failed to send to analyzer queue | Error: ${error}`);
            throw error;
        }
    }

    async updateCandidateImage(candidateId: string, imageUrl: string): Promise<void> {
        try {
            await this.db.update(candidates).set({ imageLink: imageUrl }).where(eq(candidates.id, candidateId));
        } catch (error) {
            this.logger.error("Error updating candidate image link");
            throw error;
        }
    }

    async getCandidateIdByInterviewId(interviewId: string): Promise<string> {
        const res = await this.db
            .select({ candidateId: interviews.candidateId })
            .from(interviews)
            .where(eq(interviews.id, interviewId));
        return res[0].candidateId;
    }

    async saveCandidateImage(uploadCandidateImage: UploadCandidateImageRequest): Promise<void> {
        this.logger.info("Uploading image to S3");

        try {
            const [candidateId, cloudFrontDistSecret] = await Promise.all([
                this.getCandidateIdByInterviewId(uploadCandidateImage.interviewId),
                getSecret<IverseAdminCloudfrontDistSecret>(`${this.env}/iverseAdminCloudfronDist`),
            ]);

            const bucketsMap: Record<string, string> = {
                dev: "iverse-admin-uploads69e19-dev",
                staging: "iverse-admin-uploadse62dc-staging",
                prod: "iverse-admin-uploads22cd0-prod",
            };

            const s3Client = new S3Client({ region: this.region });

            if (!uploadCandidateImage.base64Image.startsWith("data:image/")) {
                throw new Error("Invalid Base64 image string");
            }

            const base64Header = uploadCandidateImage.base64Image.split(",")[0];
            const contentType = base64Header.match(/data:(.*);base64/)?.[1] || "image/jpeg";
            const buffer = Buffer.from(uploadCandidateImage.base64Image.split(",")[1], "base64");

            const key = `public/uploads/candidate-images/${candidateId}.jpeg`;

            const uploadParams = {
                Bucket: bucketsMap[this.env],
                Key: key,
                Body: buffer,
                ContentType: contentType,
            };

            const imageUrl = cloudFrontDistSecret.CLOUDFRONT_DISTRIBUTION + key;

            await s3Client.send(new PutObjectCommand(uploadParams));
            await this.updateCandidateImage(candidateId, imageUrl);
            this.logger.info(`Successfully uploaded image to ${key}`);
        } catch (error) {
            this.logger.error("Error uploading image to S3", error);
            throw error;
        }
    }

    private async fetchJobData(jobId: string, gender: Gender): Promise<JobQueryResult> {
        const jobRes = await this.db
            .select({
                questions: jobs.questions,
                language: jobs.language,
                customerId: jobs.customerId,
                jobTitle: jobs.jobTitle,
                customerName: customers.companyName,
                logoLink: customers.logoLink,
                welcomeVideoLink: welcomeVideos.videoLink,
                myInterviewJobId: jobIdMapping.myInterviewId,
                themeColor: customers.themeColor,
            })
            .from(jobs)
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .innerJoin(jobIdMapping, eq(jobs.id, jobIdMapping.iverseId))
            .leftJoin(
                welcomeVideos,
                and(
                    eq(customers.id, welcomeVideos.customerId),
                    eq(welcomeVideos.language, jobs.language),
                    eq(welcomeVideos.gender, gender)
                )
            )
            .where(eq(jobs.id, jobId));

        if (jobRes.length === 0) {
            throw new AppError("JOB_NOT_FOUND", 404);
        }

        return jobRes[0];
    }

    private async fetchJobQuestions(
        questionIds: string[],
        customerId: string,
        language: Language,
        gender: Gender | null
    ): Promise<QuestionQueryResult[]> {
        const genderWhereClause = gender ? eq(questionVideos.gender, gender) : isNull(questionVideos.gender);

        return await this.db
            .select({
                id: questions.id,
                options: questions.options,
                videoLink: questionVideos.videoLink,
            })
            .from(questions)
            .leftJoin(
                questionVideos,
                and(
                    eq(questions.id, questionVideos.questionId),
                    eq(questionVideos.language, language),
                    eq(questionVideos.customerId, customerId),
                    genderWhereClause
                )
            )
            .where(inArray(questions.id, questionIds));
    }

    async saveWidgetInterview(
        pilatCandidateData: PilatCandidateData,
        jobId: string,
        pilatInterviewId: string,
        questionIds: string[]
    ): Promise<string> {
        try {
            const candidate = await this.db
                .select({ id: candidates.id })
                .from(candidates)
                .where(
                    and(
                        eq(candidates.email, pilatCandidateData.email),
                        eq(candidates.jobId, jobId),
                        eq(candidates.socialId, pilatCandidateData.socialId)
                    )
                );

            let newInterviewId = "";
            if (!candidate.length) {
                await this.db.transaction(async (trx) => {
                    const newCandidate = await trx
                        .insert(candidates)
                        .values({
                            email: pilatCandidateData.email,
                            jobId,
                            name: `${pilatCandidateData.firstName} ${pilatCandidateData.lastName}`,
                            socialId: pilatCandidateData.socialId,
                            gender: pilatCandidateData.gender,
                        })
                        .returning({ id: candidates.id });

                    const newInterview = await trx
                        .insert(interviews)
                        .values({
                            runId: crypto.randomUUID(),
                            status: "interviewing",
                            candidateId: newCandidate[0].id,
                            questions: { questionIds },
                        })
                        .returning({ id: interviews.id });

                    newInterviewId = newInterview[0].id;
                    await trx
                        .insert(interviewIdMappings)
                        .values({ externalId: pilatInterviewId, interviewId: newInterviewId });
                });
                return newInterviewId;
            }

            const candidateId = candidate[0].id;

            await this.db.transaction(async (trx) => {
                const newInterview = await trx
                    .insert(interviews)
                    .values({
                        runId: crypto.randomUUID(),
                        status: "interviewing",
                        candidateId,
                        questions: { questionIds: questionIds },
                    })
                    .returning({ id: interviews.id });

                await trx
                    .insert(interviewIdMappings)
                    .values({ externalId: pilatInterviewId, interviewId: newInterview[0].id });
                newInterviewId = newInterview[0].id;
            });

            return newInterviewId;
        } catch (error) {
            this.logger.error("Error saving widget interview");
            throw error;
        }
    }

    async checkIfInterviewIsDone(pilatInterviewId: string): Promise<boolean> {
        const interviewIdMapping = await this.db
            .select({ id: interviewIdMappings.id })
            .from(interviews)
            .innerJoin(interviewIdMappings, eq(interviews.id, interviewIdMappings.interviewId))
            .where(and(eq(interviewIdMappings.externalId, pilatInterviewId), eq(interviews.status, "completed")));

        return interviewIdMapping.length > 0;
    }

    async getInterviewByExternalInterviewId(pilatInterviewId: string): Promise<InterviewQueryResult | null> {
        const interview = await this.db
            .select({
                id: interviews.id,
                questions: interviews.questions,
                status: interviews.status,
                language: jobs.language,
                customerId: jobs.customerId,
                customerName: customers.companyName,
                logoLink: customers.logoLink,
                welcomeVideoLink: welcomeVideos.videoLink,
                myInterviewJobId: jobIdMapping.myInterviewId,
                themeColor: customers.themeColor,
                candidateEmail: candidates.email,
                jobTitle: jobs.jobTitle,
                interviewId: interviewIdMappings.interviewId,
                gender: candidates.gender,
                candidateName: candidates.name,
                email: candidates.email,
            })
            .from(interviews)
            .innerJoin(interviewIdMappings, eq(interviews.id, interviewIdMappings.interviewId))
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .innerJoin(jobIdMapping, eq(jobs.id, jobIdMapping.iverseId))
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .leftJoin(
                welcomeVideos,
                and(
                    eq(customers.id, welcomeVideos.customerId),
                    eq(jobs.language, welcomeVideos.language),
                    eq(candidates.gender, welcomeVideos.gender)
                )
            )
            .where(eq(interviewIdMappings.externalId, pilatInterviewId));

        if (!interview.length) return null;

        const result = {
            ...interview[0],
            questions: interview[0].questions?.questionIds || [],
            candidateEmail: interview[0].candidateEmail || "",
            welcomeVideoLink: interview[0].welcomeVideoLink || null,
            gender: interview[0].gender as Gender,
            email: interview[0].email || "",
        };

        return result;
    }

    private async createInterviewForExistingCandidate(
        candidateId: string,
        questionIds: string[],
        externalId: string
    ): Promise<string> {
        let interviewId = "";

        await this.db.transaction(async (trx) => {
            const createInterview = await trx
                .insert(interviews)
                .values({
                    candidateId: candidateId,
                    questions: { questionIds: questionIds },
                    status: "interviewing",
                    runId: crypto.randomUUID(),
                })
                .returning({ id: interviews.id });

            interviewId = createInterview[0].id;

            await trx.insert(interviewIdMappings).values({
                externalId: externalId,
                interviewId: createInterview[0].id,
            });
        });

        return interviewId;
    }

    private async createCandidateAndInterview(
        jobId: string,
        candidateDetails: MICandidateDetailsData,
        questionIds: string[]
    ): Promise<string> {
        let interviewId = "";

        await this.db.transaction(async (trx) => {
            const newCandidate = await trx
                .insert(candidates)
                .values({
                    jobId: jobId,
                    email: candidateDetails.email,
                    name:
                        candidateDetails.parsedName.firstname + " " + candidateDetails.parsedName.lastname
                            ? candidateDetails.parsedName.lastname
                            : "",
                })
                .returning({ id: candidates.id });

            const createdInterview = await trx
                .insert(interviews)
                .values({
                    candidateId: newCandidate[0].id,
                    questions: { questionIds: questionIds },
                    status: "interviewing",
                    runId: crypto.randomUUID(),
                })
                .returning({ id: interviews.id });

            interviewId = createdInterview[0].id;

            await trx.insert(interviewIdMappings).values({
                externalId: candidateDetails.candidate_id,
                interviewId: createdInterview[0].id,
            });
        });

        return interviewId;
    }

    async getInterviewStatus(interviewId: string): Promise<InterviewStatus> {
        const interview = await this.db
            .select({ status: interviews.status })
            .from(interviews)
            .where(eq(interviews.id, interviewId));
        return interview[0].status;
    }

    async updateCandidateGender(interviewId: string, gender: Gender): Promise<void> {
        try {
            const candidateId = await this.getCandidateIdByInterviewId(interviewId);
            await this.db.update(candidates).set({ gender }).where(eq(candidates.id, candidateId));
        } catch (error) {
            this.logger.error(`Error updating candidate gender for interview ${interviewId}: ${error}`);
            throw error;
        }
    }

    private extractStoryContent(
        story: SimulationStory,
        language: Language,
        gender: Gender | null
    ): { text: string; videoLink: string } {
        this.logger.info(`Extracting story content for language: ${language}, gender: ${gender}`);
        this.logger.info(`Story: ${JSON.stringify(story)}`);
        const genderToUse = language === "he" ? (gender === null ? "male" : gender) : "male";
        const content = story[language][genderToUse];

        if (!content?.text || !content?.videoLink) {
            this.logger.warn(`Missing content for language: ${language}, gender: ${genderToUse}`);
        }
        return {
            text: content?.text || "",
            videoLink: content?.videoLink || "",
        };
    }

    private async getInterviewJobData(interviewId: string): Promise<InterviewJobDataDBRes> {
        const jobDataRes = await this.db
            .select({
                questions: jobs.questions,
                language: jobs.language,
                gender: candidates.gender,
                customerId: customers.id,
                state: interviews.state,
                status: interviews.status,
                event: interviews.event,
                candidateName: candidates.name,
                runId: interviews.runId,
                id: interviews.id,
            })
            .from(interviews)
            .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
            .innerJoin(jobs, eq(candidates.jobId, jobs.id))
            .innerJoin(customers, eq(jobs.customerId, customers.id))
            .where(eq(interviews.id, interviewId))
            .limit(1);

        if (!jobDataRes.length) {
            this.logger.error(`INTERVIEW NOT FOUND FOR INTERVIEW ${interviewId}`);
            throw new AppError("INTERVIEW_NOT_FOUND", 404);
        }

        return jobDataRes[0];
    }

    private parseStateUrl(state: InterviewState | null): number[] {
        const stateUrl = state ? state.url : "/0";
        return stateUrl
            .split("/")
            .filter((s) => s !== "")
            .map((s) => parseInt(s));
    }

    private combineSimulationsAndQuestions(jobQuestions: JobQuestionsData): QuestionDetails[] {
        const warmupSteps: WarmupQuestion[] = jobQuestions.warmupQuestions.map((q) => ({
            type: "warmup",
            question: q,
        }));

        const openSteps: OpenQuestion[] = jobQuestions.openQuestions.map((q) => ({
            type: "open",
            question: q,
        }));
        const simulationSteps: SimulationQuestion[] = jobQuestions.simulations.map((s) => ({
            type: "simulation",
            id: s,
        }));
        const generalSteps: GeneralQuestion[] = jobQuestions.questionIds.map((q) => ({
            type: "general",
            id: q,
        }));

        return [...warmupSteps, ...simulationSteps, ...generalSteps, ...openSteps];
    }

    private async handleSimulationStep(
        stepId: string,
        questionIdx: number,
        language: Language,
        gender: Gender | null,
        customerId: string
    ): Promise<InterviewerJobStep | null> {
        this.logger.info(`Handling simulation step: ${stepId}, questionIdx: ${questionIdx}`);
        const simulation = await this.db
            .select({ data: simulations.data, id: simulations.id })
            .from(simulations)
            .where(eq(simulations.id, stepId));

        if (!simulation.length) throw new AppError("SIMULATION_NOT_FOUND", 404);

        const simulationData = simulation[0].data;
        const currentQuestionId = simulationData.questionIds[questionIdx || 0];

        const question = await this.db
            .select({
                id: questions.id,
                options: questions.options,
                videoLink: questionVideos.videoLink,
            })
            .from(questions)
            .leftJoin(
                questionVideos,
                and(
                    eq(questions.id, questionVideos.questionId),
                    eq(questionVideos.language, language),
                    gender
                        ? eq(questionVideos.gender, gender)
                        : or(isNull(questionVideos.gender), eq(questionVideos.gender, "male"))
                )
            )
            .where(eq(questions.id, currentQuestionId));

        if (!question.length) throw new AppError("QUESTION_NOT_FOUND", 404);

        const story = simulationData.story;
        const { text: storyText, videoLink: storyVideoLink } = this.extractStoryContent(story, language, gender);

        return {
            type: "simulation",
            simulation: {
                id: simulation[0].id,
                story: storyText,
                videoLink: storyVideoLink,
                question: {
                    id: currentQuestionId,
                    text: this.extractQuestionTextFromOptions(question[0].options, customerId, language, gender),
                    videoLink: question[0].videoLink || undefined,
                },
            },
        };
    }

    private async handleGeneralStep(
        stepId: string,
        language: Language,
        gender: Gender | null,
        customerId: string
    ): Promise<InterviewerJobStep | null> {
        this.logger.info(`Handling general step: ${stepId}`);
        this.logger.info(`Language: ${language}, Gender: ${gender}, Customer ID: ${customerId}`);
        const question = await this.db
            .select({
                id: questions.id,
                options: questions.options,
                videoLink: questionVideos.videoLink,
            })
            .from(questions)
            .leftJoin(
                questionVideos,
                and(
                    eq(questions.id, questionVideos.questionId),
                    eq(questionVideos.language, language),
                    eq(questionVideos.customerId, customerId),
                    gender
                        ? eq(questionVideos.gender, gender)
                        : or(isNull(questionVideos.gender), eq(questionVideos.gender, "male"))
                )
            )
            .where(eq(questions.id, stepId));

        if (!question.length) throw new AppError("QUESTION_NOT_FOUND", 404);

        return {
            type: "general",
            question: {
                id: stepId,
                text: this.extractQuestionTextFromOptions(question[0].options, customerId, language, gender),
                videoLink: question[0].videoLink || undefined,
            },
        };
    }

    async getCurrentStep(interviewId: string): Promise<InterviewerJobStep | null> {
        try {
            this.logger.info(`GETTING CURRENT STEP FOR INTERVIEW ${interviewId}`);

            const jobData = await this.getInterviewJobData(interviewId);

            if (jobData.status !== "interviewing" && jobData.status !== "invited") {
                this.logger.info(`INTERVIEW ${interviewId} IS NOT IN INTERVIEWING OR INVITED STATE`);
                return null;
            }

            this.logger.info(`JOB DATA RES: ${JSON.stringify(jobData)}`);

            const stateParts = this.parseStateUrl(jobData.state);
            const combinedSteps = this.combineSimulationsAndQuestions(jobData.questions);

            if (combinedSteps.length === stateParts[0]) return null;

            this.logger.info(`COMBINED SIMULATIONS AND QUESTIONS: ${JSON.stringify(combinedSteps)}`);
            this.logger.info(`STATE PARTS: ${JSON.stringify(stateParts)}`);

            const currentStep = combinedSteps[stateParts[0]];

            if (!currentStep) throw new AppError("INVALID_STATE", 400);

            if (currentStep.type === "simulation") {
                this.logger.info(`GETTING SIMULATION FOR INTERVIEW ${interviewId}`);
                this.logger.info(`Current step: ${JSON.stringify(currentStep)}`);
                return await this.handleSimulationStep(
                    currentStep.id,
                    stateParts[1],
                    jobData.language as Language,
                    jobData.gender as Gender | null,
                    jobData.customerId
                );
            } else if (currentStep.type === "general") {
                this.logger.info(`GETTING GENERAL QUESTION FOR INTERVIEW ${interviewId}`);
                return await this.handleGeneralStep(
                    currentStep.id,
                    jobData.language as Language,
                    jobData.gender as Gender | null,
                    jobData.customerId
                );
            } else if (currentStep.type === "warmup") {
                this.logger.info(`GETTING WARMUP QUESTION FOR INTERVIEW ${interviewId}`);
                return await this.handleWarmupStep(currentStep.question);
            } else if (currentStep.type === "open") {
                this.logger.info(`GETTING OPEN QUESTION FOR INTERVIEW ${interviewId}`);
                return await this.handleOpenStep(currentStep.question);
            }

            this.logger.info(`THERE IS NOTHING TO RETURN FOR INTERVIEW ${interviewId}`);
            return null;
        } catch (error) {
            this.logger.error(`Error getting current step: ${error}`);
            throw error;
        }
    }

    private async handleWarmupStep(question: string): Promise<InterviewerJobStep | null> {
        return {
            type: "warmup",
            question: {
                id: crypto.randomUUID(),
                text: question,
                videoLink: undefined,
            },
        };
    }

    private async handleOpenStep(question: string): Promise<InterviewerJobStep | null> {
        return {
            type: "open",
            question: {
                id: crypto.randomUUID(),
                text: question,
                videoLink: undefined,
            },
        };
    }

    async submitAnswer(request: SubmitAnswerRequest): Promise<void> {
        try {
            this.logger.info(`Submitting answer for interview ${request.interviewId}, question ${request.questionId}`);

            const jobData = await this.getInterviewJobData(request.interviewId);

            const stateParts = this.parseStateUrl(jobData.state);
            const combinedSteps = this.combineSimulationsAndQuestions(jobData.questions);

            const currentStepIdx = stateParts[0];
            const currentStep = combinedSteps[currentStepIdx];

            if (!currentStep) throw new AppError("INVALID_STATE", 400);

            let questionId: string | null;
            let questionText: string;

            if (currentStep.type === "simulation") {
                questionId = await this.getSimulationQuestionId(currentStep.id, stateParts[1] || 0);
                if (!questionId) {
                    this.logger.error(`Could not find simulation question ID for step: ${JSON.stringify(currentStep)}`);
                    throw new AppError("INTERNAL_ERROR", 500);
                }
                questionText = await this.getQuestionText(
                    questionId,
                    jobData.customerId,
                    jobData.language as Language,
                    jobData.gender as Gender
                );
            } else if (currentStep.type === "general") {
                questionId = currentStep.id;
                questionText = await this.getQuestionText(
                    questionId,
                    jobData.customerId,
                    jobData.language as Language,
                    jobData.gender as Gender
                );
            } else if (currentStep.type === "warmup" || currentStep.type === "open") {
                questionText = currentStep.question;
                questionId = null;
            } else {
                const exhaustiveCheck: never = currentStep;
                this.logger.error(`Unexpected step type: ${JSON.stringify(exhaustiveCheck)}`);
                throw new AppError("INTERNAL_ERROR", 500);
            }

            if (questionText === undefined) {
                this.logger.error(`Could not determine question text for step: ${JSON.stringify(currentStep)}`);
                throw new AppError("INTERNAL_ERROR", 500);
            }

            const event = this.createOrUpdateAnalyzerEvent(jobData, questionId, questionText, request.key);

            this.logger.info(`Combined steps: ${JSON.stringify(combinedSteps)}`);
            this.logger.info(`Current step idx: ${currentStepIdx}`);
            this.logger.info(`Current step: ${JSON.stringify(currentStep)}`);

            let nextStateUrl: string;

            if (currentStep.type === "simulation") {
                const simulationQuestionCount = await this.getSimulationQuestionCount(currentStep.id);
                const currentSimulationQuestionIdx = stateParts[1] || 0;

                if (currentSimulationQuestionIdx + 1 >= simulationQuestionCount) {
                    nextStateUrl = "/" + (currentStepIdx + 1);
                    this.logger.info(`Last question in simulation, moving to next step: ${nextStateUrl}`);
                } else {
                    nextStateUrl = this.calculateNextStateUrl(
                        currentStep.type,
                        currentStepIdx,
                        currentSimulationQuestionIdx
                    );
                }
            } else {
                nextStateUrl = this.calculateNextStateUrl(currentStep.type, currentStepIdx, stateParts[1] || 0);
            }

            if (currentStepIdx + 1 === combinedSteps.length) {
                this.logger.info(`Last question in interview, moving to pending state`);
                await this.db
                    .update(interviews)
                    .set({ status: "pending", event })
                    .where(eq(interviews.id, request.interviewId));
                await this.sendToAnalyzerQueue(event);
            } else {
                this.logger.info(`Moving to next step: ${nextStateUrl}`);
                await this.db
                    .update(interviews)
                    .set({ event, state: { url: nextStateUrl } })
                    .where(eq(interviews.id, request.interviewId));
            }
        } catch (error) {
            this.logger.error(`Error submitting answer: ${error}`);
            throw error;
        }
    }

    private async getSimulationQuestionCount(simulationId: string): Promise<number> {
        const simulation = await this.db
            .select({ data: simulations.data })
            .from(simulations)
            .where(eq(simulations.id, simulationId));

        if (!simulation.length) throw new AppError("SIMULATION_NOT_FOUND", 404);

        const simulationData = simulation[0].data;
        return simulationData.questionIds.length;
    }

    private async getSimulationQuestionId(simulationId: string, questionIdx: number): Promise<string> {
        const simulation = await this.db
            .select({ data: simulations.data })
            .from(simulations)
            .where(eq(simulations.id, simulationId));

        if (!simulation.length) throw new AppError("SIMULATION_NOT_FOUND", 404);

        const simulationData = simulation[0].data;
        return simulationData.questionIds[questionIdx];
    }

    private async getQuestionText(
        questionId: string,
        customerId: string,
        language: Language,
        gender: Gender
    ): Promise<string> {
        this.logger.info(`GETTING QUESTION TEXT FOR QUESTION ${questionId}`);
        const question = await this.db
            .select({ options: questions.options })
            .from(questions)
            .where(eq(questions.id, questionId));

        this.logger.info(`QUESTION RES: ${JSON.stringify(question)}`);

        if (!question.length) throw new AppError("QUESTION_NOT_FOUND", 404);

        return this.extractQuestionTextFromOptions(question[0].options, customerId, language, gender);
    }

    private createOrUpdateAnalyzerEvent(
        jobData: InterviewJobDataDBRes,
        questionId: string | null,
        questionText: string,
        videoKey: string
    ): AnalyzerHandlerEvent {
        if (jobData.event) {
            const existingEvent = jobData.event as AnalyzerHandlerEvent;
            return {
                ...existingEvent,
                answer_and_question_pairs: [
                    ...existingEvent.answer_and_question_pairs,
                    {
                        question_id: questionId,
                        text: questionText,
                        video_link: videoKey,
                    },
                ],
            };
        } else {
            return {
                candidate_name: jobData.candidateName,
                language: jobData.language,
                run_id: jobData.runId,
                gender: jobData.gender,
                interview_id: jobData.id,
                customer_id: jobData.customerId,
                computedTraits: [],
                answer_and_question_pairs: [
                    {
                        question_id: questionId,
                        text: questionText,
                        video_link: videoKey,
                    },
                ],
            };
        }
    }

    private calculateNextStateUrl(
        stepType: QuestionType,
        currentStepIdx: number,
        simulationQuestionIdx: number
    ): string {
        if (stepType === "simulation") {
            return "/" + currentStepIdx + "/" + (simulationQuestionIdx + 1);
        } else {
            return "/" + (currentStepIdx + 1);
        }
    }

    async setupInterview(interviewId: string): Promise<InterviewerJobData> {
        try {
            const interviewRes = await this.db
                .select({
                    candidateName: candidates.name,
                    customerId: customers.id,
                    customerName: customers.companyName,
                    jobTitle: jobs.jobTitle,
                    logoLink: customers.logoLink,
                    welcomeVideoLink: welcomeVideos.videoLink,
                    language: jobs.language,
                    gender: candidates.gender,
                    themeColor: customers.themeColor,
                    status: interviews.status,
                })
                .from(interviews)
                .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .innerJoin(customers, eq(jobs.customerId, customers.id))
                .leftJoin(
                    welcomeVideos,
                    and(
                        eq(customers.id, welcomeVideos.customerId),
                        eq(jobs.language, welcomeVideos.language),
                        or(eq(candidates.gender, welcomeVideos.gender), isNull(welcomeVideos.gender))
                    )
                )
                .where(eq(interviews.id, interviewId))
                .limit(1);

            if (!interviewRes.length) {
                this.logger.error(`Interview ${interviewId} not found`);

                throw new AppError("INTERVIEW_NOT_FOUND", 404);
            }

            return {
                candidateName: interviewRes[0].candidateName,
                customerId: interviewRes[0].customerId,
                customerName: interviewRes[0].customerName,
                jobTitle: interviewRes[0].jobTitle,
                logoLink: interviewRes[0].logoLink,
                welcomeVideoLink: interviewRes[0].welcomeVideoLink,
                language: interviewRes[0].language,
                interviewId: interviewId,
                gender: interviewRes[0].gender,
                themeColor: interviewRes[0].themeColor,
                isImageExists: false,
            };
        } catch (error) {
            this.logger.error(`Error setting up interview: ${error}`);
            throw error;
        }
    }

    async startInterview(interviewId: string): Promise<void> {
        try {
            await this.db.update(interviews).set({ status: "interviewing" }).where(eq(interviews.id, interviewId));
        } catch (error) {
            this.logger.error(`Error starting interview: ${error}`);
            throw error;
        }
    }

    async getInterviewProgressStatus(interviewId: string): Promise<InterviewProgressStatus> {
        try {
            this.logger.info(`Getting interview progress status for interview ${interviewId}`);
            const jobRes = await this.db
                .select({
                    jobData: jobs.questions,
                    interviewState: interviews.state,
                })
                .from(interviews)
                .innerJoin(candidates, eq(interviews.candidateId, candidates.id))
                .innerJoin(jobs, eq(candidates.jobId, jobs.id))
                .where(eq(interviews.id, interviewId));

            if (!jobRes.length) throw new AppError("INTERVIEW_NOT_FOUND", 404);

            const jobData = jobRes[0].jobData;

            const totalSteps =
                jobData.questionIds.length +
                jobData.openQuestions.length +
                jobData.warmupQuestions.length +
                jobData.simulations.length;

            const currentStep = jobRes[0].interviewState
                ? jobRes[0].interviewState.url
                      .split("/")
                      .filter((s) => s !== "")
                      .map((s) => parseInt(s))[0]
                : 1;

            return {
                currentStep,
                totalSteps,
            };
        } catch (error) {
            this.logger.error(`Error getting interview progress status: ${error}`);
            throw error;
        }
    }

    async getTermsUrl(): Promise<string> {
        try {
            return generateCloudfrontSignedUrl(this.termsUrl, 24, this.cloudfrontSignedUrlSecret);
        } catch (error) {
            this.logger.error(`Error getting terms url: ${error}`);
            throw error;
        }
    }
}
