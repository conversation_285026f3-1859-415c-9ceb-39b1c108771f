import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import Arrow<PERSON><PERSON><PERSON>Icon from "@mui/icons-material/ArrowForward";
import { IconButton } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useAppTheme } from "../../theme/AppThemeProvider";

export const BackButton = () => {
    const navigate = useNavigate();
    const { currentDirection } = useAppTheme();

    const handleClick = () => {
        navigate(-1);
    };

    const isRtl = currentDirection === "rtl";

    return <IconButton size="small" onClick={handleClick}>{isRtl ? <ArrowForwardIcon /> : <ArrowBackIcon />}</IconButton>;
};
