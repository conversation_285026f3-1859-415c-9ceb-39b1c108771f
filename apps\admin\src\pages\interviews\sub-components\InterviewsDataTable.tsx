import { <PERSON> } from "react-router-dom";
import { GridColDef, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";
import { InterviewStatus, InterviewTableData, Language } from "@eva/shared-common";
import { ClickableBody1, CustomDataGridServer, formatDate } from "@eva/shared-ui";
import { languageMapper } from "../../../app-state/appText";
import { InterviewStatusIndicator } from "../../../components/EvaluatorStatusIndicator";
import { ReviewStatusIndicator } from "../../../components/ReviewStatusIndicator";
import { InterviewDataTableRerunButton } from "./InterviewDataTableRerunButton";
import { InterviewLogsButton } from "./InterviewLogsButton";

const columns: GridColDef<InterviewTableData>[] = [
    {
        field: "candidateName",
        headerName: "Name",
        type: "string",
        width: 120,
        disableColumnMenu: true,
        renderCell: (params) => {
            return params.row.reviewStatus === "not_available" ? params.value : <Link to={`/interviews/${params.id}`}>{params.value}</Link>;
        },
    },
    {
        field: "email",
        headerName: "Email",
        type: "string",
        width: 230,
        disableColumnMenu: true,
    },
    {
        field: "socialId",
        headerName: "Social ID",
        type: "string",
        width: 180,
        disableColumnMenu: true,
    },

    {
        field: "jobTitle",
        headerName: "Job Title",
        type: "string",
        disableColumnMenu: true,
        width: 150,
    },
    {
        field: "companyName",
        headerName: "Company",
        type: "string",
        disableColumnMenu: true,
        width: 150,
    },
    {
        field: "language",
        headerName: "Language",
        type: "string",
        disableColumnMenu: true,
        width: 150,
        renderCell: (params) => (params.row ? languageMapper(params.row.language as Language) : ""),
    },

    {
        field: "accountName",
        headerName: "MI Account",
        type: "string",
        disableColumnMenu: true,
        width: 150,
    },
    {
        field: "globalScore",
        headerName: "Score",
        type: "number",
        disableColumnMenu: true,
        width: 100,
        renderCell: (params) => {
            return params.value ? params.value.toFixed(2) : 0;
        },
    },
    {
        field: "interviewDate",
        headerName: "Interview Date",
        type: "string",
        disableColumnMenu: true,
        width: 180,
        renderCell: (params) => {
            return params.value ? formatDate(params.value) : "";
        },
    },
    {
        field: "reviewStatus",
        headerName: "Review Status",
        type: "string",
        width: 150,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) =>
            params.value ? <ReviewStatusIndicator isThereDiffs={params.row.isThereDiffsInReport} status={params.value} /> : <></>,
    },
    {
        field: "evaluationStatus",
        headerName: "Evaluation Status",
        type: "string",
        width: 150,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) => <InterviewStatusIndicator status={params.value as InterviewStatus} />,
    },
    {
        field: "id",
        headerName: "",
        type: "string",
        width: 120,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) =>
            params.row.id ? (
                <InterviewDataTableRerunButton
                    interviewId={params.row.id}
                    evaluationStatus={params.row.evaluationStatus as InterviewStatus}
                />
            ) : (
                <></>
            ),
    },
    {
        field: "run_id",
        headerName: "",
        type: "string",
        width: 120,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) => (
            <InterviewLogsButton
                disabled={params.row.runId === null || params.row.evaluationStatus === "interviewing"}
                runId={params.row.runId}
                interviewDate={params.row.interviewDate ? params.row.interviewDate : ""}
            />
        ),
    },
    {
        field: "bucket_url",
        headerName: "",
        type: "string",
        width: 120,
        disableColumnMenu: true,
        sortable: false,
        renderCell: (params) => <ClickableBody1 href={params.row.bucketUrl}>Files</ClickableBody1> ,
    },
];

interface InterviewsDataTableProps {
    interviews: InterviewTableData[];
    isLoading: boolean;
    paginationModel: GridPaginationModel;
    onPaginationModelChange: (model: GridPaginationModel) => void;
    sortModel: GridSortModel;
    onSortModelChange: (model: GridSortModel) => void;
    rowCount: number;
}

export const InterviewsDataTable = ({ 
    interviews, 
    isLoading, 
    paginationModel, 
    onPaginationModelChange, 
    sortModel, 
    onSortModelChange,
    rowCount
}: InterviewsDataTableProps) => {
    return (
        <CustomDataGridServer
            tableName='interviews-table' 
            rows={interviews} 
            columns={columns} 
            rowCount={rowCount}
            loading={isLoading}
            paginationModel={paginationModel}
            onPaginationModelChange={onPaginationModelChange}
            sortModel={sortModel}
            onSortModelChange={onSortModelChange}
            pageSizeOptions={[5, 10, 25, 50]}
            
        />
    );
};
