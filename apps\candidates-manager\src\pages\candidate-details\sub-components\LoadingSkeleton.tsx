import { FC } from "react";
import { Box, Skeleton, Grid, styled } from "@mui/material";
import { PageLayout } from "@eva/shared-ui";

const HeaderSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    borderBottom: `1px solid ${theme.palette.divider}`,
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(2),
}));

const DetailsSection = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
}));

export const LoadingSkeleton: FC = () => {
    return (
        <PageLayout>
            <HeaderSection>
                <Skeleton variant="circular" width={80} height={80} />
                <Skeleton variant="text" height={60} width="50%" />
            </HeaderSection>
            <DetailsSection>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Skeleton variant="text" height={20} width="50%" sx={{ mb: 1 }} />
                        <Skeleton variant="rectangular" height={150} width="100%" sx={{ mb: 2 }} />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Skeleton variant="text" height={20} width="50%" sx={{ mb: 1 }} />
                        <Skeleton variant="rectangular" height={150} width="100%" sx={{ mb: 2 }} />
                    </Grid>
                </Grid>
            </DetailsSection>
        </PageLayout>
    );
}; 