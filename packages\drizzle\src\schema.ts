import {
    AnalyzerHandlerEvent,
    AnalyzerReportData,
    FamilyPromptMetadata,
    GradingPrinciplesMap,
    InterviewQuestionAnswer,
    InterviewState,
    InterviewStatus,
    JobInvitationData,
    JobQuestionsData,
    JobStatus,
    LanguageVariation,
    QuestionOptions,
    SimulationData,
    TraitComputedBy,
    TraitPromptMetadata,
    TraitSummariesMap,
} from "@eva/shared-common";
import { relations } from "drizzle-orm";
import { integer, json, pgTable, text, timestamp, unique, uuid, varchar } from "drizzle-orm/pg-core";

export const questions = pgTable("questions", {
    id: varchar("id", { length: 10 }).primaryKey(),
    traitId: varchar("trait_id", { length: 10 })
        .notNull()
        .references(() => traits.id),
    options: json("options").$type<QuestionOptions>().notNull(),
    gradingPrinciples: json("grading_principles").$type<GradingPrinciplesMap>().notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const customers = pgTable("customers", {
    id: uuid("id").primaryKey().defaultRandom(),
    companyName: varchar("company_name", { length: 255 }).notNull(),
    logoLink: varchar("logo_link", { length: 255 }),
    themeColor: varchar("theme_color", { length: 255 }),
    integration: varchar("integration", { length: 255 }),
    accountId: varchar("account_id", { length: 255 })
        .notNull()
        .references(() => accounts.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const welcomeVideos = pgTable("welcome_videos", {
    id: uuid("id").primaryKey().defaultRandom(),
    customerId: uuid("customer_id").references(() => customers.id),
    videoLink: varchar("video_link", { length: 255 }).notNull(),
    gender: varchar("gender", { length: 255 }),
    language: varchar("language", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow(),
});

export const jobs = pgTable(
    "jobs",
    {
        id: uuid("id").primaryKey().defaultRandom(),
        customerId: uuid("customer_id")
            .notNull()
            .references(() => customers.id),
        jobTitle: varchar("job_title", { length: 255 }).notNull(),
        language: varchar("language", { length: 255 }).notNull(),
        description: text("description").notNull(),
        status: varchar("status", { length: 255 }).$type<JobStatus>().notNull(),
        questions: json("questions").$type<JobQuestionsData>().notNull(),
        invitation: json("invitation").$type<JobInvitationData>(),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
    },
    (table) => ({
        uniqueConstraint: unique("uq_customer_job_title").on(table.customerId, table.jobTitle),
    })
);

export const candidates = pgTable("candidates", {
    id: uuid("id").primaryKey().defaultRandom(),
    socialId: varchar("social_id", { length: 255 }),
    email: varchar("email", { length: 255 }),
    name: varchar("name", { length: 255 }).notNull(),
    imageLink: varchar("image_link", { length: 255 }),
    gender: varchar("gender", { length: 255 }),
    jobId: uuid("job_id")
        .notNull()
        .references(() => jobs.id),
    phoneNumber: varchar("phone_number", { length: 255 }),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const interviews = pgTable("interviews", {
    id: uuid("id").primaryKey().defaultRandom(),
    candidateId: uuid("candidate_id")
        .notNull()
        .unique()
        .references(() => candidates.id),
    analyzerReport: json("analyzer_report").$type<AnalyzerReportData>(),
    state: json("state").$type<InterviewState>(),
    questions: json("questions").$type<{ questionIds: string[] }>(),
    status: varchar("status", { length: 255 }).$type<InterviewStatus>().notNull(),
    event: json("event").$type<AnalyzerHandlerEvent>(),
    runId: varchar("run_id", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const traitsFamilies = pgTable("traits_families", {
    id: varchar("id", { length: 10 }).primaryKey(),
    family: json("family").$type<LanguageVariation>().notNull(),
    promptMetadata: json("prompt_metadata").$type<FamilyPromptMetadata>().notNull(),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow(),
});

export const traits = pgTable("traits", {
    id: varchar("id", { length: 10 }).primaryKey(),
    familyId: varchar("family_id", { length: 10 })
        .notNull()
        .references(() => traitsFamilies.id),
    trait: json("trait").$type<LanguageVariation>().notNull(),
    computedBy: json("computed_by").$type<TraitComputedBy>(),
    traitSummariesMap: json("trait_summaries_map").$type<TraitSummariesMap>(),
    promptMetadata: json("prompt_metadata").$type<TraitPromptMetadata>().notNull(),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow(),
});

export const answers = pgTable("answers", {
    id: uuid("id").primaryKey().defaultRandom(),
    answer: json("answer").$type<InterviewQuestionAnswer>().notNull(),
    interviewId: uuid("interview_id")
        .notNull()
        .references(() => interviews.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const promptTemplates = pgTable("prompt_templates", {
    id: uuid("id").primaryKey().defaultRandom(),
    type: varchar("type", { length: 255 }).notNull(),
    template: text("template").notNull(),
    language: varchar("language", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const translationCorrections = pgTable("translation_corrections", {
    id: uuid("id").primaryKey().defaultRandom(),
    language: varchar("language", { length: 255 }).notNull(),
    text: text("text").notNull(),
    correction: text("correction").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const interviewSummarySentences = pgTable("interview_summary_sentences", {
    id: uuid("id").primaryKey().defaultRandom(),
    sentence: text("sentence").notNull(),
    language: varchar("language", { length: 255 }).notNull(),
    score: integer("score").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const jobIdMapping = pgTable("job_id_mapping", {
    id: uuid("id").primaryKey().defaultRandom(),
    myInterviewId: varchar("my_interview_id", { length: 255 }).notNull(),
    iverseId: uuid("iverse_id")
        .notNull()
        .references(() => jobs.id),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const candidateIdMapping = pgTable("candidate_id_mapping", {
    id: uuid("id").primaryKey().defaultRandom(),
    myInterviewId: uuid("my_interview_id").notNull(),
    iverseId: uuid("iverse_id").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const accounts = pgTable("accounts", {
    id: varchar("id", { length: 255 }).primaryKey(),
    name: varchar("name", { length: 255 }).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const questionVideos = pgTable(
    "question_videos",
    {
        id: uuid("id").primaryKey().defaultRandom(),
        questionId: varchar("question_id", { length: 255 })
            .notNull()
            .references(() => questions.id, { onDelete: "cascade" }),
        customerId: uuid("customer_id").references(() => customers.id, { onDelete: "set null" }),
        gender: varchar("gender", { length: 255 }),
        videoLink: varchar("video_link", { length: 255 }).notNull(),
        language: varchar("language", { length: 255 }).notNull(),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
    },
    (table) => ({
        uniqueConstraint: unique("uq_customer_question_gender_language").on(
            table.customerId,
            table.questionId,
            table.gender,
            table.language
        ),
    })
);

export const pollingStates = pgTable("polling_states", {
    id: uuid("id").primaryKey().defaultRandom(),
    consumer: varchar("consumer", { length: 255 }).notNull(),
    lastPolledAt: timestamp("last_polled_at").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const interviewIdMappings = pgTable(
    "interview_id_mapping",
    {
        id: uuid("id").primaryKey().defaultRandom(),
        externalId: varchar("external_id", { length: 255 }).notNull(),
        interviewId: uuid("interview_id")
            .notNull()
            .references(() => interviews.id, { onDelete: "cascade" }),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
    },
    (table) => ({
        uniqueConstraint: unique("interview_id_mapping_unique").on(table.interviewId, table.externalId),
    })
);

export const customerIdMappings = pgTable(
    "customer_id_mapping",
    {
        id: uuid("id").primaryKey().defaultRandom(),
        externalId: varchar("external_id", { length: 255 }).notNull(),
        internalId: uuid("internal_id")
            .notNull()
            .references(() => customers.id, { onDelete: "cascade" }),
        createdAt: timestamp("created_at").defaultNow().notNull(),
        updatedAt: timestamp("updated_at").defaultNow().notNull(),
    },
    (table) => ({
        uniqueConstraint: unique("customer_id_mapping_unique").on(table.externalId, table.internalId),
    })
);

export const simulations = pgTable("simulations", {
    id: uuid("id").primaryKey().defaultRandom(),
    data: json("data").$type<SimulationData>().notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const interviewRelations = relations(interviews, ({ one, many }) => ({
    answers: many(answers),
    candidateIdMapping: one(candidateIdMapping, {
        fields: [interviews.id],
        references: [candidateIdMapping.iverseId],
    }),
    interviewIdMapping: one(interviewIdMappings, {
        fields: [interviews.id],
        references: [interviewIdMappings.interviewId],
    }),
    candidate: one(candidates, {
        fields: [interviews.candidateId],
        references: [candidates.id],
    }),
}));

export const questionRelations = relations(questions, ({ one, many }) => ({
    trait: one(traits, {
        fields: [questions.traitId],
        references: [traits.id],
    }),
    questionVideos: many(questionVideos),
}));

export const customerRelations = relations(customers, ({ one, many }) => ({
    jobs: many(jobs),
    welcomeVideos: many(welcomeVideos),
    questionVideos: many(questionVideos),
    account: one(accounts, {
        fields: [customers.accountId],
        references: [accounts.id],
    }),
}));

export const traitsFamiliesRelations = relations(traitsFamilies, ({ one, many }) => ({
    traits: many(traits),
}));

export const traitsRelations = relations(traits, ({ one }) => ({
    family: one(traitsFamilies, {
        fields: [traits.familyId],
        references: [traitsFamilies.id],
    }),
}));
