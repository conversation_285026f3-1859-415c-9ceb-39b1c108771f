import { Stack } from "@mui/material";
import { Body2, H3, H5, splitAndCapitalize } from "@eva/shared-ui";

interface ReviewPageFamilyDetailsProps {
    score: number;
    summary: string;
    language: string;
    familyName: string;
}

export const ReviewPageFamilyDetails: React.FC<ReviewPageFamilyDetailsProps> = ({ familyName, score, summary, language }) => {
    return (
        <Stack padding={3} gap={3} width='45%'>
            <H3 color='secondary'>{splitAndCapitalize(familyName)}</H3>
            <H5>Score : {score ? score.toFixed(1) : 0}</H5>
            <Body2 textAlign={language === "he" ? "right" : "left"}>{summary}</Body2>
        </Stack>
    );
};
