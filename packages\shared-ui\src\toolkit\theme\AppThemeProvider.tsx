import createCache from "@emotion/cache";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as EmotionThemeProvider } from "@emotion/react";
import { Direction, ThemeOptions, Theme } from "@mui/material";
import { enUS, heIL } from "@mui/material/locale";
import React, { createContext, useContext, useLayoutEffect, useState } from "react";
import { prefixer } from "stylis";
import rtlPlugin from "stylis-plugin-rtl";
import { useLocalStorageState } from "../hooks/useLocalStorageState";
import createTheme from "@mui/material/styles/createTheme";
import "@fontsource/poppins";
import { typography } from "./Typography";

export const basicTheme: Theme = createTheme({
    spacing: 4,
    typography,
    breakpoints: {
        values: {
            xs: 0,
            sm: 600,
            md: 960,
            lg: 1280,
            xl: 1920,
        },
    },
    palette: {
        primary: {
            main: "#4C42FF",
            light: "#E9E8FF",
        },
        secondary: {
            main: "#A119E4",
        },
        text: {
            primary: "#000000",
            secondary: "#A119E4",
        },
        background: {
            default: "#F0F0F0",
            paper: "#F8F8F8",
        },
        error: {
            main: "#FF8A8A",
        },
        success: {
            main: "#2FFF82",
        },
        warning: {
            main: "#FFD166",
        },
    },
});

interface ThemeProviderProps {
    children: React.ReactNode;
}

interface ThemeContextValue {
    changeDirection: (newDirection: Direction) => void;
    changeMainColor: (newColor: string) => void;
    currentDirection: Direction;
    mainColor: string;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

export const AppThemeProvider = ({ children }: ThemeProviderProps) => {
    const [direction, setDirection] = useLocalStorageState<Direction>("app-direction", "ltr");
    const [mainColor, setMainColor] = useState<string>(basicTheme.palette.primary.main);

    const themeOptions: ThemeOptions = {
        ...basicTheme,
        palette: {
            ...basicTheme.palette,
            primary: {
                main: mainColor,
            },
        },
        components: {
            MuiCssBaseline: {
                styleOverrides: {
                    body: {
                        backgroundColor: "#F0F0F0",
                        color: "black",
                        boxSizing: "border-box",
                    },
                    "*::-webkit-scrollbar": {
                        width: "8px",
                        height: "8px",
                    },
                    "*::-webkit-scrollbar-track": {
                        backgroundColor: "#F0F0F0",
                    },
                    "*::-webkit-scrollbar-thumb": {
                        backgroundColor: mainColor,
                        borderRadius: "4px",
                    },
                    "*::-webkit-scrollbar-thumb:hover": {
                        backgroundColor: "#2FFF82",
                    },
                    "*": {
                        scrollbarColor: `${mainColor} #F0F0F0`,
                        scrollbarWidth: "thin",
                    },
                },
            },
        },
        direction: direction as Direction,
    };

    const theme = createTheme(themeOptions, direction === "rtl" ? heIL : enUS);

    const cache = createCache({
        key: direction === "rtl" ? "muirtl" : "mui",
        stylisPlugins: direction === "rtl" ? [prefixer, rtlPlugin] : [],
    });

    const changeDirection = (newDirection: Direction) => {
        setDirection(newDirection);
    };

    const changeMainColor = (newColor: string) => {
        setMainColor(newColor);
    };

    useLayoutEffect(() => {
        document.dir = direction;
    }, [direction]);

    return (
        <ThemeContext.Provider value={{ changeDirection, changeMainColor, currentDirection: direction, mainColor }}>
            <CacheProvider value={cache}>
                <EmotionThemeProvider theme={theme}>{children}</EmotionThemeProvider>
            </CacheProvider>
        </ThemeContext.Provider>
    );
};

export const useAppTheme = () => {
    const context = useContext(ThemeContext);
    if (context === undefined) {
        throw new Error("useAppTheme must be used within a ThemeProvider");
    }
    return context;
};

export const createAppTheme = (direction: Direction): Theme =>
    createTheme(
        {
            ...basicTheme,
        },
        direction === "rtl" ? heIL : enUS,
        {
            direction: direction as Direction,
        }
    );
