import { createApi } from "@reduxjs/toolkit/query/react";
import { 
    PromptsPageFamiliesResponse,
    PromptsPageTraitsResponse,
    UpdateFamilyPromptMetadataRequest,
    UpdateTraitPromptMetadataRequest
} from "@eva/shared-common";
import { baseQuery } from "./baseQuery";

export const promptsApi = createApi({
    reducerPath: "promptsApi",
    baseQuery,
    tagTypes: [],
    endpoints: (builder) => ({
        getPromptsPageFamiliesMetadata: builder.query<PromptsPageFamiliesResponse, void>({
            query: () => ({
                url: `/prompts/families`,
                method: "GET",
            }),
        }),
        getPromptsPageTraitsMetadata: builder.query<PromptsPageTraitsResponse, void>({
            query: () => ({
                url: `/prompts/traits`,
                method: "GET",
            }),
        }),
        updateFamilyPromptMetadata: builder.mutation<void, UpdateFamilyPromptMetadataRequest>({
            query: (body) => ({
                url: `/prompts/families`,
                method: "POST",
                body: body,
            }),
            onQueryStarted: async ({ id, metadata }, { dispatch, queryFulfilled }) => {
                const patchResult = dispatch(
                    promptsApi.util.updateQueryData("getPromptsPageFamiliesMetadata", undefined, (draft) => {
                        const index = draft.families.findIndex((family) => family.id === id);
                        if (index !== -1) {
                            draft.families[index].metadata = metadata;
                        }
                    })
                );
                try {
                    await queryFulfilled;
                } catch {
                    patchResult.undo();
                }
            },
        }),
        updateTraitPromptMetadata: builder.mutation<void, UpdateTraitPromptMetadataRequest>({
            query: (body) => ({
                url: `/prompts/traits`,
                method: "POST",
                body: body,
            }),
            onQueryStarted: async ({ id, metadata }, { dispatch, queryFulfilled }) => {
                const patchResult = dispatch(
                    promptsApi.util.updateQueryData("getPromptsPageTraitsMetadata", undefined, (draft) => {
                        const index = draft.traits.findIndex((trait) => trait.id === id);
                        if (index !== -1) {
                            draft.traits[index].metadata = metadata;
                        }
                    })
                );
                try {
                    await queryFulfilled;
                } catch {
                    patchResult.undo();
                }
            },
        }),
    }),
});

export const {
    useGetPromptsPageFamiliesMetadataQuery,
    useGetPromptsPageTraitsMetadataQuery,
    useUpdateFamilyPromptMetadataMutation,
    useUpdateTraitPromptMetadataMutation
} = promptsApi; 