import React, { createContext, useContext, ReactNode } from "react";
import { useMediaQuery, useTheme } from "@mui/material";

interface MediaQueryContextProps {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
}

const MediaQueryContext = createContext<MediaQueryContextProps | undefined>(undefined);

interface MediaQueryProviderProps {
    children: ReactNode;
}

export const MediaQueryProvider: React.FC<MediaQueryProviderProps> = ({ children }) => {
    const theme = useTheme();

    const isMobile = useMediaQuery(theme.breakpoints.down("sm")); // Screen size <= 600px
    const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md")); // 601px <= size <= 960px
    const isDesktop = useMediaQuery(theme.breakpoints.up("md")); // Screen size >= 961px

    return <MediaQueryContext.Provider value={{ isMobile, isTablet, isDesktop }}>{children}</MediaQueryContext.Provider>;
};

export const useMediaQueryContext = (): MediaQueryContextProps => {
    const context = useContext(MediaQueryContext);
    if (!context) {
        throw new Error("useMediaQueryContext must be used within a MediaQueryProvider");
    }
    return context;
};
