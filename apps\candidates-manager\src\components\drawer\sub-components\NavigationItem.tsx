import { ListItem, ListItemButton, ListItemIcon, ListItemText, styled } from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";

interface NavigationItemProps {
    to: string;
    icon: React.ReactNode;
    text: string;
}

const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
    '&.Mui-selected': {
        backgroundColor: `${theme.palette.primary.main}10`, 
        '&:hover': {
            backgroundColor: `${theme.palette.primary.main}15`, 
        }
    },
    '& .MuiListItemIcon-root': {
        color: theme.palette.text.primary,
    },
    '&.Mui-selected .MuiListItemIcon-root': {
        color: theme.palette.primary.main,
    },
}));

export const NavigationItem = ({ to, icon, text }: NavigationItemProps) => {
    const navigate = useNavigate();
    const location = useLocation();
    const isActive = location.pathname === to;
    
    return (
        <ListItem disablePadding>
            <StyledListItemButton 
                selected={isActive}
                onClick={() => navigate(to)}
            >
                <ListItemIcon>
                    {icon}
                </ListItemIcon>
                <ListItemText primary={text} />
            </StyledListItemButton>
        </ListItem>
    );
};

