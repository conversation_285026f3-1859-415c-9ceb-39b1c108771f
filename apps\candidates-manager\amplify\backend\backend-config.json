{"api": {"candidatesManagerApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "candidates<PERSON>anager<PERSON>unction"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "auth": {"candidatesmanager": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed"}}, "function": {"candidatesManagerFunction": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "hosting": {"amplifyhosting": {"providerPlugin": "awscloudformation", "service": "amplifyhosting", "type": "manual"}}, "parameters": {"AMPLIFY_function_candidatesManagerFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "candidates<PERSON>anager<PERSON>unction"}]}, "AMPLIFY_function_candidatesManagerFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "candidates<PERSON>anager<PERSON>unction"}]}, "AMPLIFY_hosting_amplifyhosting_appId": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}, "AMPLIFY_hosting_amplifyhosting_type": {"usedBy": [{"category": "hosting", "resourceName": "amplifyhosting"}]}}}