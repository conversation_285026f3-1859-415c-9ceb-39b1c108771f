import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";

interface ReportFooterPageProps {
    page: number;
    currentPage: number;
}

export const ReportFooterPage: FC<ReportFooterPageProps> = ({ currentPage, page }) => {
    return (
        <View
            style={[
                styles.pageBoxContainerWidth,
                styles.smallBorderRadius,
                styles.displayFlex,
                styles.alignItemsCenter,
                styles.justifyCenter,
                page === currentPage ? styles.backgroundColorBlack : styles.backgroundColorDefault,
            ]}
        >
            <Text style={[styles.familyOverallScoreText, page === currentPage ? styles.textColorWhite : styles.textColorBlack]}>
                {page}
            </Text>
        </View>
    );
};
