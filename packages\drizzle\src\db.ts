import { Client } from "pg";
import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";
import { drizzle } from "drizzle-orm/node-postgres";
import { DB } from "./types";

let client: Client | null = null;

const createDBClient = async (): Promise<Client> => {
    const secretName = `${process.env.ENV ? process.env.ENV : "staging"}/iverse-db/postgres`;
    const client = new SecretsManagerClient({
        region: "us-east-1",
    });

    let response;

    try {
        response = await client.send(
            new GetSecretValueCommand({
                SecretId: secretName,
                VersionStage: "AWSCURRENT",
            })
        );

        if (response.SecretString) {
            const secret = JSON.parse(response.SecretString);
            return new Client({
                host: secret.host,
                port: Number(secret.port),
                user: secret.username,
                password: secret.password,
                database: secret.dbInstanceIdentifier,
                ssl: {
                    rejectUnauthorized: false,
                },
            });
        } else {
            throw new Error("No secret string found");
        }
    } catch (error) {
        throw error;
    }
};

export const initDB = async (): Promise<DB> => {
    client = await createDBClient();
    await client.connect();
    const db = drizzle(client);
    return db;
};

export const closeDBConnection = async () => {
    if (client) {
        await client.end();
        client = null;
        console.log("DB connection closed");
    }
};
