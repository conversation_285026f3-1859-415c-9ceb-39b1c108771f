import { Stack } from "@mui/material";
import React, { useRef } from "react";
import { PrimaryButton } from "./buttons/PrimaryButton";

interface FileInputProps {
    accept?: string; 
    disabled?: boolean; 
    onFileSelect: (file: File | null) => void; 
    buttonLabel?: string; 
}

export const FileInput: React.FC<FileInputProps> = ({
    accept = "*", 
    disabled = false,
    onFileSelect,
    buttonLabel = "Choose File",
}) => {
    const inputRef = useRef<HTMLInputElement>(null);

    const handleButtonClick = () => {
        if (inputRef.current) {
            inputRef.current.click();
        }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files && event.target.files[0];
        onFileSelect(file || null);
    };

    return (
        <Stack direction='row' alignItems='center' gap={2}>
            <input type='file' accept={accept} style={{ display: "none" }} ref={inputRef} onChange={handleFileChange} />
            <PrimaryButton content={buttonLabel} onClick={handleButtonClick} disabled={disabled} />
        </Stack>
    );
};
