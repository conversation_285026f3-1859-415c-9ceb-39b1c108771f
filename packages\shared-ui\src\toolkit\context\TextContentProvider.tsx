import { Direction } from "@mui/material";
import React, { createContext, useContext, useState } from "react";
import { useAppTheme } from "../theme/AppThemeProvider";
import { useLocalStorageState } from "../hooks/useLocalStorageState";

export type Language = "en" | "he";
export type AppText = Record<Language, AppTextContent>;

interface AppTextContent {
    direction: Direction;
    [key: string]: any;
}

interface TextContextValue {
    currentLanguage: Language;
    appText: AppTextContent;
    changeLanguage: (newText: Language) => void;
    getContentForDisplay: (keyChain: string) => string;
}

export const TextContext = createContext<TextContextValue | undefined>(undefined);

interface TextProviderProps {
    children: React.ReactNode;
    appLanguagesTextMap: AppText;
}

export const TextProvider = ({ children, appLanguagesTextMap }: TextProviderProps) => {
    const { changeDirection } = useAppTheme();
    const [currentLanguage, setCurrentLanguage] = useLocalStorageState<Language>("app-language", "en");
    const [appText, setAppText] = useState<AppTextContent>(appLanguagesTextMap[currentLanguage]);

    const changeLanguage = (newText: Language) => {
        setAppText(appLanguagesTextMap[newText]);
        changeDirection(appLanguagesTextMap[newText].direction);
        setCurrentLanguage(newText);
    };

    const getContentForDisplay = (keyChain: string): string => {
        const keys = keyChain.split(".");
        return (
            keys.reduce((acc, key) => acc[key], appText) ? keys.reduce((acc, key) => acc[key], appText) : ""
        ) as string;
    };

    return (
        <TextContext.Provider value={{ appText, currentLanguage, changeLanguage, getContentForDisplay }}>
            {children}
        </TextContext.Provider>
    );
};

export const useAppText = (): TextContextValue => {
    const context = useContext(TextContext);
    if (context === undefined) {
        throw new Error("useAppText must be used within a TextProvider");
    }
    return context;
};
