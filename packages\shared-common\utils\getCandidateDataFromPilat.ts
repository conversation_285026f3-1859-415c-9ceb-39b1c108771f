import { AxiosError } from "axios";
import { Logger } from "winston";
import axios from "axios";
import { sleep } from "./sleep";
import { PilatCandidateData, PilatCandidateDataRequest } from "../types/PilateCandidateData";

export async function getCandidateDataFromPilat(args: {
    pilatInterviewId: string;
    logger: Logger;
    maxRetries: number;
    pilatCandidateDataUrl: string;
    pilateApiKey: string;
}): Promise<PilatCandidateData> {
    const { pilatInterviewId, logger, maxRetries, pilatCandidateDataUrl, pilateApiKey} = args;
    
    const delay = 1500;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            logger.info(`Fetching candidate data from Pilat for candidate_id: ${pilatInterviewId} - url:${pilatCandidateDataUrl}`);

            const response = await axios.post<string>(pilatCandidateDataUrl, { encTesteeId: pilatInterviewId } as PilatCandidateDataRequest, {
                headers: {
                    "x-api-key": pilateApiKey,
                },
                timeout: 10000,
            });


            return response.data as unknown as PilatCandidateData;
        } catch (error) {
            logger.error(`Attempt ${attempt + 1} failed: ${(error as AxiosError).message}`);
            logger.error(`ERROR: ${JSON.stringify(error)}`);
            if (attempt < maxRetries - 1) {
                await sleep(delay);
            } else {
                logger.error(`Failed to fetch CANDIDATE DATA after ${maxRetries} attempts.`);
            }
        }
    }
    throw new Error("Failed to fetch candidate data from pilat after max retries");
}
