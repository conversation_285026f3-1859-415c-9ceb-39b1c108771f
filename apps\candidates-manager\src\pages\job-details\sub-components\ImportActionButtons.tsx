import React from "react";
import { Stack } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { PrimaryButton } from "@eva/shared-ui";

export interface ImportActionButtonsProps {
    onDownloadTemplate: () => void;
    onUploadClick: () => void;
    isLoading: boolean;
    isCreating: boolean;
}

export const ImportActionButtons: React.FC<ImportActionButtonsProps> = ({
    onDownloadTemplate,
    onUploadClick,
    isLoading,
    isCreating,
}) => (
    <Stack direction="row" width="100%" gap={2}>
        <PrimaryButton
            variant="outlined"
            content="Download Template"
            onClick={onDownloadTemplate}
            endIcon={<FileDownloadIcon />}
        />

        <PrimaryButton
            variant="contained"
            content="Upload Excel File"
            onClick={onUploadClick}
            disabled={isLoading || isCreating}
            endIcon={<CloudUploadIcon />}
        />
    </Stack>
); 