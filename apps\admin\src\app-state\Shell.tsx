import { Authenticator } from "@aws-amplify/ui-react";
import { CssBaseline } from "@mui/material";
import { Amplify } from "aws-amplify";
import { Provider as StoreProvider } from "react-redux";
import awsExports from "../aws-exports";
import { TextProvider, AppThemeProvider } from "@eva/shared-ui";
import { appText } from "./appText";
import { store } from "./store";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";

Amplify.configure(awsExports);

interface ShellProps {
    children: React.ReactNode;
}

export const Shell = ({ children }: ShellProps) => (
    <Authenticator.Provider>
        <StoreProvider store={store}>
            <AppThemeProvider>
                <CssBaseline />
                <ToastContainer />
                <TextProvider appLanguagesTextMap={appText}>{children}</TextProvider>
            </AppThemeProvider>
        </StoreProvider>
    </Authenticator.Provider>
);
