import { Chip, Divider, Paper, SelectChangeEvent, Stack, styled } from "@mui/material";
import { useField } from "formik";
import { FC } from "react";
import { useGetAllAccountsQuery, useGetCustomersByAccountIdQuery } from "../../../app-state/apis";
import { languageMapper, languages } from "../../../app-state/appText";
import { H1, CustomSelect, CustomTextField, useAppText } from "@eva/shared-ui";
import { CustomerLogo } from "../../../components/CustomerLogo";
import { JobStatus } from "@eva/shared-common";
import { JobStatusChip } from "../../../components/JobStatusChip";

interface JobBuilderFormBodyGeneralDetailsProps {
    status: JobStatus;
}

export const JobBuilderFormBodyGeneralDetails: FC<JobBuilderFormBodyGeneralDetailsProps> = ({ status }) => {
    const { getContentForDisplay } = useAppText();
    const { data: accountsData, isFetching: isAccountsLoading } = useGetAllAccountsQuery(undefined);
    const [titleField, , { setValue: setTitleValue }] = useField("title");
    const [descriptionField, , { setValue: setDescriptionValue }] = useField("description");
    const [languageField, , { setValue: setLanguageValue }] = useField("language");
    const [accountIdField, , { setValue: setAccountIdValue }] = useField("accountId");
    const [customerField, , { setValue: setCustomerValue }] = useField("customerId");
    const { data: customersData, isFetching: isCustomersLoading } = useGetCustomersByAccountIdQuery(
        accountIdField.value,
        {
            skip: !accountIdField.value,
        }
    );

    const handlerCustomerChange = (event: any) => {
        setCustomerValue(event.target.value as string);
    };

    const handleLanguageChange = (event: SelectChangeEvent) => {
        setLanguageValue(event.target.value);
    };

    const handleTitleChange = (event: React.ChangeEvent<{ value: string }>) => {
        setTitleValue(event.target.value);
    };

    const handleDescriptionChange = (event: React.ChangeEvent<{ value: string }>) => {
        setDescriptionValue(event.target.value);
    };

    const handleAccountIdChange = (event: SelectChangeEvent) => {
        setAccountIdValue(event.target.value);
        setCustomerValue("");
    };

    const headerText = getContentForDisplay("jobBuilderForm.jobDetails");
    const customerSelectText = getContentForDisplay("jobBuilderForm.jobGeneralDetails.customerSelect");
    const languageSelectText = getContentForDisplay("jobBuilderForm.jobGeneralDetails.languageSelect");
    const titleInputText = getContentForDisplay("jobBuilderForm.jobGeneralDetails.titleInput");
    const descriptionInputText = getContentForDisplay("jobBuilderForm.jobGeneralDetails.descriptionInput");

    const isFormDisabled = !accountsData || !customersData || accountIdField.value === "";
    const customerOptions = customersData
        ? customersData?.customers.map((customer) => ({
              label: customer.companyName,
              value: customer.id,
          }))
        : [];
    const accountsOptions = accountsData
        ? accountsData.accounts.map((account) => ({
              label: account.name,
              value: account.id,
          }))
        : [];

    const languagesOptions = languages.map((language) => ({
        value: language,
        label: languageMapper(language),
    }));

    return (
        <Paper elevation={0}>
            <Stack gap={5} padding={4}>
                <Stack direction="row" justifyContent="space-between">
                    <Stack direction="row" gap={3}>
                        <H1>{headerText}</H1>
                        {status && <JobStatusChip status={status} />}
                    </Stack>
                    {customerField.value && <CustomerLogo customerId={customerField.value} />}
                </Stack>
                <Divider />
                <Stack direction="row" gap={3} justifyContent="start">
                    <CustomSelect
                        disabled
                        loading={isAccountsLoading}
                        label="Accounts"
                        value={accountIdField.value}
                        onChange={handleAccountIdChange}
                        options={accountsOptions}
                    />
                    <CustomSelect
                        disabled
                        loading={isCustomersLoading}
                        label={customerSelectText}
                        value={customerField.value}
                        onChange={handlerCustomerChange}
                        options={customerOptions}
                    />
                </Stack>
                <Stack direction="row" gap={3} justifyContent="start">
                    <CustomSelect
                        disabled={isFormDisabled}
                        width={230}
                        label={languageSelectText}
                        value={languageField.value}
                        onChange={handleLanguageChange}
                        options={languagesOptions}
                    />
                    <CustomTextField
                        width={300}
                        value={titleField.value}
                        onChange={handleTitleChange}
                        label={titleInputText}
                    />
                    <CustomTextField
                        width={700}
                        value={descriptionField.value}
                        onChange={handleDescriptionChange}
                        label={descriptionInputText}
                    />
                </Stack>
            </Stack>
        </Paper>
    );
};
