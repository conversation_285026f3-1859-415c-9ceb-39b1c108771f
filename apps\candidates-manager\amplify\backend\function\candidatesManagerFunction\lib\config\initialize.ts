import { NextFunction, Request, Response } from "express";
import { DB, initDB } from "@eva/drizzle";
import { Logger } from "@eva/logger";
import { setupLogging } from "@eva/logger";
import { JobService } from "../services/JobService";
import { CustomerService } from "../services/CustomeService";
import { CandidateService } from "../services/CandidateService";

let db: DB | null = null;
let logger: Logger = setupLogging();
let jobService: JobService;
let customerService: CustomerService;
let candidateService: CandidateService;

export const getLogger = () => logger;
export const getJobService = () => jobService;
export const getCustomerService = () => customerService;
export const getCandidateService = () => candidateService;

const initializeResources = async () => {
    if (!db) {
        logger.info("Initializing DB connection");
        db = await initDB();
        logger.info("DB connection initialized");
    }
    if (!jobService) {
        jobService = new JobService(db, logger);
    }
    if (!customerService) {
        customerService = new CustomerService(db, logger);
    }
    if (!candidateService) {
        candidateService = new CandidateService(db, logger);
    }
};

export const initializeMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
        await initializeResources();
        next();
    } catch (error) {
        logger.error("Error initializing resources", error);
        next(error);
    }
};
