import { configureStore } from "@reduxjs/toolkit";
import { candidateManagerApi } from "./apis";

export const store = configureStore({
    reducer: {
        [candidateManagerApi.reducerPath]: candidateManagerApi.reducer,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(candidateManagerApi.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
