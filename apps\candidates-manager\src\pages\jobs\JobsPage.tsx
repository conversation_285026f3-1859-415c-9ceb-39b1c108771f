import { useState } from "react";
import { useGetJobsQuery } from "../../app-state/apis";
import { useUserDetails } from "../../app-state/context/UserDetailsProvider";
import { PageContainer, PageLayout, LoadingScreen, PrimaryButton } from "@eva/shared-ui";
import { JobsDataTable } from "./sub-components/JobsDataTable";
import { JobsDataTableFilter, JobsFilterFields } from "./sub-components/JobsDataTableFilter";
import { Stack } from "@mui/material";
import { AddJobModal } from "./sub-components/AddJobModal";
import AddIcon from '@mui/icons-material/Add';

const JobsPage = () => {
    const { customerId } = useUserDetails();
    const { data, isLoading, error } = useGetJobsQuery(customerId ?? "", {
        skip: !customerId,
    });

    const [filterFields, setFilterFields] = useState<JobsFilterFields>({
        searchText: "",
    });
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);

    const handleFilterChange = (filterFields: JobsFilterFields) => {
        setFilterFields(filterFields);
    };

    const handleOpenAddModal = () => {
        setIsAddModalOpen(true);
    };

    const handleCloseAddModal = () => {
        setIsAddModalOpen(false);
    };

    const filteredJobs = data
        ? data?.jobs.filter((job) => {
              return job.title.toLowerCase().includes(filterFields.searchText.toLowerCase());
          })
        : [];

    return (
        <PageContainer>
            {isLoading ? (
                <LoadingScreen />
            ) : (
                <PageLayout transparent>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                        <JobsDataTableFilter
                            filterFields={filterFields}
                            handleFilterChange={handleFilterChange}
                            resultsCount={filteredJobs.length}
                        />
                        <PrimaryButton
                            content="Add Job"
                            variant="contained"
                            onClick={handleOpenAddModal}
                            width="auto"
                            endIcon={<AddIcon />}
                        />
                    </Stack>
                    <JobsDataTable jobs={filteredJobs ?? []} />
                </PageLayout>
            )}
            <AddJobModal isOpen={isAddModalOpen} onClose={handleCloseAddModal} />
        </PageContainer>
    );
};

export default JobsPage;
