import { FamiliesWithTraitsForDisplayMap, Family, FamilyId, LanguageVariation, Trait } from "@eva/shared-common";
import { Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";
import { TraitProgressBar } from "../TraitProgressBar";
import { FamilySectionHeader } from "./sub-components/FamilySectionHeader";
import { ZeroScoreExplanation } from "./sub-components/ZeroScoreExplanation";

interface FamilySectionProps {
    isRtl: boolean;
    language: string;
    familiesMapForDisplay: FamiliesWithTraitsForDisplayMap;
    family: Family;
    traits: Trait[];
}

export const FamilySection: FC<FamilySectionProps> = ({ isRtl, familiesMapForDisplay, family, language, traits }) => {
    const familyName = familiesMapForDisplay[family.code as FamilyId].family[language as keyof LanguageVariation];
    const familyTraits = traits.filter((trait) => Object.keys(familiesMapForDisplay[family.code].traitsMap).includes(trait.code));
    const summary = family.summary;
    const score = family.score;
    const traitsMap = familiesMapForDisplay[family.code].traitsMap;
    const scoreForDisplay = score === 0 ? "*" : score.toFixed(1);

    const overallScoreText = isRtl ? "ציון כולל" : "Overall Score";
    const isThereZero = familyTraits.some((trait) => trait.score === 0);

    const zeroScoreExplanation = isRtl
        ? "לא ניתן להעריך את התכונה על סמך התשובה שסופקה *"
        : "* The trait could not be assessed based on the provided answer.";

    const verbalAbilityNotIncludedInScoreMessage = isRtl
        ? "יכולת ביטוי לא נכללת בציון הכולל"
        : "Verbal Ability is not included in the overall score";

    const isVerbalAbilityExists = familyTraits.some((trait) => trait.code === "A38");

    return (
        <View style={styles.sectionContainer}>
            <FamilySectionHeader
                familyCode={family.code}
                familyName={familyName}
                isRtl={isRtl}
                overallScoreText={overallScoreText}
                scoreForDisplay={scoreForDisplay}
            />
            <View style={[styles.familySectionContentContainer]}>
                <View style={styles.familySectionScoresContainer}>
                    {familyTraits.map((trait, idx) => (
                        <TraitProgressBar
                            isRtl={isRtl}
                            videoLink={trait.video_link}
                            traitName={traitsMap[trait.code].trait[language as keyof LanguageVariation]}
                            key={trait.code}
                            traitIndex={idx}
                            totalTraits={familyTraits.length}
                            score={trait.score}
                            max={5}
                            width={190}
                            segments={5}
                            gap={0.6}
                        />
                    ))}
                </View>
                <View style={[styles.familySectionContentTextContainer]}>
                    <Text style={[isRtl ? styles.hebrewTextContent : styles.textContent, isRtl ? styles.alignTextRight : {}]}>
                        {summary.split("\n").join(" ")}
                    </Text>
                    {isThereZero && <ZeroScoreExplanation isRtl={isRtl} />}
                    {isVerbalAbilityExists && (
                        <Text style={[isRtl ? styles.hebrewTextBoldSubTitle : styles.textBoldSubTitle, isRtl ? styles.alignTextRight : {}]}>
                            {verbalAbilityNotIncludedInScoreMessage}
                        </Text>
                    )}
                </View>
            </View>
        </View>
    );
};
