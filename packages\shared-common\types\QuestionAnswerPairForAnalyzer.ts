import { LanguageVariation } from "./FamiliesWithTraitsForDisplayMap";
import { FamilyPromptMetadata } from "./FamilyPromptMetadata";
import { GradingPrinciplesMap } from "./GradingPrinciple";
import { QuestionOptions } from "./QuestionOptions";
import { TraitPromptMetadata } from "./TraitPromptMetadata";
import { TraitSummariesMap } from "./TraitSummariesMap";

export interface AnswerForAnalyzer {
    transcription: string;
    translatedTranscription: string;
    videoLink: string;
}

export interface FamilyTraitForAnalyzer {
    metadata: FamilyPromptMetadata;
    id: string;
    options: LanguageVariation;
}

export interface TraitForAnalyzer {
    id: string;
    options: LanguageVariation;
    traitSummariesMap: TraitSummariesMap;
    familyTrait: FamilyTraitForAnalyzer;
    metadata: TraitPromptMetadata | null;
}

export interface QuestionForAnalyzer {
    id: string;
    gradingPrinciples: GradingPrinciplesMap;
    options: QuestionOptions;
    trait: TraitForAnalyzer;
}

export interface TemplatesMap {
    en: Record<string, string>;
    he: Record<string, string>;
}

export interface QuestionAnswerPairForAnalyzer {
    question: QuestionForAnalyzer;
    answer: AnswerForAnalyzer;
}
