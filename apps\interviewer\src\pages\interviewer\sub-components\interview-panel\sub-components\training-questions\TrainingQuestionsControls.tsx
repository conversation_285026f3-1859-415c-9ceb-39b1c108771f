import { useAppText } from "@eva/shared-ui";
import { Stack } from "@mui/material";
import { InterviewerPrimaryButton } from "../../../../../../components/buttons/InterviewerPrimaryButton";
import { useInterviewer } from "../../../../InterviewerProvider";

export const TrainingQuestionsControls = () => {
    const { finishTraining } = useInterviewer();
    const { getContentForDisplay } = useAppText();
    const goBackText = getContentForDisplay("interviewer.trainingQuestionPreview.goBack");
    return (
        <Stack direction='row' gap='24px' paddingBlock={4}>
            <InterviewerPrimaryButton data-qa='training-go-back-button' content={goBackText} onClick={finishTraining} />
        </Stack>
    );
};
