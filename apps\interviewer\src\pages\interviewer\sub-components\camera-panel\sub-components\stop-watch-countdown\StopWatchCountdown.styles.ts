import { styled, keyframes } from '@mui/material';
import { motion } from 'framer-motion';


const pulsate = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
`;

export const CountdownContainer = styled(motion.div)({
    background: 'grey',
    padding: '8px 16px',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '8px',
});

export const VibratingDot = styled('div')({
    width: '10px',
    height: '10px',
    background: 'red',
    borderRadius: '50%',
    marginRight: '8px',
    animation: `${pulsate} 0.5s infinite`
});

export const TimeText = styled('p')({
    color: 'white',
    fontSize: '16px',
    margin: 0
});