import { ConfirmationDialog, formatDate, H1, Language, Overline, PrimaryButton, useAppText } from "@eva/shared-ui";
import BackupIcon from "@mui/icons-material/Backup";
import CancelPresentationIcon from "@mui/icons-material/CancelPresentation";
import EditIcon from "@mui/icons-material/Edit";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import SaveIcon from "@mui/icons-material/Save";
import { Stack } from "@mui/material";
import { Suspense, useState } from "react";
import { languageMapper } from "../../../app-state/appText";
import { PDFActionButton } from "../../../components/PDFActionButton";
import { useReviewPageContext } from "../context/ReviewPageProvider";
import { ReviewPageDetailsCell } from "./ReviewPageDetailsCell";
import { WatchDiffsButton } from "./WatchDiffsButton";

const ReviewPageGeneralDetails = ({}) => {
    const {
        isEditMode,
        interviewData,
        editRequest,
        familiesWithTraitsMap,
        additionalQuestionsResponse,
        warmupQuestionsResponse,
        handleSubmitReport,
        handleEditClick,
        onExitEditModeClicked,
        handleSaveClick,
        handleResetClick,
    } = useReviewPageContext();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { getContentForDisplay } = useAppText();

    const handlerSubmitClick = () => {
        setIsModalOpen(true);
    };

    const handleClose = () => {
        setIsModalOpen(false);
    };

    const isReviewSessionInProgress = interviewData?.reportEditSession ? true : false;
    const interview = interviewData?.interview;
    const interviewScore =
        isEditMode && editRequest
            ? editRequest.interview_score
            : interviewData
              ? interviewData.report.interview_score
              : 0;

    const reviewedBy = interviewData?.report.reviewed_by;
    const reviewDate = interviewData?.report.review_date;
    const isApproved = interviewData?.report.approved;
    const approvedBy = interviewData?.report.approved_by;
    const approvedDate = interviewData?.report.approved_date;

    const exitReviewModeText = getContentForDisplay("reviewPage.exitReviewMode");
    const saveChangesText = getContentForDisplay("reviewPage.saveChanges");
    const resetChangesText = getContentForDisplay("reviewPage.resetChanges");
    const submitReviewText = getContentForDisplay("reviewPage.submitReview");
    const backToReviewModeText = getContentForDisplay("reviewPage.backToReviewMode");
    const startReviewText = getContentForDisplay("reviewPage.startReview");
    const scoreText = getContentForDisplay("reviewPage.score");
    const candidateNameText = getContentForDisplay("reviewPage.candidateName");
    const socialIdText = getContentForDisplay("reviewPage.socialId");
    const emailText = getContentForDisplay("reviewPage.email");
    const companyNameText = getContentForDisplay("reviewPage.companyName");
    const jobTitleText = getContentForDisplay("reviewPage.jobTitle");
    const languageText = getContentForDisplay("reviewPage.language");
    const interviewDateText = getContentForDisplay("reviewPage.interviewDateText");
    const dialogModalContent = getContentForDisplay("reviewPage.dialogModal.content");
    const reviewDateText = getContentForDisplay("reviewPage.reviewDate");
    const reviewedByText = getContentForDisplay("reviewPage.reviewedBy");

    return (
        <>
            <Stack padding={3} gap={4}>
                <Stack direction="row" justifyContent="space-between">
                    <Stack direction="row" gap={2} alignItems="center">
                        <H1>Evaluation Report</H1>
                        {isReviewSessionInProgress && <Overline color="error">Review Session In Progress</Overline>}
                        {isApproved && <Overline color="secondary">Approved</Overline>}
                    </Stack>
                    {isEditMode ? (
                        <Stack direction="row" gap={2}>
                            <PrimaryButton
                                onClick={onExitEditModeClicked}
                                endIcon={<CancelPresentationIcon />}
                                content={exitReviewModeText}
                            />
                            <PrimaryButton onClick={handleSaveClick} endIcon={<SaveIcon />} content={saveChangesText} />
                            <PrimaryButton
                                onClick={handleResetClick}
                                endIcon={<RestartAltIcon />}
                                content={resetChangesText}
                            />
                            <PrimaryButton
                                onClick={handlerSubmitClick}
                                endIcon={<BackupIcon />}
                                content={submitReviewText}
                            />
                        </Stack>
                    ) : (
                        <Stack direction="row" gap={3}>
                            {familiesWithTraitsMap && interviewData && additionalQuestionsResponse && (
                                <>
                                    <WatchDiffsButton />
                                    <PDFActionButton
                                        actionType="export"
                                        familiesWithTraitsMap={familiesWithTraitsMap}
                                        interviewData={interviewData}
                                        additionalQuestionsResponse={additionalQuestionsResponse}
                                        warmupQuestionsResponse={warmupQuestionsResponse}
                                    />
                                    {!isApproved && !isReviewSessionInProgress && (
                                        <PDFActionButton
                                            actionType="approveReport"
                                            familiesWithTraitsMap={familiesWithTraitsMap}
                                            interviewData={interviewData}
                                            additionalQuestionsResponse={additionalQuestionsResponse}
                                            warmupQuestionsResponse={warmupQuestionsResponse}
                                        />
                                    )}
                                </>
                            )}
                            <PrimaryButton
                                onClick={handleEditClick}
                                endIcon={<EditIcon />}
                                content={isReviewSessionInProgress ? backToReviewModeText : startReviewText}
                            />
                        </Stack>
                    )}
                </Stack>
                {interview && (
                    <Stack direction="row" gap={2} flexWrap="wrap">
                        <ReviewPageDetailsCell label={scoreText} value={interviewScore.toFixed(1)} />
                        <ReviewPageDetailsCell label={candidateNameText} value={interview.candidateName} />
                        {interview.socialId && (
                            <ReviewPageDetailsCell label={socialIdText} value={interview.socialId} />
                        )}
                        {interview.email && <ReviewPageDetailsCell label={emailText} value={interview.email} />}
                        <ReviewPageDetailsCell label={companyNameText} value={interview.candidateName} />
                        <ReviewPageDetailsCell label={jobTitleText} value={interview.jobTitle} />
                        <ReviewPageDetailsCell
                            label={languageText}
                            value={languageMapper(interview.language as Language)}
                        />
                        <ReviewPageDetailsCell label={interviewDateText} value={formatDate(interview.interviewDate)} />
                        {reviewedBy && <ReviewPageDetailsCell label={reviewedByText} value={reviewedBy} />}
                        {reviewDate && <ReviewPageDetailsCell label={reviewDateText} value={formatDate(reviewDate)} />}
                        {approvedBy && <ReviewPageDetailsCell label="Approved By" value={approvedBy} />}
                        {approvedDate && (
                            <ReviewPageDetailsCell label="Approval Date" value={formatDate(approvedDate)} />
                        )}
                    </Stack>
                )}
            </Stack>
            {isModalOpen && (
                <Suspense fallback={null}>
                    <ConfirmationDialog
                        isOpen={isModalOpen}
                        onClose={handleClose}
                        onSubmit={handleSubmitReport}
                        message={dialogModalContent}
                    />
                </Suspense>
            )}
        </>
    );
};

export default ReviewPageGeneralDetails;
