import { useMediaQueryContext } from "@eva/shared-ui";
import { DesktopVideoPlayer } from "./DesktopVideoPlayer";
import { MobileVideoPlayer } from "./MobileVideoPlayer";

export interface VideoPlayerProps {
    videoUrl: string;
    height?: string;
    width?: string;
    "data-qa"?: string;
    onVideoEnd?: () => void;
    onVideoStart?: () => void;
    controls?: boolean;
    hidden?: boolean;
    autoPlayDisabled?: boolean;
}

export const VideoPlayer = (props: VideoPlayerProps) => {
    const { isMobile } = useMediaQueryContext();
    
    return isMobile ? <MobileVideoPlayer {...props} /> : <DesktopVideoPlayer {...props} />;
};
