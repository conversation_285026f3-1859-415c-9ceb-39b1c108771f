import { NextFunction, Request, Response, Router } from "express";
import { getCandidateService, getLogger } from "../config/initialize";
import {
    CMCandidateDetailsResponse,
    CMCandidatesTableDataResponse,
    CMInviteCandidatesRequest,
    CMUpdateCandidateRequest,
    CreateCandidateRequest,
    InterviewState,
    InterviewStatus,
    ValidateCandidatesResponse,
} from "@eva/shared-common";
const router = Router();
const logger = getLogger();

router.get(
    "/:customerId",
    async (
        req: Request<{ customerId: string }, {}, {}, {}>,
        res: Response<CMCandidatesTableDataResponse, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const candidates = await candidateService.getCandidates(req.params.customerId);
            res.json(candidates);
        } catch (error) {
            logger.error("Error getting candidates");
            next(error);
        }
    }
);

router.get(
    "/job/:jobId",
    async (
        req: Request<{ jobId: string }, {}, {}, {}>,
        res: Response<CMCandidatesTableDataResponse, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const candidates = await candidateService.getCandidatesByJobId(req.params.jobId);
            res.json(candidates);
        } catch (error) {
            logger.error(`Error getting candidates for job ${req.params.jobId}`);
            next(error);
        }
    }
);

router.get(
    "/details/:id",
    async (
        req: Request<{ id: string }, {}, {}, {}>,
        res: Response<CMCandidateDetailsResponse | null, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const candidate = await candidateService.getCandidateById(req.params.id);

            res.json(candidate);
        } catch (error) {
            logger.error(`Error getting candidate ${req.params.id}: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/",
    async (req: Request<{}, {}, CreateCandidateRequest, {}>, res: Response<{ id: string }, {}>, next: NextFunction) => {
        try {
            const candidateService = getCandidateService();
            const candidateId = await candidateService.createCandidate(req.body);
            res.status(201).json({ id: candidateId });
        } catch (error) {
            logger.error(`Error creating candidate: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/bulk",
    async (
        req: Request<{}, {}, { candidates: CreateCandidateRequest[] }, {}>,
        res: Response<{ count: number } | { status: number; message: string }, {}>,
        next: NextFunction
    ) => {
        try {
            const { candidates } = req.body;

            if (!candidates || !Array.isArray(candidates) || candidates.length === 0) {
                return res.status(400).json({ status: 400, message: "Invalid candidates data" });
            }

            logger.info(`Received bulk import request for ${candidates.length} candidates`);

            const candidateService = getCandidateService();
            const results = await candidateService.bulkCreateCandidates(candidates);

            res.status(201).json({ count: results.length });
        } catch (error) {
            logger.error(`Error bulk creating candidates: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/invite",
    async (req: Request<{}, {}, CMInviteCandidatesRequest, {}>, res: Response<{}, {}>, next: NextFunction) => {
        try {
            const { jobId, candidateIds } = req.body;

            if (!jobId || !candidateIds || !Array.isArray(candidateIds) || candidateIds.length === 0) {
                return res.status(400).json({ error: "Invalid request parameters" });
            }

            const candidateService = getCandidateService();
            await candidateService.inviteCandidates({ jobId, candidateIds });

            res.status(200).json({ message: `Successfully invited ${candidateIds.length} candidates` });
        } catch (error) {
            logger.error(`Error inviting candidates: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/check-duplicate",
    async (
        req: Request<{}, {}, { email: string; jobId: string }, {}>,
        res: Response<{ exists: boolean }, {}>,
        next: NextFunction
    ) => {
        try {
            const { email, jobId } = req.body;

            const candidateService = getCandidateService();
            const exists = await candidateService.checkDuplicateCandidate(email, jobId);

            res.json({ exists });
        } catch (error) {
            logger.error(`Error checking for duplicate candidate: ${error}`);
            next(error);
        }
    }
);

router.get(
    "/:candidateId/score",
    async (
        req: Request<{ candidateId: string }, {}, {}, {}>,
        res: Response<{ score: number | null }, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const result = await candidateService.getCandidateScore(req.params.candidateId);
            res.json(result);
        } catch (error) {
            logger.error(`Error getting score for candidate ${req.params.candidateId}: ${error}`);
            next(error);
        }
    }
);

router.get(
    "/:candidateId/status",
    async (
        req: Request<{ candidateId: string }, {}, {}, {}>,
        res: Response<{ status: InterviewStatus | null }, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const result = await candidateService.getCandidateStatus(req.params.candidateId);
            res.json(result);
        } catch (error) {
            logger.error(`Error getting status for candidate ${req.params.candidateId}: ${error}`);
            next(error);
        }
    }
);

router.get(
    "/:candidateId/report",
    async (req: Request<{ candidateId: string }, {}, {}, {}>, res: Response<{}, {}>, next: NextFunction) => {
        try {
            const candidateService = getCandidateService();
            const report = await candidateService.downloadCandidateReport(req.params.candidateId);

            res.setHeader("Content-Type", "application/pdf");
            res.setHeader("Content-Disposition", `attachment; filename=report-${req.params.candidateId}.pdf`);
            res.send(report);
        } catch (error) {
            logger.error(`Error generating report for candidate ${req.params.candidateId}: ${error}`);
            next(error);
        }
    }
);

router.get(
    "/:candidateId/report-exists",
    async (
        req: Request<{ candidateId: string }, {}, {}, {}>,
        res: Response<{ interviewId: string | null }, {}>,
        next: NextFunction
    ) => {
        try {
            const candidateService = getCandidateService();
            const result = await candidateService.checkIfReportExists(req.params.candidateId);
            res.json(result);
        } catch (error) {
            logger.error(`Error checking if report exists for candidate ${req.params.candidateId}: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/validate-invitation",
    async (
        req: Request<{}, {}, { candidateIds: string[] }, {}>,
        res: Response<ValidateCandidatesResponse, {}>,
        next: NextFunction
    ) => {
        try {
            const { candidateIds } = req.body;

            if (!candidateIds || !Array.isArray(candidateIds) || candidateIds.length === 0) {
                return res.status(400).json({
                    validCandidates: [],
                    invalidCandidates: [],
                });
            }

            const candidateService = getCandidateService();
            const result = await candidateService.validateCandidatesForInvitation(candidateIds);

            res.json(result);
        } catch (error) {
            logger.error(`Error validating candidates: ${error}`);
            next(error);
        }
    }
);

router.post(
    "/validate-email",
    async (
        req: Request<{}, {}, { email: string; jobId: string }, {}>,
        res: Response<{ isValid: boolean; message?: string }, {}>,
        next: NextFunction
    ) => {
        try {
            const { email, jobId } = req.body;

            if (!email || !jobId) {
                return res.status(400).json({
                    isValid: false,
                    message: "Email and jobId are required",
                });
            }

            const candidateService = getCandidateService();
            const result = await candidateService.validateCandidateEmail(email, jobId);

            res.json(result);
        } catch (error) {
            logger.error(`Error validating email: ${error}`);
            next(error);
        }
    }
);

router.post<{ candidateId: string }, { message: string }, CMUpdateCandidateRequest>("/:candidateId/update", async (req, res, next) => {
    try {
        const { candidateId } = req.params;
        const updateData = req.body;

        const candidateService = getCandidateService();
        await candidateService.updateCandidate(candidateId, updateData);

        res.json({ message: "Candidate updated successfully" });
    } catch (error) {
        logger.error(`Error updating candidate ${req.params.candidateId}: ${error}`);
        next(error);
    }
});

export default router;
