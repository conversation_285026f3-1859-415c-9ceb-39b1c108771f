import { FC } from "react";
import { useGetCustomerLogoQuery } from "../app-state/apis";
import { Stack } from "@mui/material";

interface CustomerLogoProps {
    customerId: string;
    size?: number;
}

export const CustomerLogo: FC<CustomerLogoProps> = ({ customerId ,size = 120}) => {
    const { data } = useGetCustomerLogoQuery(customerId, {
        skip: !customerId,
    });
    return data && data.logoLink ? <Stack width={size}><img src={data.logoLink}  alt='Customer Logo' /></Stack> : null;
};
