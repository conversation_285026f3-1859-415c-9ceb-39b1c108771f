import React from 'react';
import { CenteredModal } from '@eva/shared-ui';
import { CreateCandidateForm } from './CreateCandidateForm';

interface AddCandidateModalProps {
    isOpen: boolean;
    onClose: () => void;
    jobId: string;
    isHebrew: boolean;
}

export const AddCandidateModal: React.FC<AddCandidateModalProps> = ({ 
    isOpen, 
    onClose, 
    jobId,
    isHebrew
}) => {
    const handleSuccess = () => {
        onClose();
    };

    return (
        <CenteredModal
            isOpen={isOpen}
            onClose={onClose}
            width={500}
            height="auto"
        >
            <CreateCandidateForm 
                jobId={jobId}
                isHebrew={isHebrew}
                onSuccess={handleSuccess}
                onCancel={onClose}
            />
        </CenteredModal>
    );
}; 