import { Document, Page } from "@react-pdf/renderer";
import { FC } from "react";
import { AdditionalQuestion, FamiliesWithTraitsForDisplayMap, Family, ReportVersion, Trait } from "@eva/shared-common";
import { stylesPDF as styles } from "../PDF.styles";
import { AdditionalQuestionsSection } from "./sub-components/AdditionalQuestionsSection";
import { FamilySection } from "./sub-components/FamilySection/FamilySection";
import { ReportFooter } from "./sub-components/report-footer/ReportFooter";
import { ReportHeader } from "./sub-components/report-header/ReportHeader";
import { FinalScoreSection } from "./sub-components/final-score-section/FinalScoreSection";
import { ReportExplanation } from "./sub-components/ReportExplanation";

const shouldMoveSecondFamilyToSecondPage = (
    families: Family[],
    traits: Trait[],
    familiesMapForDisplay: FamiliesWithTraitsForDisplayMap
): boolean => {
    const firstAndSecondFamilySummariesCount = (families[0].summary + families[1].summary).length;
    const familiesWithZeros = [];
    for (const family of families.slice(0, 2)) {
        const familyTraits = traits.filter((trait) =>
            Object.keys(familiesMapForDisplay[family.code].traitsMap).includes(trait.code)
        );
        const isThereZero = familyTraits.some((trait) => !trait.score);
        if (isThereZero) {
            familiesWithZeros.push(family);
        }
    }

    if (familiesWithZeros.length === 2 && firstAndSecondFamilySummariesCount > 300) return true;
    if (familiesWithZeros.length === 1 && firstAndSecondFamilySummariesCount > 600) return true;

    return firstAndSecondFamilySummariesCount > 750;
};

interface ReportPDFProps {
    reportData: ReportVersion;
    familiesWithTraitsMap: FamiliesWithTraitsForDisplayMap;
    additionalQuestions: AdditionalQuestion[];
    warmupQuestions: AdditionalQuestion[];
    candidateName: string;
    customerName: string;
    interviewDate: string;
    candidateImage: string | null;
    candidateId: string;
    language: string;
    jobTitle: string;
    email: string | null;
}

export const ReportPDF: FC<ReportPDFProps> = ({
    candidateImage,
    reportData,
    familiesWithTraitsMap,
    additionalQuestions,
    warmupQuestions,
    candidateName,
    customerName,
    interviewDate,
    candidateId,
    language,
    jobTitle,
    email,
}) => {
    const { families, traits } = reportData;
    const isRtl = language === "he";
    const shouldMoveSecondFamily = shouldMoveSecondFamilyToSecondPage(families, traits, familiesWithTraitsMap);
    const sortedFamilies = [...families].sort((a, b) => a.code.localeCompare(b.code));
    const firstPageFamilies = sortedFamilies.slice(0, shouldMoveSecondFamily ? 1 : 2);
    const secondPageFamilies = sortedFamilies.slice(shouldMoveSecondFamily ? 1 : 2);
    const totalPages = secondPageFamilies.length > 2 ? 3 : 2;
    return (
        <Document>
            <Page style={styles.page} key={1}>
                <ReportHeader
                    isRtl={isRtl}
                    candidateId={candidateId}
                    candidateImage={candidateImage}
                    candidateName={candidateName}
                    customerName={customerName}
                    interviewDate={interviewDate}
                    jobTitle={jobTitle}
                    email={email}
                />
                <FinalScoreSection
                    isRtl={isRtl}
                    familiesMapForDisplay={familiesWithTraitsMap}
                    reportData={reportData}
                    language={language}
                />
                {warmupQuestions.length > 0 && (
                    <AdditionalQuestionsSection warmup isRtl={isRtl} additionalQuestions={warmupQuestions} />
                )}
                {firstPageFamilies.map((firstPageFamily) => (
                    <FamilySection
                        isRtl={isRtl}
                        family={firstPageFamily}
                        familiesMapForDisplay={familiesWithTraitsMap}
                        language={language}
                        traits={reportData.traits}
                    />
                ))}
                <ReportFooter key={1} isRtl={isRtl} candidateName={candidateName} page={1} totalPages={totalPages} />
            </Page>
            <Page size="A4" style={styles.page} key={2}>
                {secondPageFamilies.map((family) => (
                    <FamilySection
                        isRtl={isRtl}
                        key={family.code}
                        family={family}
                        familiesMapForDisplay={familiesWithTraitsMap}
                        language={language}
                        traits={reportData.traits}
                    />
                ))}
                {additionalQuestions.length > 0 && secondPageFamilies.length === 2 && (
                    <AdditionalQuestionsSection isRtl={isRtl} additionalQuestions={additionalQuestions} />
                )}
                {secondPageFamilies.length === 2 && additionalQuestions.length === 0 && (
                    <ReportExplanation isRtl={isRtl} />
                )}
                <ReportFooter key={2} isRtl={isRtl} candidateName={candidateName} page={2} totalPages={totalPages} />
            </Page>
            {secondPageFamilies.length > 2 && (
                <Page style={styles.page} key={3}>
                    {additionalQuestions.length > 0 && secondPageFamilies.length > 2 && (
                        <AdditionalQuestionsSection isRtl={isRtl} additionalQuestions={additionalQuestions} />
                    )}
                    <ReportExplanation isRtl={isRtl} />
                    <ReportFooter
                        key={3}
                        isRtl={isRtl}
                        candidateName={candidateName}
                        page={3}
                        totalPages={totalPages}
                    />
                </Page>
            )}
        </Document>
    );
};
