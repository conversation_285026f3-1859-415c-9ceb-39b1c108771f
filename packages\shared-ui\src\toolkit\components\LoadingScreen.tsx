import React from "react";
import { Box, CircularProgress, styled } from "@mui/material";

interface LoadingScreenProps {
    height?: string;
}

const LoadingContainer = styled(Box)(({ height }: { height?: string }) => ({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: height || "75dvh",
}));

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ height = "75dvh" }) => {
    return (
        <LoadingContainer height={height}>
            <CircularProgress />
        </LoadingContainer>
    );
};
