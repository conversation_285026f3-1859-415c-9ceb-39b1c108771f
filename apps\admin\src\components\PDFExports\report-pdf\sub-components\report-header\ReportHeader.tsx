import { Image, Text, View } from "@react-pdf/renderer";
import { FC } from "react";
import { stylesPDF as styles } from "../../../PDF.styles";
import { ReportHeaderReportInfo } from "./ReportHeaderReportInfo";

interface ReportHeaderProps {
    jobTitle: string;
    candidateName: string;
    customerName: string;
    interviewDate: string;
    candidateImage: string | null;
    candidateId: string;
    isRtl: boolean;
    email: string | null;
}

export const ReportHeader: FC<ReportHeaderProps> = ({
    isRtl,
    candidateName,
    customerName,
    interviewDate,
    candidateImage,
    candidateId,
    jobTitle,
    email,
}) => {
    const headerTitle = isRtl ? "דוח הערכה" : "Evaluation Report";
    const jobTitlePropertyName = isRtl ? "תפקיד" : "Job Title";
    const companyNamePropertyName = isRtl ? "חברה" : "Company";
    const emailPropertyName = isRtl ? "אימייל" : "Email";

    return (
        <View style={[styles.sectionContainer]}>
            <View
                style={[
                    styles.displayFlex,
                    styles.flewDirectionRow,
                    styles.spaceBetween,
                    styles.alignItemsCenter,
                    isRtl ? styles.flexReverse : styles.displayFlex,
                ]}
            >
                <View
                    style={[styles.displayFlex, styles.spaceBetween, styles.flexDirectionColumn, isRtl ? styles.alignEnd : {}, styles.flexGap4]}
                >
                    <Text style={[isRtl ? styles.hebrewTextSubTitle : styles.textSubTitle]}>{interviewDate}</Text>
                    <Text style={[isRtl ? styles.hebrewTextBoldMainHeader : styles.textBoldMainHeader, isRtl ? styles.flexReverse : {}]}>
                        {headerTitle} - {candidateName}
                    </Text>
                    <View style={[styles.flewDirectionRow, styles.displayFlex, isRtl ? styles.flexReverse : {}]}>
                        {email && (
                            <ReportHeaderReportInfo
                                paddingSide={isRtl ? "left" : "right"}
                                border={isRtl ? "left" : "right"}
                                isRtl={isRtl}
                                propertyName={emailPropertyName}
                                propertyValue={email}
                            />
                        )}
                        <ReportHeaderReportInfo
                            paddingSide='both'
                            border={isRtl ? "left" : "right"}
                            isRtl={isRtl}
                            propertyName={companyNamePropertyName}
                            propertyValue={customerName}
                        />
                        <ReportHeaderReportInfo
                            paddingSide='both'
                            border='none'
                            isRtl={isRtl}
                            propertyName={jobTitlePropertyName}
                            propertyValue={jobTitle}
                        />
                    </View>
                </View>
                {candidateImage && (
                    <View style={[styles.imageContainer]}>
                        <Image source={candidateImage} style={styles.image} />
                    </View>
                )}
            </View>
        </View>
    );
};
