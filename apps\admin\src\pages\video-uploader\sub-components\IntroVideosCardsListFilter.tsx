import { IntroVideo, IntroVideosResponse } from "@eva/shared-common";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { IconButton, SelectChangeEvent } from "@mui/material";
import { FC, useEffect, useState } from "react";
import { CustomSelect, CustomTextField, DataTableFilterWrapper } from "@eva/shared-ui";

interface IntrosCardsListFilterFields {
    searchText: string;
    customer: string;
}

interface QuestionsCardsListFilterProps {
    introVideosData: IntroVideosResponse;
    handleVideosChange: (filterFields: IntroVideo[]) => void;
}

export const IntroVideosCardsListFilter: FC<QuestionsCardsListFilterProps> = ({ introVideosData, handleVideosChange }) => {
    const [filteredCount, setFilteredCount] = useState<number>(introVideosData.videos.length);
    const [filterFields, setFilterFields] = useState<IntrosCardsListFilterFields>({
        searchText: "",
        customer: "",
    });

    const handleSearchChange = (event: React.ChangeEvent<{ value: string }>) => {
        setFilterFields((prev) => ({ ...prev, searchText: event.target.value }));
    };

    const handleCustomerChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, customer: event.target.value }));
    };

    const handleGenderChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, gender: event.target.value }));
    };

    const handleTraitChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, trait: event.target.value }));
    };

    const handleLanguageChange = (event: SelectChangeEvent) => {
        setFilterFields((prev) => ({ ...prev, language: event.target.value }));
    };

    const uniqueCustomers = [...new Set(introVideosData.videos.map((video) => video.customerName))];

    const customerOptions = [
        {
            value: "",
            label: "All",
        },
        ...uniqueCustomers.map((customer) => ({
            value: customer,
            label: customer,
        })),
    ];

    const handleReset = () => {
        setFilterFields({
            searchText: "",
            customer: "",
        });
    };

    useEffect(() => {
        const filteredVideos = introVideosData.videos.filter(
            (video) =>
                video.customerName.toLowerCase().includes(filterFields.customer.toLowerCase()) &&
                video.customerName.toLowerCase().includes(filterFields.searchText.toLowerCase())
        );
        handleVideosChange(filteredVideos);
        setFilteredCount(filteredVideos.length);
    }, [filterFields, introVideosData]);
    return (
        <DataTableFilterWrapper resultsCount={filteredCount}>
            <CustomTextField width={400} label='Search' value={filterFields.searchText} onChange={handleSearchChange} />
            <CustomSelect width={200} label='Customer' value={filterFields.customer} onChange={handleCustomerChange} options={customerOptions} />
            <IconButton onClick={handleReset}>
                <RestartAltIcon />
            </IconButton>
        </DataTableFilterWrapper>
    );
};
