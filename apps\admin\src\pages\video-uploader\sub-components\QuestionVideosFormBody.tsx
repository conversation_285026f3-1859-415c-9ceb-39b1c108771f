import {
    FamiliesWithQuestionsForBuilderResponse,
    CustomersResponse,
    LanguageVariation,
    QuestionOptions,
    QuestionsLanguageVariations,
} from "@eva/shared-common";
import { Stack, Button, Box, SelectChangeEvent, Divider, Alert, Paper, useTheme } from "@mui/material";
import { FC, useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { languageMapper, languages } from "../../../app-state/appText";
import {
    CustomSelect,
    CustomSelectOption,
    FileInput,
    PrimaryButton,
    H4,
    H5,
    Body1,
    Body2,
    Subtitle1,
} from "@eva/shared-ui";
import { Form, useFormikContext } from "formik";
import { QuestionVideosFormValues, QuestionVideoItem } from "./QuestionVideosForm";

interface QuestionVideosFormBodyProps {
    questionsData: FamiliesWithQuestionsForBuilderResponse;
    customersData: CustomersResponse;
    isSubmitting: boolean;
    uploadProgress: Record<string, number>;
    isLoading: boolean;
}

const determineQuestionOptionsByCustomerAndLanguage = (
    customerId: string,
    language: keyof QuestionsLanguageVariations,
    questionOptions: QuestionOptions
) => {
    if (questionOptions.options[customerId]) return questionOptions.options[customerId][language].male.text;
    else return questionOptions.options.default[language].male.text;
};

export const QuestionVideosFormBody: FC<QuestionVideosFormBodyProps> = ({
    questionsData,
    customersData,
    isSubmitting,
    uploadProgress,
    isLoading,
}) => {
    const { values, setFieldValue, errors, touched, submitForm } = useFormikContext<QuestionVideosFormValues>();
    const [error, setError] = useState<string | null>(null);
    const theme = useTheme();

    useEffect(() => {
        if (!values.language || !values.customerId) return;

        // Reset all question selections when common fields change
        setFieldValue(
            "questionVideos",
            values.questionVideos.map((item) => ({
                ...item,
                questionId: "", // Reset question selection
            }))
        );
    }, [values.language, values.customerId, values.gender, setFieldValue]);

    // Add a useEffect to validate that each selected question is still available
    useEffect(() => {
        if (!values.language || !values.customerId) return;

        // For each question item, validate that its questionId is still valid
        values.questionVideos.forEach((item) => {
            if (!item.questionId) return;

            // Get all available question IDs for this item
            const availableQuestionIds: string[] = [];
            questionsData.families.forEach((family) => {
                family.questions.forEach((question) => {
                    availableQuestionIds.push(question.question_id);
                });
            });

            // Get selected question IDs from other items
            const otherSelectedIds = values.questionVideos
                .filter((qv) => qv.id !== item.id && qv.questionId)
                .map((qv) => qv.questionId);

            // Check if this item's questionId is valid
            const isValid =
                availableQuestionIds.includes(item.questionId) && !otherSelectedIds.includes(item.questionId);

            // If not valid, clear it
            if (!isValid) {
                setFieldValue(
                    "questionVideos",
                    values.questionVideos.map((video) => (video.id === item.id ? { ...video, questionId: "" } : video))
                );
            }
        });
    }, [values.questionVideos, values.language, values.customerId, questionsData, setFieldValue]);

    const generateLanguageOptions = (): CustomSelectOption[] => {
        return languages.map((language) => ({
            value: language,
            label: languageMapper(language),
        }));
    };

    const generateCustomerOptions = (): CustomSelectOption[] => {
        return [
            {
                label: "All",
                value: "all",
            },
            ...customersData.customers.map((customer) => ({
                value: customer.id,
                label: customer.companyName,
            })),
        ];
    };

    const generateGenderOptions = (): CustomSelectOption[] => {
        return [
            {
                value: "all",
                label: "All",
            },
            {
                value: "male",
                label: "Male",
            },
            {
                value: "female",
                label: "Female",
            },
        ];
    };

    const generateQuestionOptions = (itemId?: string): CustomSelectOption[] => {
        if (!values.customerId || !values.language) return [];

        // Get all selected question IDs except for the current item being viewed
        const selectedQuestionIds = values.questionVideos
            .filter((qv) => qv.questionId && (!itemId || qv.id !== itemId))
            .map((qv) => qv.questionId);

        return questionsData.families.reduce((acc, family) => {
            const familyQuestions = family.questions
                .filter((q) => !selectedQuestionIds.includes(q.question_id)) // Filter out questions already selected
                .map((question) => ({
                    value: question.question_id,
                    label: `${
                        question.trait[values.language as keyof LanguageVariation]
                    } - ${determineQuestionOptionsByCustomerAndLanguage(
                        values.customerId,
                        values.language as keyof QuestionsLanguageVariations,
                        question.question
                    )}`,
                }));
            return [...acc, ...familyQuestions];
        }, [] as CustomSelectOption[]);
    };

    const handleLanguageChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("language", event.target.value);
        if (event.target.value !== "he") {
            setFieldValue("gender", "all");
        }
    };

    const handleCustomerChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("customerId", event.target.value);
    };

    const handleGenderChange = (event: SelectChangeEvent<string>) => {
        setFieldValue("gender", event.target.value);
    };

    const handleQuestionChange = (id: string, event: SelectChangeEvent<string>) => {
        setFieldValue(
            "questionVideos",
            values.questionVideos.map((item) => (item.id === id ? { ...item, questionId: event.target.value } : item))
        );
    };

    const handleFileSelect = (id: string, file: File | null) => {
        // Check if this file is already selected for another question
        if (file) {
            const isDuplicate = values.questionVideos.some(
                (item) => item.id !== id && item.file && item.file.name === file.name
            );

            if (isDuplicate) {
                setError(`File "${file.name}" is already selected for another question.`);
                return;
            }
        }

        setError(null);
        setFieldValue(
            "questionVideos",
            values.questionVideos.map((item) => (item.id === id ? { ...item, file } : item))
        );
    };

    const addMoreQuestion = () => {
        setFieldValue("questionVideos", [
            ...values.questionVideos,
            {
                id: uuidv4(),
                questionId: "",
                file: null,
            },
        ]);
    };

    const removeQuestion = (id: string) => {
        if (values.questionVideos.length > 1) {
            setFieldValue(
                "questionVideos",
                values.questionVideos.filter((item) => item.id !== id)
            );
        }
    };

    const isQuestionItemValid = (item: QuestionVideoItem): boolean => {
        return !!item.questionId && !!item.file;
    };

    const areAllQuestionsValid = (): boolean => {
        return (
            !!values.language &&
            !!values.customerId &&
            (values.language !== "he" || !!values.gender) &&
            values.questionVideos.every(isQuestionItemValid) &&
            values.questionVideos.length > 0
        );
    };

    return (
        <Form>
            <Stack gap={4}>
                {error && <Alert severity="error">{error}</Alert>}

                {/* Common fields that apply to all videos */}
                <Paper elevation={1} sx={{ padding: 3 }}>
                    <Stack gap={3}>
                        <H5>Common Settings</H5>

                        <Stack direction="row" gap={2}>
                            <CustomSelect
                                label="Language"
                                options={generateLanguageOptions()}
                                value={values.language}
                                onChange={handleLanguageChange}
                                mapper={languageMapper}
                                disabled={isSubmitting}
                            />

                            <CustomSelect
                                label="Customer"
                                options={generateCustomerOptions()}
                                value={values.customerId}
                                onChange={handleCustomerChange}
                                disabled={isSubmitting}
                            />

                            {values.language === "he" && (
                                <CustomSelect
                                    label="Gender"
                                    options={generateGenderOptions()}
                                    value={values.gender}
                                    onChange={handleGenderChange}
                                    disabled={isSubmitting}
                                />
                            )}
                        </Stack>

                        {touched.language && errors.language && <Body2 color="error">{errors.language}</Body2>}
                        {touched.customerId && errors.customerId && <Body2 color="error">{errors.customerId}</Body2>}
                        {touched.gender && errors.gender && <Body2 color="error">{errors.gender}</Body2>}
                    </Stack>
                </Paper>

                {/* Question-specific fields */}
                {values.questionVideos.map((item, index) => (
                    <Paper key={item.id} elevation={1} sx={{ padding: 3 }}>
                        {index > 0 && <Divider sx={{ mb: 3 }} />}
                        <Stack gap={3}>
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                                <H4>Question {index + 1}</H4>
                                {values.questionVideos.length > 1 && (
                                    <Button
                                        variant="outlined"
                                        color="error"
                                        startIcon={<DeleteIcon />}
                                        onClick={() => removeQuestion(item.id)}
                                        disabled={isSubmitting}
                                    >
                                        Remove
                                    </Button>
                                )}
                            </Stack>

                            <CustomSelect
                                width={900}
                                label="Question"
                                options={generateQuestionOptions(item.id)}
                                value={item.questionId || ""}
                                onChange={(e) => handleQuestionChange(item.id, e)}
                                disabled={
                                    !values.language ||
                                    !values.customerId ||
                                    (values.language === "he" && !values.gender) ||
                                    isSubmitting
                                }
                            />

                            <Stack direction="row" gap={2} alignItems="center">
                                <FileInput
                                    onFileSelect={(file) => handleFileSelect(item.id, file)}
                                    buttonLabel="Choose Video"
                                    disabled={isSubmitting}
                                />

                                {item.file && (
                                    <Stack direction="row" alignItems="center" spacing={1}>
                                        <Body2 color="textSecondary">{item.file.name}</Body2>

                                        {uploadProgress[item.id] > 0 && uploadProgress[item.id] < 100 && (
                                            <Box sx={{ fontWeight: "bold", color: theme.palette.primary.main }}>
                                                <Body2 color="primary">{Math.round(uploadProgress[item.id])}%</Body2>
                                            </Box>
                                        )}

                                        {uploadProgress[item.id] === 100 && (
                                            <CheckCircleIcon color="success" fontSize="small" sx={{ ml: 1 }} />
                                        )}
                                    </Stack>
                                )}
                            </Stack>

                            {touched.questionVideos &&
                                errors.questionVideos &&
                                Array.isArray(errors.questionVideos) &&
                                errors.questionVideos[index] && (
                                    <Body2 color="error" textAlign="left">
                                        {(() => {
                                            const errorItem = errors.questionVideos[index];
                                            if (typeof errorItem === 'string') {
                                                return errorItem;
                                            } else if (typeof errorItem === 'object' && errorItem !== null) {
                                                // Display the first error message found (either questionId or file)
                                                return errorItem.questionId || errorItem.file || ''; 
                                            }
                                            return '';
                                        })()}
                                    </Body2>
                                )}
                        </Stack>
                    </Paper>
                ))}

                <Stack direction="row" gap={2} justifyContent="space-between" sx={{ mt: 2 }}>
                    <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={addMoreQuestion}
                        disabled={isSubmitting}
                        sx={{
                            color: theme.palette.primary.main,
                            borderColor: theme.palette.primary.main,
                        }}
                    >
                        Add More Question
                    </Button>

                    <PrimaryButton
                        content={isSubmitting ? "Uploading..." : "Upload All Videos"}
                        onClick={submitForm}
                        disabled={!areAllQuestionsValid() || isSubmitting || isLoading}
                    />
                </Stack>
            </Stack>
        </Form>
    );
};
