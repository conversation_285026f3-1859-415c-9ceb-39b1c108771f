import * as AWS from "@aws-sdk/client-sns";
export async function sendSNSMessage(args: { subject: string; message: string; topicArn: string; region: string }): Promise<void> {
    const { message, topicArn, region , subject } = args;
    const sns = new AWS.SNS({ region });
    try {
        await sns.publish({
            Message: message,
            TopicArn: topicArn,
            Subject: subject,
            MessageStructure: "string",
        });
        console.log(`Message sent to the topic ${topicArn}`);
    } catch (error) {
        console.error(`Error sending message to the topic ${topicArn}:`, error);
        throw error;
    }
}
