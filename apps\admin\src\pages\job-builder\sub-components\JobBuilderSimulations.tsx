import { LanguageVariation } from "@eva/shared-common";
import { LoadingScreen, useAppText, useAppTheme, H4, Body2 } from "@eva/shared-ui";
import { Card, CardContent, Divider, Stack, styled } from "@mui/material";
import { useField } from "formik";
import { FC } from "react";
import { useGetSimulationsQuery } from "../../../app-state/apis/jobsApi";

const SimulationCardsContainer = styled(Stack)({
    spacing: 4
});

const SimulationCardsGrid = styled(Stack)({
    flexDirection: "row",
    gap: 4,
    flexWrap: "wrap"
});

const SimulationCard = styled(Card, {
    shouldForwardProp: (prop) => prop !== "isSelected" && prop !== "isDisabled"
})<{ isSelected: boolean; isDisabled?: boolean }>(({ theme, isSelected, isDisabled }) => ({
    width: "calc(50% - 16px)",
    minWidth: 300,
    cursor: isDisabled ? "default" : "pointer",
    opacity: isDisabled ? 0.7 : 1,
    backgroundColor: isSelected ? `${theme.palette.primary.main}50` : theme.palette.background.paper,
    transition: "background-color 0.3s ease",
    "&:hover": {
        backgroundColor: isDisabled
            ? undefined
            : isSelected
            ? `${theme.palette.primary.light}70`
            : theme.palette.action.hover,
    },
}));

const SectionDivider = styled(Divider)({
    marginTop: 16,
    marginBottom: 16
});

const QuestionsContainer = styled(Stack)({
    spacing: 1
});

export const JobBuilderSimulations: FC<{ disabled?: boolean }> = ({ disabled }) => {
    const { getContentForDisplay } = useAppText();
    const { currentDirection } = useAppTheme();
    const { data: simulations, isLoading } = useGetSimulationsQuery();
    const [field, , helpers] = useField<string[]>("simulations");
    const { currentLanguage } = useAppText();
    const selectedSimulations = field.value || [];

    const handleSimulationToggle = (simulationId: string) => {
        if (disabled) return;

        const newSelected = selectedSimulations.includes(simulationId)
            ? selectedSimulations.filter((id: string) => id !== simulationId)
            : [...selectedSimulations, simulationId];
        helpers.setValue(newSelected);
    };

    if (isLoading) return <LoadingScreen height="40dvh" />;
    if (!simulations?.simulations?.length) return <></>

    return (
        <SimulationCardsContainer>
            <SimulationCardsGrid>
                {simulations.simulations.map((simulation) => {
                    const isSelected = selectedSimulations.includes(simulation.id);
                    const storyText = simulation.story[currentLanguage].male.text;

                    return (
                        <SimulationCard
                            key={simulation.id}
                            isSelected={isSelected}
                            isDisabled={disabled}
                            onClick={() => handleSimulationToggle(simulation.id)}
                        >
                            <CardContent>
                                <H4 dir={currentDirection}>
                                    {getContentForDisplay("jobBuilderForm.simulations.story")}
                                </H4>
                                <Body2 dir={currentDirection}>{storyText}</Body2>

                                <SectionDivider />

                                <QuestionsContainer>
                                    <H4 dir={currentDirection}>
                                        {getContentForDisplay("jobBuilderForm.simulations.questions")}
                                    </H4>
                                    {simulation.questions.map((question: LanguageVariation, index: number) => (
                                        <Body2 dir={currentDirection} key={index}>
                                            {index + 1}. {question[currentLanguage]}
                                        </Body2>
                                    ))}
                                </QuestionsContainer>
                            </CardContent>
                        </SimulationCard>
                    );
                })}
            </SimulationCardsGrid>
        </SimulationCardsContainer>
    );
};
